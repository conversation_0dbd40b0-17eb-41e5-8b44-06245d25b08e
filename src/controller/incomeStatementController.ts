import {consoleLog} from "@/lib/utils";
import {IncomeStatementRepository} from "../repositories/implements/IncomeStatementRepository";
import {LogsController} from "./logsController";
import {LogLevel} from "@/utils/types/logs/log";
import {addLogJobExecution} from "@/lib/agenda";

export async function incomeStatementParseData(ticker_internal_id: number, file: any) {
    try {
        const incomeStatementRepository = new IncomeStatementRepository(ticker_internal_id);

        await incomeStatementRepository.getSavedYearlyIncomeStatement();
        await incomeStatementRepository.getSavedQuarterlyIncomeStatement();

        const incomeStatement = file || {};

        const yearly = incomeStatement?.yearly || {};
        const quarterly = incomeStatement?.quarterly || {};

        const incomeStatementYearly = (await incomeStatementRepository.getYearlyIncomeStatement(yearly)) || [];
        const incomeStatementQuarterly = (await incomeStatementRepository.getQuarterlyIncomeStatement(quarterly)) || [];

        await incomeStatementRepository.saveYearlyIncomeStatement();
        await incomeStatementRepository.saveQuarterlyIncomeStatement();

        // Calculate quantities
        const quantity_of_income_statement_year = incomeStatementYearly.length;
        const quantity_of_income_statement_quarter = incomeStatementQuarterly.length;

        // Calculate date ranges for yearly data
        let start_of_income_statement_year = null;
        let end_of_income_statement_year = null;

        if (incomeStatementYearly.length > 0) {
            // First record date (earliest)
            start_of_income_statement_year = new Date(incomeStatementYearly[incomeStatementYearly.length - 1]?.document_date) || null;
            // Last record date (latest)
            end_of_income_statement_year = new Date(incomeStatementYearly[0]?.document_date) || null;
        }

        // Calculate date ranges for quarterly data
        let start_of_income_statement_quarter = null;
        let end_of_income_statement_quarter = null;

        if (incomeStatementQuarterly.length > 0) {
            // First record date (earliest)
            start_of_income_statement_quarter = new Date(incomeStatementQuarterly[incomeStatementQuarterly.length - 1]?.document_date) || null;
            // Last record date (latest)
            end_of_income_statement_quarter = new Date(incomeStatementQuarterly[0]?.document_date) || null;
        }

        return {
            quantity_of_income_statement_year,
            quantity_of_income_statement_quarter,
            start_of_income_statement_year,
            end_of_income_statement_year,
            start_of_income_statement_quarter,
            end_of_income_statement_quarter,
        };
    } catch (error: any) {
        consoleLog(`Error when try to parse income statement ${error}`, incomeStatementParseData.name, LogLevel.ERROR);
        addLogJobExecution(
            LogLevel.ERROR,
            incomeStatementParseData.name,
            "Error when try to parse income statement",
            {error: error instanceof Error ? error.message : String(error)},
            ticker_internal_id,
        );
        await LogsController.saveError(ticker_internal_id, error.message);
    }
}

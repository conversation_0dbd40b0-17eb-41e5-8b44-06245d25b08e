import {ListOfTickers} from "../entities/consolidated_data/ListOfTickers";
import {HistoricalPriceRepository} from "../repositories/implements/HistoricalPriceRepository";
import {addLogJobExecution} from "@/lib/agenda";
import {LogLevel} from "@/utils/types/logs/log";
import {consoleLog} from "@/lib/utils";
import {initializeJobProgress, updateJobProgress} from "../lib/jobDefinitions";
import {ObjectId} from "mongodb";

/**
 * Gets price history data for specified tickers and saves to HistoricalPrices table
 * Implements delta logic - first run gets all history, subsequent runs get only new data
 * @param updatedTickers - Array of tickers to process
 * @param historyId - Optional job history ID for progress tracking
 * @returns Status message indicating successful processing or error message
 */
export async function getPriceHistorySaveToDatabase(updatedTickers: ListOfTickers[], historyId?: string) {
    try {
        const historyObjectId = historyId ? new ObjectId(historyId) : undefined;

        addLogJobExecution(LogLevel.INFO, "getPriceHistorySaveToDatabase", "Starting price history processing", {
            tickersLength: updatedTickers.length,
        });

        if (updatedTickers.length === 0) {
            addLogJobExecution(LogLevel.INFO, "getPriceHistorySaveToDatabase", "No tickers to process", {});
            return "No tickers to process";
        }

        if (historyObjectId) {
            await initializeJobProgress(historyObjectId, updatedTickers.length);
        }

        const repository = new HistoricalPriceRepository();
        let processedCount = 0;
        let successCount = 0;
        let errorCount = 0;

        // Process tickers in batches for better performance
        const batchSize = 10; // Process 10 tickers at a time
        for (let i = 0; i < updatedTickers.length; i += batchSize) {
            const batch = updatedTickers.slice(i, i + batchSize);

            for (const ticker of batch) {
                try {
                    consoleLog(`Processing price history for ticker: ${ticker.primary_ticker_eodhd}`, "getPriceHistorySaveToDatabase", LogLevel.DEBUG);

                    await repository.getPriceHistoryAndSave(ticker);
                    successCount++;

                    addLogJobExecution(LogLevel.DEBUG, "getPriceHistorySaveToDatabase", "Successfully processed ticker", {
                        ticker: ticker.primary_ticker_eodhd,
                        tickerId: ticker.id,
                    });
                } catch (error) {
                    errorCount++;
                    const errorMessage = error instanceof Error ? error.message : String(error);
                    consoleLog(`Error processing ticker ${ticker.primary_ticker_eodhd}: ${errorMessage}`, "getPriceHistorySaveToDatabase", LogLevel.ERROR);

                    addLogJobExecution(LogLevel.ERROR, "getPriceHistorySaveToDatabase", "Error processing ticker", {
                        ticker: ticker.primary_ticker_eodhd,
                        tickerId: ticker.id,
                        error: errorMessage,
                    });
                }

                processedCount++;

                // Update progress every minute (approximately every 60 tickers processed)
                if (historyObjectId && processedCount % 60 === 0) {
                    await updateJobProgress(historyObjectId, processedCount);
                }
            }

            // Small delay between batches to avoid overwhelming the API
            await new Promise((resolve) => setTimeout(resolve, 1000));
        }

        // Final progress update
        if (historyObjectId) {
            await updateJobProgress(historyObjectId, processedCount);
        }

        const message = `Processed ${processedCount} tickers: ${successCount} successful, ${errorCount} errors`;
        addLogJobExecution(LogLevel.INFO, "getPriceHistorySaveToDatabase", "Completed price history processing", {
            totalProcessed: processedCount,
            successful: successCount,
            errors: errorCount,
        });

        consoleLog(message, "getPriceHistorySaveToDatabase", LogLevel.INFO);
        return message;
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        consoleLog(`Error in getPriceHistorySaveToDatabase: ${errorMessage}`, "getPriceHistorySaveToDatabase", LogLevel.ERROR);
        addLogJobExecution(LogLevel.ERROR, "getPriceHistorySaveToDatabase", "Error in price history processing", {
            error: errorMessage,
        });
        throw error;
    }
}

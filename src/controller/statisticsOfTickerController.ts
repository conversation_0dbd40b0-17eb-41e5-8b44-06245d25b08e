import {ListOfTickers} from "@/entities/consolidated_data/ListOfTickers";
import {StatisticsOfTickerRepository} from "../repositories/implements/StatisticsOfTickerRepository";

export async function getTickersStatisticsAndCalculate(tickers: ListOfTickers[], historyId?: string) {
    const statisticsRepository = new StatisticsOfTickerRepository();
    await statisticsRepository.getTickersStatisticsAndCalculate(tickers, historyId);
}

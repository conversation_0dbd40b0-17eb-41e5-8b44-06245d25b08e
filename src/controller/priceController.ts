import {RealTimePriceEODHD} from "@/repositories/implements/RealTimePriceDAO";
import {ListOfTickers} from "../entities/consolidated_data/ListOfTickers";
import {StatisticsOfTicker} from "../entities/consolidated_data/StatisticsOfTickers";
import {PriceTickerRepository} from "../repositories/implements/PriceTickerRepository";
import {addLogJobExecution} from "@/lib/agenda";
import {LogLevel} from "@/utils/types/logs/log";
import {ReasonNotEnable} from "@/utils/types/EODHD/exchange_countries";
import {consoleLog} from "@/lib/utils";
import {log} from "console";
import {LogsController} from "./logsController";

export async function getPrices(updatedTickers: ListOfTickers[], historyId?: string) {
    const repository = new PriceTickerRepository();
    return await repository.getPrice(updatedTickers, historyId);
}

export async function setPrices(tickersPrice: RealTimePriceEODHD[]) {
    const repository = new PriceTickerRepository();
    // Use the new batch method for better performance
    await repository.setPrices(tickersPrice);
}

export async function setInvalidPrices(tickersPrice: RealTimePriceEODHD[]) {
    try {
        addLogJobExecution(LogLevel.INFO, setInvalidPrices.name, "Disabling ticker and deleting statistics", {tickersPriceLength: tickersPrice.length});
        for (const tickerPrice of tickersPrice) {
            const {ticker_internal_id, code} = tickerPrice;
            const symbol_code = code.trim();
            consoleLog(`Disabling ticker ${symbol_code}/${ticker_internal_id} and deleting statistics`, setInvalidPrices.name, LogLevel.DEBUG);
            let ticker: ListOfTickers | null = null;
            if (!ticker_internal_id && ticker_internal_id !== null) {
                ticker = await ListOfTickers.findOne({
                    where: {
                        id: ticker_internal_id,
                    },
                });
            } else {
                ticker = await ListOfTickers.findOne({
                    where: {
                        primary_ticker_eodhd: symbol_code,
                    },
                });
            }
            if (ticker) {
                await ticker.update({is_enable: 0, reason_not_enable: ReasonNotEnable.PRICE_NOT_FOUND});
                await StatisticsOfTicker.destroy({
                    where: {
                        ticker_internal_id: ticker.id,
                    },
                });
                consoleLog(`Ticker ${symbol_code}/${ticker.id} disabled and statistics deleted`, setInvalidPrices.name, LogLevel.ERROR);
                addLogJobExecution(LogLevel.ERROR, setInvalidPrices.name, "Ticker disabled and statistics deleted", {}, ticker.id);
                await LogsController.saveError(ticker.id || 0, `Price not found for ticker ${symbol_code}`);
            }
        }
    } catch (error: any) {
        consoleLog(`Error when try to set invalid Price ${error}`, setInvalidPrices.name, LogLevel.ERROR);
        addLogJobExecution(LogLevel.ERROR, setInvalidPrices.name, "Error when try to set invalid Price", {error: error instanceof Error ? error.message : String(error)});
    }
}

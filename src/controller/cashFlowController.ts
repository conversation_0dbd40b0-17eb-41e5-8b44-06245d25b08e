import {consoleLog} from "@/lib/utils";
import {CashFlowRepository} from "../repositories/implements/CashFlowRepository";
import {LogsController} from "./logsController";
import {addLogJobExecution} from "@/lib/agenda";
import {LogLevel} from "@/utils/types/logs/log";

export async function cashFlowParseData(ticker_internal_id: number, file: any) {
    try {
        const cashFlowRepository = new CashFlowRepository(ticker_internal_id);

        await cashFlowRepository.getSavedYearlyCashFlow();
        await cashFlowRepository.getSavedQuarterlyCashFlow();

        const cashFlow = file || {};

        const yearly = cashFlow?.yearly || {};
        const quarterly = cashFlow?.quarterly || {};

        const cashFlowYearly = (await cashFlowRepository.getYearlyCashFlow(yearly)) || [];
        const cashFlowQuarterly = (await cashFlowRepository.getQuarterlyCashFlow(quarterly)) || [];

        await cashFlowRepository.saveYearlyCashFlow();
        await cashFlowRepository.saveQuarterlyCashFlow();

        // Calculate quantities
        const quantity_of_cash_flow_year = cashFlowYearly.length;
        const quantity_of_cash_flow_quarter = cashFlowQuarterly.length;

        // Calculate date ranges for yearly data
        let start_of_cash_flow_year = null;
        let end_of_cash_flow_year = null;

        if (cashFlowYearly.length > 0) {
            // First record date (earliest)
            start_of_cash_flow_year = new Date(cashFlowYearly[cashFlowYearly.length - 1]?.document_date) || null;
            // Last record date (latest)
            end_of_cash_flow_year = new Date(cashFlowYearly[0]?.document_date) || null;
        }

        // Calculate date ranges for quarterly data
        let start_of_cash_flow_quarter = null;
        let end_of_cash_flow_quarter = null;

        if (cashFlowQuarterly.length > 0) {
            // First record date (earliest)
            start_of_cash_flow_quarter = new Date(cashFlowQuarterly[cashFlowQuarterly.length - 1]?.document_date) || null;
            // Last record date (latest)
            end_of_cash_flow_quarter = new Date(cashFlowQuarterly[0]?.document_date) || null;
        }

        return {
            quantity_of_cash_flow_year,
            quantity_of_cash_flow_quarter,
            start_of_cash_flow_year,
            end_of_cash_flow_year,
            start_of_cash_flow_quarter,
            end_of_cash_flow_quarter,
        };
    } catch (error: any) {
        consoleLog(`Error when try to parse cash flow ${error}`, cashFlowParseData.name, LogLevel.ERROR);
        addLogJobExecution(LogLevel.ERROR, cashFlowParseData.name, "Error when try to parse cash flow", {error: error instanceof Error ? error.message : String(error)}, ticker_internal_id);
        await LogsController.saveError(ticker_internal_id, error);
    }
}

// Job details type definition
export type JobDetails = {
    _id?: string;

    name: string;
    priority: string | number;
    executionOrder?: number;
    nextRunAt: Date | null;
    /**
     * normal: job is queued and will be processed (regular case when the user adds a new job)
     * single: job with this name is only queued once, if there is an exisitn gentry in the database, the job is just updated, but not newly inserted (this is used for .every())
     */
    type: "normal" | "single";
    data: any;

    lockedAt?: Date;
    lastFinishedAt?: Date;
    failedAt?: Date;
    failCount?: number;
    failReason?: string;
    repeatTimezone?: string;
    lastRunAt?: Date;
    repeatInterval?: string | number;
    repeatAt?: string;
    disabled?: boolean;
    progress?: number;

    // unique query object
    uniqueOpts?: {
        insertOnly: boolean;
    };

    lastModifiedBy?: string;
};

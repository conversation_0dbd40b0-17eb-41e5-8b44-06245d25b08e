import { clsx, type ClassValue } from "clsx";
import crypto from 'crypto';
import { toast } from 'react-toastify';
import { twMerge } from "tailwind-merge";

export function formatNumber(value: number, lang = "en") {
  if (value === null || value === undefined) return "-"

  // Handle special cases for percentages and ratios
  if (typeof value === "number") {
    // For percentage values (margins, ROE, ROA, etc.)
    if (
      /margin|roe|roa|roic|ratio|equity_ratio|assets_ratio|turnover|payout/.test(String(value)) &&
      value > 0 &&
      value < 10
    ) {
      const formatter = new Intl.NumberFormat(lang, {
        style: "percent",
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      })
      return formatter.format(value)
    }

    // For large values (market cap, revenue, etc.)
    if (value > 1000000) {
      const formatter = new Intl.NumberFormat(lang, {
        notation: "compact",
        compactDisplay: "short",
        maximumFractionDigits: 2,
      })
      return formatter.format(value)
    }
  }

  // Default formatting
  const formatter = new Intl.NumberFormat(lang, {
    maximumFractionDigits: 2,
    minimumFractionDigits: 2,
  })
  return formatter.format(value)
}

export function sortByYear(data: any[], year: string, field: string, direction: "asc" | "desc" = "asc") {
  return [...data].sort((a, b) => {
    const yearIndexA = a.findIndex((item: any) => item.date === year)
    const yearIndexB = b.findIndex((item: any) => item.date === year)

    if (yearIndexA === -1 || yearIndexB === -1) return 0

    const valueA = a[yearIndexA][field] || 0
    const valueB = b[yearIndexB][field] || 0

    return direction === "asc" ? valueA - valueB : valueB - valueA
  })
}


export const format2Digits = (num) =>
  parseFloat(num) > 1 ? parseFloat(num.toFixed(2)) : 0;

export const encrypt = (text: string) => {
  const algorithm = 'aes-256-cbc';
  const key = crypto.randomBytes(32);
  const iv = crypto.randomBytes(16);

  const cipher = crypto.createCipheriv(algorithm, key, iv);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');

  return encrypted;
}

export const decrypt = (encrypted: string) => {
  const algorithm = 'aes-256-cbc';
  const key = crypto.randomBytes(32);
  const iv = crypto.randomBytes(16);

  const decipher = crypto.createDecipheriv(algorithm, key, iv);
  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');

  return decrypted;
}

export const errorMessage = (message) => toast.error(message, {
  position: "top-right"
})

export const successMessage = (message) => toast.success(message, {
  position: "top-right"
})

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

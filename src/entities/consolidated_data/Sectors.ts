import {DataTypes, Model, Optional} from "sequelize";
import {sequelize as conn} from "../../lib/database";
import {iSector} from "./interfaces/iSector";

export interface SectorInput extends Optional<iSector, "id" | "created_at" | "updated_at" | "created_by_id" | "updated_by_id" | "en" | "fr" | "pt"> {}

export class Sector extends Model<iSector, SectorInput> implements iSector {
    declare id?: number;
    declare created_at?: Date;
    declare updated_at?: Date;
    declare created_by_id: number;
    declare updated_by_id: number;
    declare sector_id: number;
    declare en: string;
    declare fr: string;
    declare pt: string;
}

export const sectors = Sector.init(
    {
        id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            primaryKey: true,
        },
        created_at: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        updated_at: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        created_by_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        updated_by_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        sector_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        en: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        fr: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        pt: {
            type: DataTypes.STRING,
            allowNull: false,
        },
    },
    {
        timestamps: false,
        sequelize: conn,
        tableName: "sectors",
        hooks: {
            beforeCreate: (record) => {
                record.created_at = new Date();
                record.updated_at = new Date();
                record.created_by_id = 1;
                record.updated_by_id = 1;
            },
            beforeUpdate: (record) => {
                record.updated_at = new Date();
                record.updated_by_id = 1;
            },
        },
    },
);

import {Model, Optional, DataTypes} from "sequelize";
import {sequelize as conn} from "../../lib/database";
import {iStatisticsOfFilters} from "./interfaces/iStatisticsOfFilters";

type StatisticsOfFiltersCreationAttributes = Optional<iStatisticsOfFilters, "id" | "created_at" | "updated_at" | "created_by_id" | "updated_by_id" | "step">;

class StatisticsOfFilters extends Model<iStatisticsOfFilters, StatisticsOfFiltersCreationAttributes> {
    declare id?: number;
    declare indicator_name: string;
    declare created_at?: Date;
    declare updated_at?: Date;
    declare created_by_id: number;
    declare updated_by_id: number;
    declare min: number;
    declare max: number;
    declare step?: number;
}

StatisticsOfFilters.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        indicator_name: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        created_at: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        updated_at: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        created_by_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        updated_by_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        min: {
            type: DataTypes.DECIMAL(18, 5),
            allowNull: false,
        },
        max: {
            type: DataTypes.DECIMAL(18, 5),
            allowNull: false,
        },
        step: {
            type: DataTypes.DECIMAL(18, 5),
            allowNull: true,
        },
    },
    {
        timestamps: false,
        sequelize: conn,
        tableName: "statistics_of_filter",
        hooks: {
            beforeCreate: (record) => {
                record.created_at = new Date();
                record.updated_at = new Date();
                record.created_by_id = 1;
                record.updated_by_id = 1;
            },
            beforeUpdate: (record) => {
                record.updated_at = new Date();
                record.updated_by_id = 1;
            },
        },
    },
);

export {StatisticsOfFilters};

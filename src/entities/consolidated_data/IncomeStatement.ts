import {Model, Optional, DataTypes} from "sequelize";
import {sequelize as conn} from "../../lib/database";
import {Document_type_year_or_quarter} from "./DocumentTypeYearorQuarterly";
import {iIncomeStatement} from "./interfaces/iIncomeStatement";

type IncomeStatementcreationAttributes = Optional<
    iIncomeStatement,
    | "id"
    | "ticker_internal_id"
    | "created_at"
    | "updated_at"
    | "created_by_id"
    | "updated_by_id"
    | "document_current"
    | "document_type_year_or_quarter"
    | "document_date"
    | "currency_symbol"
    | "research_development"
    | "effect_of_accounting_charges"
    | "income_before_tax"
    | "minority_interest"
    | "net_income"
    | "selling_general_administrative"
    | "selling_and_marketing_expenses"
    | "gross_profit"
    | "reconciled_depreciation"
    | "ebit"
    | "ebitda"
    | "depreciation_and_amortization"
    | "non_operating_income_net_other"
    | "operating_income"
    | "other_operating_expenses"
    | "interest_expense"
    | "tax_provision"
    | "interest_income"
    | "net_interest_income"
    | "extraordinary_items"
    | "non_recurring"
    | "other_items"
    | "income_tax_expense"
    | "total_revenue"
    | "total_operating_expenses"
    | "cost_of_revenue"
    | "total_other_income_expense_net"
    | "discontinued_operations"
    | "net_income_from_continuing_ops"
    | "net_income_applicable_to_common_shares"
    | "preferred_stock_and_other_adjustments"
    | "eps_diluted_current"
    | "eps_diluted_last_date"
>;

class IncomeStatement extends Model<iIncomeStatement, IncomeStatementcreationAttributes> {
    declare id?: number;
    declare ticker_internal_id: number;
    declare created_at: Date;
    declare updated_at: Date;
    declare created_by_id: number;
    declare updated_by_id: number;
    declare document_current: number;
    declare document_type_year_or_quarter: Document_type_year_or_quarter;
    declare document_date: string;
    declare currency_symbol: string;
    declare research_development: number;
    declare effect_of_accounting_charges: number;
    declare income_before_tax: number;
    declare minority_interest: number;
    declare net_income: number;
    declare selling_general_administrative: number;
    declare selling_and_marketing_expenses: number;
    declare gross_profit: number;
    declare reconciled_depreciation: number;
    declare ebit: number;
    declare ebitda: number;
    declare depreciation_and_amortization: number;
    declare non_operating_income_net_other: number;
    declare operating_income: number;
    declare other_operating_expenses: number;
    declare interest_expense: number;
    declare tax_provision: number;
    declare interest_income: number;
    declare net_interest_income: number;
    declare extraordinary_items: number;
    declare non_recurring: number;
    declare other_items: number;
    declare income_tax_expense: number;
    declare total_revenue: number;
    declare total_operating_expenses: number;
    declare cost_of_revenue: number;
    declare total_other_income_expense_net: number;
    declare discontinued_operations: number;
    declare net_income_from_continuing_ops: number;
    declare net_income_applicable_to_common_shares: number;
    declare preferred_stock_and_other_adjustments: number;
    declare eps_diluted_current: number;
    declare eps_diluted_last_date: number;

    constructor(props?: Partial<iIncomeStatement>, document_current?: number) {
        super(props);
        Object.assign(this, props);
        if (!document_current) {
            this.document_current = 0;
        }
    }
}

IncomeStatement.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        ticker_internal_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        created_at: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        updated_at: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        created_by_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        updated_by_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        document_current: {
            type: DataTypes.TINYINT,
            allowNull: false,
        },
        document_type_year_or_quarter: {
            type: DataTypes.ENUM("y", "q"),
            allowNull: false,
        },
        document_date: {
            type: DataTypes.DATEONLY,
            allowNull: true,
        },
        currency_symbol: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        research_development: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        effect_of_accounting_charges: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        income_before_tax: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        minority_interest: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        net_income: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        selling_general_administrative: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        selling_and_marketing_expenses: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        gross_profit: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        reconciled_depreciation: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        ebit: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        ebitda: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        depreciation_and_amortization: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        non_operating_income_net_other: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        operating_income: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        other_operating_expenses: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        interest_expense: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        tax_provision: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        interest_income: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        net_interest_income: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        extraordinary_items: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        non_recurring: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        other_items: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        income_tax_expense: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        total_revenue: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        total_operating_expenses: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        cost_of_revenue: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        total_other_income_expense_net: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        discontinued_operations: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        net_income_from_continuing_ops: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        net_income_applicable_to_common_shares: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        preferred_stock_and_other_adjustments: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        eps_diluted_current: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        eps_diluted_last_date: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
    },
    {
        timestamps: false,
        sequelize: conn,
        tableName: "fundamentals_income_statement",
        hooks: {
            beforeCreate: (record, _options) => {
                record.created_at = new Date();
                record.updated_at = new Date();
                record.created_by_id = 1;
                record.updated_by_id = 1;
            },
            beforeUpdate: (record, _options) => {
                record.updated_at = new Date();
                record.updated_by_id = 1;
            },
        },
    },
);

export {IncomeStatement};

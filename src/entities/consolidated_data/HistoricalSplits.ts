import {Model, Optional, DataTypes} from "sequelize";
import {sequelize as conn} from "../../lib/database";
import {iHistoricalSplits} from "./interfaces/iHistoricalSplits";

type HistoricalSplitsCreationAttributes = Optional<iHistoricalSplits, "id" | "created_at" | "updated_at" | "created_by_id" | "updated_by_id">;

class HistoricalSplits extends Model<iHistoricalSplits, HistoricalSplitsCreationAttributes> {
    declare id?: number;
    declare ticker_internal_id: number;
    declare created_at?: Date;
    declare updated_at?: Date;
    declare created_by_id: number;
    declare updated_by_id: number;
    declare document_date: Date;
    declare split?: number;
    declare insplit?: number;
}

HistoricalSplits.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        ticker_internal_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        created_at: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        updated_at: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        created_by_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        updated_by_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        split: {
            type: DataTypes.DECIMAL(18, 5),
            allowNull: false,
        },
        insplit: {
            type: DataTypes.DECIMAL(18, 5),
            allowNull: false,
        },
        document_date: {
            type: DataTypes.DATEONLY,
            allowNull: false,
        },
    },
    {
        timestamps: false,
        sequelize: conn,
        tableName: "historical_splits",
        hooks: {
            beforeCreate: (record, options) => {
                record.created_at = new Date();
                record.updated_at = new Date();
                record.created_by_id = 1;
                record.updated_by_id = 1;
            },
            beforeUpdate: (record, options) => {
                record.updated_at = new Date();
                record.updated_by_id = 1;
            },
        },
    },
);

export {HistoricalSplits};

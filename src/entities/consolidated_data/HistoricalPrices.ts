import {DataTypes, Model, Optional} from "sequelize";
import {iHistoricalPrices} from "./interfaces/iHistoricalPrices";
import {sequelize as conn} from "../../lib/database";

type HistoricalPricesCreationAttributes = Optional<
    iHistoricalPrices,
    "id" | "created_at" | "updated_at" | "created_by_id" | "updated_by_id" | "open" | "high" | "low" | "close" | "adjusted_close" | "volume" | "ticker_internal_id"
>;

class HistoricalPrices extends Model<iHistoricalPrices, HistoricalPricesCreationAttributes> {
    declare id?: number;
    declare date: Date;
    declare open?: number;
    declare high?: number;
    declare low?: number;
    declare close?: number;
    declare adjusted_close?: number;
    declare volume?: number;
    declare ticker_internal_id: number;
    declare created_at?: Date;
    declare updated_at?: Date;
    declare created_by_id: number;
    declare updated_by_id: number;
}

HistoricalPrices.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        date: {
            type: DataTypes.DATEONLY,
            allowNull: false,
        },
        open: {
            type: DataTypes.DECIMAL(18, 5),
            allowNull: true,
        },
        high: {
            type: DataTypes.DECIMAL(18, 5),
            allowNull: true,
        },
        low: {
            type: DataTypes.DECIMAL(18, 5),
            allowNull: true,
        },
        close: {
            type: DataTypes.DECIMAL(18, 5),
            allowNull: true,
        },
        adjusted_close: {
            type: DataTypes.DECIMAL(18, 5),
            allowNull: true,
        },
        volume: {
            type: DataTypes.BIGINT,
            allowNull: true,
        },
        ticker_internal_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        created_at: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        updated_at: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        created_by_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        updated_by_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
    },
    {
        timestamps: false,
        sequelize: conn,
        tableName: "historical_prices",
        hooks: {
            beforeCreate: (record) => {
                record.created_at = new Date();
                record.updated_at = new Date();
                record.created_by_id = 1;
                record.updated_by_id = 1;
            },
            beforeUpdate: (record) => {
                record.updated_at = new Date();
                record.updated_by_id = 1;
            },
        },
    },
);

export {HistoricalPrices};

export interface iHistoricalDividends {
    id?: number;
    ticker_internal_id: number;
    created_at?: Date;
    updated_at?: Date;
    created_by_id: number;
    updated_by_id: number;
    document_date?: string;
    document_type_year_or_quarter?: string;
    currency_symbol?: string;
    declaration_date?: string;
    record_date?: string;
    payment_date?: string;
    value?: number;
    unadjusted_value?: number;
}

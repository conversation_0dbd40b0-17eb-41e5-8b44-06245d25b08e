export interface iStatisticsOfTicker {
    id?: number;
    created_at?: Date;
    updated_at?: Date;
    created_by_id: number;
    updated_by_id: number;
    ticker_internal_id: number;
    symbol_code: string; // Changed from optional to required to match Strapi schema
    price: number;
    dividend_yield?: number;
    eps_current?: number;
    eps_last_year?: number;
    pe?: number;
    peg_ratio?: number;
    bvps?: number;
    price_to_book?: number;
    ebitda?: number;
    ebitda_margin?: number;
    ebit?: number;
    operation_margin?: number;
    net_margin?: number;
    gross_margin?: number;
    price_working_capital_share?: number;
    ebit_ratio?: number;
    net_debt?: number;
    net_debt_ebit?: number;
    net_debt_ebitda?: number;
    market_capitalization?: number;
    ev_ebit?: number;
    net_debt_shareholders_equity?: number;
    price_sales_ratio?: number;
    roe?: number;
    roa?: number;
    roic?: number;
    current_ratio?: number;
    shareholder_equity_ratio?: number;
    total_debt_to_total_assets_ratio?: number;
    asset_turnover?: number;
    payout_ratio?: number;
    price_to_cash_flow?: number;
}

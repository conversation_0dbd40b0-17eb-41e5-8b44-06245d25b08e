import {Document_type_year_or_quarter} from "../DocumentTypeYearorQuarterly";

export interface iCashFlow {
    id?: number;
    ticker_internal_id: number;
    created_at: Date;
    updated_at: Date;
    created_by_id: number;
    updated_by_id: number;
    document_current: number;
    document_type_year_or_quarter: Document_type_year_or_quarter;
    document_date: string;
    currency_symbol: string;
    investments: number;
    change_to_liabilities: number;
    total_cash_flows_from_investing_activities: number;
    net_borrowings: number;
    total_cash_from_financing_activities: number;
    change_to_operating_activities: number;
    net_income: number;
    change_in_cash: number;
    begin_period_cash_flow: number;
    end_period_cash_flow: number;
    total_cash_from_operating_activities: number;
    issuance_of_capital_stock: number;
    depreciation: number;
    other_cash_flows_from_investing_activities: number;
    dividends_paid: number;
    change_to_inventory: number;
    change_to_account_receivables: number;
    sale_purchase_of_stock: number;
    other_cash_flows_from_financing_activities: number;
    change_to_net_income: number;
    capital_expenditures: number;
    change_receivables: number;
    cash_flows_other_operating: number;
    exchange_rate_changes: number;
    cash_and_cash_equivalents_changes: number;
    change_in_working_capital: number;
    stock_based_compensation: number;
    other_non_cash_items: number;
    free_cash_flow: number;
}

import {DataTypes, Model, Optional} from "sequelize";
import {sequelize as conn} from "../../lib/database";
import {iStatisticsOfTicker} from "./interfaces/iStatisticsOfTicker";

type StatisticsOfTickercreationAttributes = Optional<
    iStatisticsOfTicker,
    | "id"
    | "created_at"
    | "updated_at"
    | "created_by_id"
    | "updated_by_id"
    | "dividend_yield"
    | "eps_current"
    | "eps_last_year"
    | "pe"
    | "peg_ratio"
    | "bvps"
    | "price_to_book"
    | "ebitda"
    | "ebitda_margin"
    | "ebit"
    | "operation_margin"
    | "net_margin"
    | "gross_margin"
    | "price_working_capital_share"
    | "ebit_ratio"
    | "net_debt"
    | "net_debt_ebit"
    | "net_debt_ebitda"
    | "market_capitalization"
    | "ev_ebit"
    | "net_debt_shareholders_equity"
    | "price_sales_ratio"
    | "roe"
    | "roa"
    | "roic"
    | "current_ratio"
    | "shareholder_equity_ratio"
    | "total_debt_to_total_assets_ratio"
    | "asset_turnover"
    | "payout_ratio"
    | "price_to_cash_flow"
>;

class StatisticsOfTicker extends Model<iStatisticsOfTicker, StatisticsOfTickercreationAttributes> {
    declare id?: number;
    declare created_at?: Date;
    declare updated_at?: Date;
    declare created_by_id: number;
    declare updated_by_id: number;
    declare ticker_internal_id: number;
    declare symbol_code: string; // Changed from optional to required
    declare price: number;
    declare dividend_yield?: number;
    declare eps_current?: number;
    declare eps_last_year?: number;
    declare pe?: number;
    declare peg_ratio?: number;
    declare bvps?: number;
    declare price_to_book?: number;
    declare ebitda?: number;
    declare ebitda_margin?: number;
    declare ebit?: number;
    declare operation_margin?: number;
    declare net_margin?: number;
    declare gross_margin?: number;
    declare price_working_capital_share?: number;
    declare ebit_ratio?: number;
    declare net_debt?: number;
    declare net_debt_ebit?: number;
    declare net_debt_ebitda?: number;
    declare market_capitalization?: number;
    declare ev_ebit?: number;
    declare net_debt_shareholders_equity?: number;
    declare price_sales_ratio?: number;
    declare roe?: number;
    declare roa?: number;
    declare roic?: number;
    declare current_ratio?: number;
    declare shareholder_equity_ratio?: number;
    declare total_debt_to_total_assets_ratio?: number;
    declare asset_turnover?: number;
    declare payout_ratio?: number;
    declare price_to_cash_flow?: number;
}

StatisticsOfTicker.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        ticker_internal_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        created_at: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        updated_at: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        created_by_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        updated_by_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        symbol_code: {
            type: DataTypes.STRING,
            allowNull: false, // Changed to match Strapi schema
        },
        price: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: false, // Changed to match Strapi schema
        },
        dividend_yield: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        eps_current: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        eps_last_year: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        pe: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        peg_ratio: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        bvps: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        price_to_book: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        ebitda: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        ebitda_margin: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        ebit: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        operation_margin: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        net_margin: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        gross_margin: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        price_working_capital_share: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        ebit_ratio: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        net_debt: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        net_debt_ebit: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        net_debt_ebitda: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        market_capitalization: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        net_debt_shareholders_equity: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        price_sales_ratio: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        roe: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        roa: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        roic: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        current_ratio: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        shareholder_equity_ratio: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        total_debt_to_total_assets_ratio: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        asset_turnover: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        payout_ratio: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        ev_ebit: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        price_to_cash_flow: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
    },
    {
        timestamps: false,
        sequelize: conn,
        tableName: "statistics_of_tickers",
        hooks: {
            beforeCreate: (record) => {
                record.created_at = new Date();
                record.updated_at = new Date();
                record.created_by_id = 1;
                record.updated_by_id = 1;
            },
            beforeUpdate: (record) => {
                record.updated_at = new Date();
                record.updated_by_id = 1;
            },
        },
    },
);

export {StatisticsOfTicker};

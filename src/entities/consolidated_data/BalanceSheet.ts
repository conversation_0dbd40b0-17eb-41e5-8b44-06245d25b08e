import {Model, Optional, DataTypes} from "sequelize";
import {sequelize as conn} from "../../lib/database";
import {iBalanceSheet} from "./interfaces/iBalanceSheet";
import {Document_type_year_or_quarter} from "./DocumentTypeYearorQuarterly";

type BalanceSheetCreationAttributes = Optional<
    iBalanceSheet,
    | "id"
    | "ticker_internal_id"
    | "created_at"
    | "updated_at"
    | "created_by_id"
    | "updated_by_id"
    | "document_current"
    | "document_type_year_or_quarter"
    | "document_date"
    | "currency_symbol"
    | "total_assets"
    | "intangible_assets"
    | "earning_assets"
    | "other_current_assets"
    | "total_liab"
    | "total_stockholder_equity"
    | "deferred_long_term_liab"
    | "other_current_liab"
    | "common_stock"
    | "capital_stock"
    | "retained_earnings"
    | "other_liab"
    | "good_will"
    | "other_assets"
    | "cash"
    | "cash_and_equivalents"
    | "total_current_liabilities"
    | "current_deferred_revenue"
    | "net_debt"
    | "short_term_debt"
    | "short_long_term_debt"
    | "short_long_term_debt_total"
    | "other_stockholder_equity"
    | "property_plant_equipment"
    | "total_current_assets"
    | "long_term_investments"
    | "net_tangible_assets"
    | "short_term_investments"
    | "net_receivables"
    | "long_term_debt"
    | "inventory"
    | "accounts_payable"
    | "total_permanent_equity"
    | "noncontrolling_interest_in_consolidated_entity"
    | "temporary_equity_redeemable_noncontrolling_interests"
    | "accumulated_other_comprehensive_income"
    | "additional_paid_in_capital"
    | "common_stock_total_equity"
    | "preferred_stock_total_equity"
    | "retained_earnings_total_equity"
    | "treasury_stock"
    | "accumulated_amortization"
    | "non_current_assets_other"
    | "deferred_long_term_asset_charges"
    | "non_current_assets_total"
    | "capital_lease_obligations"
    | "long_term_debt_total"
    | "non_current_liabilities_other"
    | "non_current_liabilities_total"
    | "negative_goodwill"
    | "warrants"
    | "preferred_stock_redeemable"
    | "capital_surpluse"
    | "liabilities_and_stockholders_equity"
    | "cash_and_short_term_investments"
    | "property_plant_and_equipment_gross"
    | "property_plant_and_equipment_net"
    | "accumulated_depreciation"
    | "net_working_capital"
    | "net_invested_capital"
    | "common_stock_shares_outstanding"
>;

class BalanceSheet extends Model<iBalanceSheet, BalanceSheetCreationAttributes> {
    declare id?: number;
    declare created_at?: Date;
    declare updated_at?: Date;
    declare created_by_id: number;
    declare updated_by_id: number;
    declare ticker_internal_id: number;
    declare document_current: number;
    declare document_type_year_or_quarter: Document_type_year_or_quarter;
    declare document_date: string;
    declare currency_symbol: string;
    declare total_assets: number;
    declare intangible_assets: number;
    declare earning_assets: number;
    declare other_current_assets: number;
    declare total_liab: number;
    declare total_stockholder_equity: number;
    declare deferred_long_term_liab: number;
    declare other_current_liab: number;
    declare common_stock: number;
    declare capital_stock: number;
    declare retained_earnings: number;
    declare other_liab: number;
    declare good_will: number;
    declare other_assets: number;
    declare cash: number;
    declare cash_and_equivalents: number;
    declare total_current_liabilities: number;
    declare current_deferred_revenue: number;
    declare net_debt: number;
    declare short_term_debt: number;
    declare short_long_term_debt: number;
    declare short_long_term_debt_total: number;
    declare other_stockholder_equity: number;
    declare property_plant_equipment: number;
    declare total_current_assets: number;
    declare long_term_investments: number;
    declare net_tangible_assets: number;
    declare short_term_investments: number;
    declare net_receivables: number;
    declare long_term_debt: number;
    declare inventory: number;
    declare accounts_payable: number;
    declare total_permanent_equity: number;
    declare noncontrolling_interest_in_consolidated_entity: number;
    declare temporary_equity_redeemable_noncontrolling_interests: number;
    declare accumulated_other_comprehensive_income: number;
    declare additional_paid_in_capital: number;
    declare common_stock_total_equity: number;
    declare preferred_stock_total_equity: number;
    declare retained_earnings_total_equity: number;
    declare treasury_stock: number;
    declare accumulated_amortization: number;
    declare non_current_assets_other: number;
    declare deferred_long_term_asset_charges: number;
    declare non_current_assets_total: number;
    declare capital_lease_obligations: number;
    declare long_term_debt_total: number;
    declare non_current_liabilities_other: number;
    declare non_current_liabilities_total: number;
    declare negative_goodwill: number;
    declare warrants: number;
    declare preferred_stock_redeemable: number;
    declare capital_surpluse: number;
    declare liabilities_and_stockholders_equity: number;
    declare cash_and_short_term_investments: number;
    declare property_plant_and_equipment_gross: number;
    declare property_plant_and_equipment_net: number;
    declare accumulated_depreciation: number;
    declare net_working_capital: number;
    declare net_invested_capital: number;
    declare common_stock_shares_outstanding: number;

    constructor(props?: Partial<iBalanceSheet>, document_current?: number) {
        super(props);
        Object.assign(this, props);
        if (!document_current) {
            this.document_current = 0;
        }
    }
}

BalanceSheet.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        ticker_internal_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        created_at: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        updated_at: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        created_by_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        updated_by_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        document_current: {
            type: DataTypes.TINYINT,
            allowNull: false,
        },
        document_type_year_or_quarter: {
            type: DataTypes.ENUM("y", "q"),
            allowNull: false,
        },
        document_date: {
            type: DataTypes.DATEONLY,
            allowNull: true,
        },
        currency_symbol: {
            type: DataTypes.STRING(6),
            allowNull: true,
        },
        total_assets: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        intangible_assets: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        earning_assets: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        other_current_assets: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        total_liab: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        total_stockholder_equity: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        deferred_long_term_liab: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        other_current_liab: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        common_stock: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        capital_stock: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        retained_earnings: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        other_liab: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        good_will: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        other_assets: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        cash: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        cash_and_equivalents: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        total_current_liabilities: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        current_deferred_revenue: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        net_debt: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        short_term_debt: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        short_long_term_debt: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        short_long_term_debt_total: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        other_stockholder_equity: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        property_plant_equipment: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        total_current_assets: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        long_term_investments: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        net_tangible_assets: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        short_term_investments: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        net_receivables: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        long_term_debt: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        inventory: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        accounts_payable: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        total_permanent_equity: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        noncontrolling_interest_in_consolidated_entity: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        temporary_equity_redeemable_noncontrolling_interests: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        accumulated_other_comprehensive_income: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        additional_paid_in_capital: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        common_stock_total_equity: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        preferred_stock_total_equity: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        retained_earnings_total_equity: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        treasury_stock: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        accumulated_amortization: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        non_current_assets_other: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        deferred_long_term_asset_charges: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        non_current_assets_total: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        capital_lease_obligations: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        long_term_debt_total: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        non_current_liabilities_other: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        non_current_liabilities_total: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        negative_goodwill: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        warrants: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        preferred_stock_redeemable: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        capital_surpluse: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        liabilities_and_stockholders_equity: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        cash_and_short_term_investments: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        property_plant_and_equipment_gross: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        property_plant_and_equipment_net: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        accumulated_depreciation: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        net_working_capital: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        net_invested_capital: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        common_stock_shares_outstanding: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
    },
    {
        timestamps: false,
        sequelize: conn,
        tableName: "fundamentals_balance_sheet",
        hooks: {
            beforeCreate: (record) => {
                record.created_at = new Date();
                record.updated_at = new Date();
                record.created_by_id = 1;
                record.updated_by_id = 1;
            },
            beforeUpdate: (record) => {
                record.updated_at = new Date();
                record.updated_by_id = 1;
            },
        },
    },
);

export {BalanceSheet};

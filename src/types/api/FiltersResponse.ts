
export type FilterLabel = {
    en: string | null,
    fr: string | null
    pt: string | null
}

export type FilterLocation = {
    value: string,
    label: string
}

export type FilterCategory = {
    createdAt:	string
    updatedAt:	string
    en:	string
    pt:	string
    fr:	string
}

export type FilterCategoryAttributes = {
    id:	number,
    createdAt:	string
    updatedAt:	string
    en:	string
    pt:	string
    fr:	string,
    isEnabled?: boolean
}

export enum filterTypeEnum {
    normal = 'normal',
    percent = 'percent',
    million = 'million'
}

export type FilterResponse = {
    id: number,
    indicator_name: string,
    min: number,
    max: number,
    step: number | null,
    value?: number,
    values: [number, number],
    search_type: string,
    label: FilterLabel | null,
    actived: boolean,
    filter_type: filterTypeEnum,
    filter_category: FilterCategory,
    tooltip?: string,
    type?: string,
}

export type FilterItemResponse = {
    id: number,
    attributes: FilterResponse
}

export type FiltersReponse =  FilterItemResponse[]

export type SelectedFilters = {
    name?: string,
    stage: string,
    location: string[],
    sector: number | string,
    filters: { [filter_label: string] : FilterResponse }
}

export type MultiselectLocation = {
    [key: string]: string ;
    value: string;
}
import { Dispatch, SetStateAction } from "react"
import { PaginationReactTable } from "../utils/MaterialTable"

export type WatchListType = {
    id: number,
    name: string,
    price: number,
    is_enable: boolean,
    current_price: number,
    createdAt: string,
    updatedAt: string,
    symbol_code: string,
    after_added: number,
    ticker_id: number
}


export type WatchListContextType = {
    list: WatchListType[],
    pagination: PaginationReactTable,
    isLoading: boolean,
    rowCount: number,
    pageCount: number,
    setList: Dispatch<SetStateAction<WatchListType[]>>,
    setRowCount: Dispatch<SetStateAction<number>>,
    setPagination: Dispatch<SetStateAction<PaginationReactTable>>,
    setisLoading: Dispatch<SetStateAction<boolean>>,
    fetchWatchList: (pagination: { pageIndex: number, pageSize: number }) => void
}
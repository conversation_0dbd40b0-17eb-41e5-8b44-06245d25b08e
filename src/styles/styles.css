@tailwind base;
@tailwind components;
@tailwind utilities;
@import "../../node_modules/@syncfusion/ej2-base/styles/material.css";
@import "../../node_modules/@syncfusion/ej2-react-inputs/styles/material.css";
@import "../../node_modules/@syncfusion/ej2-react-dropdowns/styles/material.css";
@import "../../node_modules/@syncfusion/ej2-buttons/styles/material.css";
@import "../../node_modules/@syncfusion/ej2-popups/styles/material.css";
@import "../../node_modules/@syncfusion/ej2-react-navigations/styles/material.css";
@import "../../node_modules/@syncfusion/ej2-lists/styles/material.css";

@import "../../node_modules/@syncfusion/ej2-calendars/styles/material.css";
@import "../../node_modules/@syncfusion/ej2-navigations/styles/material.css";
@import "../../node_modules/@syncfusion/ej2-splitbuttons/styles/material.css";
@import "../../node_modules/@syncfusion/ej2-react-grids/styles/material.css";
@import "../../node_modules/@syncfusion/ej2-react-calendars/styles/material.css";
@import "../../node_modules/react-toastify/dist/ReactToastify.css";

@import "./syncfusion.css";

* {
  box-sizing: border-box;
}

.e-table tr td:first-child {
  background-color: #cecece;
  font-weight: bold;
  color: black;
  border-bottom: 0.2px solid var(--color-sf-purple);
}

.e-headercontent .e-table {
  border: none;
  border-bottom: 0.2px solid var(--color-sf-purple);
  color: var(--color-sf-purple);
}

.e-grid.e-default .e-rowcell.e-lastrowcell:not(.e-xlsel-bottom-border),
.e-grid.e-default .e-detailrowcollapse.e-lastrowcell {
  border: none;
  border-width: 0px;
  border-bottom: 0.2px solid var(--color-sf-purple);
}

.e-grid .e-rowcell {
  border: none;
  border-width: 0px;
  border-bottom: 0.2px solid var(--color-sf-purple);
}

.btn-stage-select-page {
  font-weight: bold;
  padding: 1rem 1.5rem;
  border-radius: 10px;
  background-color: none;
  color: var(--color-sf-grey);
}

.btn-stage-select-page-active {
  background-color: var(--color-sf-light-purple);
  color: var(--color-sf-dark-purple);
  transition: 0.3s;
}
.btn-stage-select-page div {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.4rem;
}

.e-toolbar {
  height: 40px !important;
}


.transaction-asset-name {
  font-weight: bold;
  background-color: var(--color-sf-dark-purple);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: bold;
  color: white;
}

.transaction-type-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: bold;
  color: white;
}

.transaction-type-badge-buy {
  background-color: var(--colors-success);
}

.transaction-type-badge-sell {
  background-color: var(--colors-error);
}



@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}



@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

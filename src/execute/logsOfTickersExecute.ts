import {Logs} from "../entities/consolidated_data/Logs";

export class LogsOfTickersExecute {
    static async saveLogToMoveFile(ticker_internal_id: number, message: string) {
        const log = await Logs.findOne({
            where: {
                ticker_internal_id,
            },
        });
        if (log) {
            log.latest_log = message;
            log.latest_update = new Date();

            await log.save();
        }
    }
    static async saveError(ticker_internal_id: number, message: string) {
        const log = await Logs.findOne({
            where: {
                ticker_internal_id,
            },
        });
        if (log) {
            log.latest_log = message;
            log.latest_update = new Date();

            await log.save();
        }
    }
}

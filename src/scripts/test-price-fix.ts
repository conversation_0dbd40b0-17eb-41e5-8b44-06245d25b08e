import {initDatabase} from "../lib/database";
import {ListOfTickers} from "../entities/consolidated_data/ListOfTickers";
import {getPrices} from "../controller/priceController";

async function testPriceFix() {
    try {
        console.log("Initializing database connection...");
        await initDatabase();

        console.log("Finding test tickers...");

        // Test with a valid ticker first
        const validTicker = await ListOfTickers.findOne({
            where: {
                primary_ticker_eodhd: "AAPL.US",
                is_enable: 1,
            },
        });

        if (validTicker) {
            console.log("Testing with valid ticker AAPL.US...");
            await testSingleTicker(validTicker);
        }

        // Test with an invalid ticker to ensure error handling works
        console.log("\nTesting error handling with invalid ticker...");
        const invalidTicker = {
            id: 99999,
            primary_ticker_eodhd: "INVALID.TICKER",
            is_enable: 1,
        } as ListOfTickers;

        await testSingleTicker(invalidTicker);

        console.log("\n=== All Tests Completed ===");
        process.exit(0);
    } catch (error) {
        console.error("Test failed:", error);
        process.exit(1);
    }
}

async function testSingleTicker(ticker: ListOfTickers) {
    try {
        console.log(`\n=== Testing Price Fetch for ${ticker.primary_ticker_eodhd} ===`);
        console.log(`Ticker ID: ${ticker.id}`);

        const startTime = Date.now();
        const [validPrices, invalidPrices] = await getPrices([ticker]);
        const endTime = Date.now();

        console.log(`\n=== Results ===`);
        console.log(`Duration: ${endTime - startTime}ms`);
        console.log(`Valid prices: ${validPrices.length}`);
        console.log(`Invalid prices: ${invalidPrices.length}`);

        if (validPrices.length > 0) {
            console.log("\n=== Valid Price Data ===");
            console.log(JSON.stringify(validPrices[0], null, 2));
        }

        if (invalidPrices.length > 0) {
            console.log("\n=== Invalid Price Data ===");
            console.log(JSON.stringify(invalidPrices[0], null, 2));
        }

        console.log("\n=== Test Completed Successfully ===");
        console.log("✅ No circular JSON structure errors");
        console.log('✅ No "Cannot read properties of undefined" errors');
        console.log("✅ API calls working properly");

        // Don't exit here, let the main function continue with more tests
    } catch (error) {
        console.error("\n=== Test Failed ===");
        console.error("❌ Error occurred:", error);

        if (error instanceof Error) {
            console.error("Error message:", error.message);
            console.error("Error stack:", error.stack);
        }

        // Don't exit on individual test failure, let main function handle it
        throw error;
    }
}

// Run the test
testPriceFix();

import {addLogJobExecution} from "../lib/agenda";
import {LogLevel} from "../utils/types/logs/log";

async function addTestLogs() {
    console.log("Adding test logs to the database...");

    // Add various test logs with different levels and data
    await addLogJobExecution(LogLevel.ERROR, "getFundamentalDataController", "Error processing XTR.LSE: Service temporarily unavailable", {
        error: "Service Unavailable",
        statusCode: 503,
        ticker: "XTR.LSE"
    }, 9775);

    await addLogJobExecution(LogLevel.WARNING, "processPriceData", "Price data incomplete for AAPL: missing volume information", {
        ticker: "AAPL",
        missingFields: ["volume"],
        dataSource: "EODHD"
    }, 1234);

    await addLogJobExecution(LogLevel.INFO, "syncDividendsData", "Successfully processed 150 dividend records", {
        processedCount: 150,
        duration: "2.3s",
        batchSize: 50
    }, 5678);

    await addLogJobExecution(LogLevel.DEBUG, "validateS3FileStructure", "File validation completed: splits_2025_07_04.csv", {
        fileName: "splits_2025_07_04.csv",
        fileSize: "2.1MB",
        recordCount: 1250,
        validationTime: "0.5s"
    });

    await addLogJobExecution(LogLevel.ERROR, "processEPSData", "Database connection timeout while processing EPS batch", {
        batchSize: 5,
        timeout: "30s",
        retryAttempt: 3,
        connectionPool: "primary"
    }, 9876);

    await addLogJobExecution(LogLevel.WARNING, "checkAgendaStatus", "High memory usage detected: 85% of available memory in use", {
        memoryUsage: "85%",
        availableMemory: "2.1GB",
        threshold: "80%"
    });

    await addLogJobExecution(LogLevel.INFO, "backupDatabase", "Database backup completed successfully", {
        backupSize: "1.2GB",
        duration: "45s",
        location: "s3://backups/db_2025_07_04.sql",
        compressionRatio: "0.65"
    });

    await addLogJobExecution(LogLevel.DEBUG, "cacheWarmup", "Cache warming completed for ticker data", {
        cacheKeys: 1250,
        warmupTime: "3.2s",
        hitRatio: "0.95"
    });

    await addLogJobExecution(LogLevel.ERROR, "apiRateLimit", "API rate limit exceeded for EODHD", {
        apiProvider: "EODHD",
        requestsPerMinute: 1000,
        limit: 500,
        resetTime: "2025-07-04T14:30:00Z"
    });

    await addLogJobExecution(LogLevel.INFO, "jobScheduler", "Scheduled job execution completed", {
        jobName: "daily-data-sync",
        executionTime: "12.5s",
        nextRun: "2025-07-05T02:00:00Z"
    });

    console.log("Test logs added successfully!");
}

// Run the script
addTestLogs().catch(console.error);

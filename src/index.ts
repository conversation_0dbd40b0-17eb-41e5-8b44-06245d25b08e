import {getDividendsSaveToDatabase} from "./controller/dividendsController";
import {processEPSSaveToDatabase} from "./controller/epsController";
import {getTickersUpdatedLastXDays, processS3FilesFinancialsToDatabase, processS3FilesGeneralDataToDatabase} from "./controller/filesController";
import {getFundamentalDataAndSaveToS3} from "./controller/fundamentalDataController";
import {getPriceHistorySaveToDatabase} from "./controller/historicalPriceController";
import {getPrices, setInvalidPrices, setPrices} from "./controller/priceController";
import {getSplitsSaveToDatabase} from "./controller/splitsController";
import {getTickersStatisticsAndCalculate} from "./controller/statisticsOfTickerController";
import {getAllListOfTickers, getOutdatedListOfTickers, getTickersByPrimaryTickerCodes, populateListOfTickers} from "./controller/tickersController";
import {ListOfTickers} from "./entities/consolidated_data/ListOfTickers";
import {addLogJobExecution} from "./lib/agenda";
import {LogLevel} from "./utils/types/logs/log";
import {consoleLog} from "@/lib/utils";

/**
 * Populates the list of stock tickers from external data sources
 * @param exchangeCodes - Optional array of exchange codes to filter tickers (e.g., ['US', 'LSE']). If not provided, all exchanges will be processed.
 * @returns Status message indicating successful processing or error message
 */
export async function _populateListOfTickers(exchangeCodes?: string[]) {
    try {
        await populateListOfTickers(exchangeCodes);
        return "Processed populating Stock Tickers";
    } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        consoleLog(errorMessage, _populateListOfTickers.name, LogLevel.ERROR);
        addLogJobExecution(LogLevel.ERROR, _populateListOfTickers.name, "Error populating list of tickers", {error: errorMessage});
        return errorMessage;
    }
}

/**
 * Retrieves fundamental data for a list of tickers and saves it to S3
 * @param tickerCodes - Optional array of primary_ticker_eodhd codes (e.g., ['NVDA.US', 'XP.US']). If provided, only these tickers will be processed instead of outdated tickers.
 * @param historyId - Optional job history ID for progress tracking
 * @returns Status message or error message
 */
export async function _getFundamentalDataAndSaveToS3(tickerCodes?: string[], historyId?: string) {
    try {
        let data: ListOfTickers[];
        if (tickerCodes && tickerCodes.length > 0) {
            // Use the provided ticker codes to get specific tickers
            data = await getTickersByPrimaryTickerCodes(tickerCodes);
            consoleLog(`Processing ${data.length} specific tickers: ${tickerCodes.join(", ")}`, _getFundamentalDataAndSaveToS3.name);
            addLogJobExecution(LogLevel.INFO, _getFundamentalDataAndSaveToS3.name, "Processing specific tickers", {tickerCodesLength: tickerCodes.length});
        } else {
            // Use the existing logic to get outdated tickers
            data = await getOutdatedListOfTickers();
            consoleLog(`Processing ${data.length} outdated tickers`, _getFundamentalDataAndSaveToS3.name);
            addLogJobExecution(LogLevel.INFO, _getFundamentalDataAndSaveToS3.name, "Processing outdated tickers", {tickersLength: data.length});
        }

        await getFundamentalDataAndSaveToS3(data, historyId);
        return "Processed files with fundamental data for tickers";
    } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        consoleLog(errorMessage, _getFundamentalDataAndSaveToS3.name, LogLevel.ERROR);
        addLogJobExecution(LogLevel.ERROR, _getFundamentalDataAndSaveToS3.name, "Error getting fundamental data", {error: errorMessage});
        return errorMessage;
    }
}

/**
 * Processes S3 files containing general ticker data and saves to database
 * @param tickerCodes - Optional array of primary_ticker_eodhd codes (e.g., ['NVDA.US', 'XP.US']). If provided, only these tickers will be processed instead of tickers updated in the last 8 days.
 * @param historyId - Optional job history ID for progress tracking
 * @returns Status message indicating successful processing or error message
 */
export async function _processS3FilesGeneralDataToDatabase(tickerCodes?: string[], historyId?: string) {
    try {
        let data: ListOfTickers[];
        if (tickerCodes && tickerCodes.length > 0) {
            // Use the provided ticker codes to get specific tickers
            data = await getTickersByPrimaryTickerCodes(tickerCodes);
            consoleLog(`Processing ${data.length} specific tickers: ${tickerCodes.join(", ")}`, _processS3FilesGeneralDataToDatabase.name);
            addLogJobExecution(LogLevel.INFO, _processS3FilesGeneralDataToDatabase.name, "Processing specific tickers", {tickerCodesLength: tickerCodes.length});
        } else {
            // Use the existing logic to get tickers updated last 8 days
            data = await getTickersUpdatedLastXDays();
            consoleLog(`Processing ${data.length} tickers updated last 8 days`, _processS3FilesGeneralDataToDatabase.name);
            addLogJobExecution(LogLevel.INFO, _processS3FilesGeneralDataToDatabase.name, "Processing tickers updated last 8 days", {tickersLength: data.length});
        }
        await processS3FilesGeneralDataToDatabase(data, historyId);
        return "Processed files with general data saving to database";
    } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        consoleLog(errorMessage, _processS3FilesGeneralDataToDatabase.name, LogLevel.ERROR);
        addLogJobExecution(LogLevel.ERROR, _processS3FilesGeneralDataToDatabase.name, "Error processing S3 files general data", {error: errorMessage});
        return errorMessage;
    }
}

/**
 * Processes S3 files containing financial data and saves to database
 * @param tickerCodes - Optional array of primary_ticker_eodhd codes (e.g., ['NVDA.US', 'XP.US']). If provided, only these tickers will be processed instead of tickers updated in the last 8 days.
 * @param historyId - Optional job history ID for progress tracking
 * @returns Status message indicating successful processing or error message
 */
export async function _processS3FilesFinancialsToDatabase(tickerCodes?: string[], historyId?: string) {
    try {
        let data: ListOfTickers[];
        if (tickerCodes && tickerCodes.length > 0) {
            // Use the provided ticker codes to get specific tickers
            data = await getTickersByPrimaryTickerCodes(tickerCodes);
            consoleLog(`Processing ${data.length} specific tickers: ${tickerCodes.join(", ")}`, _processS3FilesFinancialsToDatabase.name);
            addLogJobExecution(LogLevel.INFO, _processS3FilesFinancialsToDatabase.name, "Processing specific tickers", {tickerCodesLength: tickerCodes.length});
        } else {
            // Use the existing logic to get tickers updated last 8 days
            data = await getTickersUpdatedLastXDays();
            consoleLog(`Processing ${data.length} tickers updated last 8 days`, _processS3FilesFinancialsToDatabase.name);
            addLogJobExecution(LogLevel.INFO, _processS3FilesFinancialsToDatabase.name, "Processing tickers updated last 8 days", {tickersLength: data.length});
        }
        await processS3FilesFinancialsToDatabase(data, historyId);
        return "Processed files with financial data saving to database";
    } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        consoleLog(errorMessage, _processS3FilesFinancialsToDatabase.name, LogLevel.ERROR);
        addLogJobExecution(LogLevel.ERROR, _processS3FilesFinancialsToDatabase.name, "Error processing S3 files financials", {error: errorMessage});
        return errorMessage;
    }
}

/**
 * Retrieves dividend information for specified tickers and saves to database
 * @param tickerCodes - Optional array of primary_ticker_eodhd codes (e.g., ['NVDA.US', 'XP.US']). If provided, only these tickers will be processed instead of all tickers.
 * @param historyId - Optional job history ID for progress tracking
 * @returns Status message indicating successful processing or error message
 */
export async function _getDividendsSaveToDatabase(tickerCodes?: string[], historyId?: string) {
    try {
        let data: ListOfTickers[];
        if (tickerCodes && tickerCodes.length > 0) {
            // Use the provided ticker codes to get specific tickers
            data = await getTickersByPrimaryTickerCodes(tickerCodes);
            consoleLog(`Processing ${data.length} specific tickers: ${tickerCodes.join(", ")}`, _getDividendsSaveToDatabase.name);
            addLogJobExecution(LogLevel.INFO, _getDividendsSaveToDatabase.name, "Processing specific tickers", {tickerCodesLength: tickerCodes.length});
        } else {
            data = await getAllListOfTickers();
            consoleLog(`Processing ${data.length} tickers`, _getDividendsSaveToDatabase.name);
            addLogJobExecution(LogLevel.INFO, _getDividendsSaveToDatabase.name, "Processing all tickers", {tickersLength: data.length});
        }
        await getDividendsSaveToDatabase(data, historyId);
        return "Dividends added to database";
    } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        consoleLog(errorMessage, _getDividendsSaveToDatabase.name, LogLevel.ERROR);
        addLogJobExecution(LogLevel.ERROR, _getDividendsSaveToDatabase.name, "Error getting dividends", {error: errorMessage});
        return errorMessage;
    }
}

/**
 * Retrieves and processes stock split information and saves to database
 * @param tickerCodes - Optional array of primary_ticker_eodhd codes (e.g., ['NVDA.US', 'XP.US']). If provided, only these tickers will be processed instead of all tickers.
 * @param historyId - Optional job history ID for progress tracking
 * @returns Status message indicating successful processing or error message
 */
export async function _getSplitsSaveToDatabase(tickerCodes?: string[], historyId?: string) {
    try {
        let data: ListOfTickers[];
        if (tickerCodes && tickerCodes.length > 0) {
            // Use the provided ticker codes to get specific tickers
            data = await getTickersByPrimaryTickerCodes(tickerCodes);
            consoleLog(`Processing ${data.length} specific tickers: ${tickerCodes.join(", ")}`, _getSplitsSaveToDatabase.name);
            addLogJobExecution(LogLevel.INFO, _getSplitsSaveToDatabase.name, "Processing specific tickers", {tickerCodesLength: tickerCodes.length});
        } else {
            data = await getAllListOfTickers();
            consoleLog(`Processing ${data.length} tickers`, _getSplitsSaveToDatabase.name);
            addLogJobExecution(LogLevel.INFO, _getSplitsSaveToDatabase.name, "Processing all tickers", {tickersLength: data.length});
        }
        await getSplitsSaveToDatabase(data, historyId);
        return "Splits added to database";
    } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        consoleLog(errorMessage, _getSplitsSaveToDatabase.name, LogLevel.ERROR);
        addLogJobExecution(LogLevel.ERROR, _getSplitsSaveToDatabase.name, "Error getting splits", {error: errorMessage});
        return errorMessage;
    }
}

/**
 * Retrieves current price data for specified tickers and saves to database
 * @param tickerCodes - Optional array of primary_ticker_eodhd codes (e.g., ['NVDA.US', 'XP.US']). If provided, only these tickers will be processed instead of all tickers.
 * @param historyId - Optional job history ID for progress tracking
 * @returns Status message indicating successful processing or error message
 */
export async function _getPricesSaveToDatabase(tickerCodes?: string[], historyId?: string) {
    try {
        let data: ListOfTickers[];
        if (tickerCodes && tickerCodes.length > 0) {
            // Use the provided ticker codes to get specific tickers
            data = await getTickersByPrimaryTickerCodes(tickerCodes);
            consoleLog(`Processing ${data.length} specific tickers: ${tickerCodes.join(", ")}`, _getPricesSaveToDatabase.name);
            addLogJobExecution(LogLevel.INFO, _getPricesSaveToDatabase.name, "Processing specific tickers", {tickerCodesLength: tickerCodes.length});
        } else {
            data = await getAllListOfTickers();
            consoleLog(`Processing ${data.length} tickers`, _getPricesSaveToDatabase.name);
            addLogJobExecution(LogLevel.INFO, _getPricesSaveToDatabase.name, "Processing all tickers", {tickersLength: data.length});
        }
        const [valid_prices, invalid_prices] = await getPrices(data, historyId);
        addLogJobExecution(LogLevel.INFO, _getPricesSaveToDatabase.name, "Retrieved prices for tickers", {
            tickersLength: data.length,
            validPricesLength: valid_prices.length,
            invalidPricesLength: invalid_prices.length,
        });
        if (valid_prices.length > 0) {
            addLogJobExecution(LogLevel.INFO, _getPricesSaveToDatabase.name, "Setting prices for tickers", {
                validPricesLength: valid_prices.length,
            });
            consoleLog(`Setting prices for ${valid_prices.length} tickers`, _getPricesSaveToDatabase.name);
            await setPrices(valid_prices);
        }
        if (invalid_prices.length > 0) {
            addLogJobExecution(LogLevel.INFO, _getPricesSaveToDatabase.name, "Setting invalid prices for tickers", {
                invalidPricesLength: invalid_prices.length,
            });
            consoleLog(`Setting invalid prices for ${invalid_prices.length} tickers`, _getPricesSaveToDatabase.name);
            await setInvalidPrices(invalid_prices);
        }
        addLogJobExecution(LogLevel.INFO, _getPricesSaveToDatabase.name, "Prices set for tickers", {});
        consoleLog("Prices set for tickers", _getPricesSaveToDatabase.name);
        return "Prices added to database";
    } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        consoleLog(errorMessage, _getPricesSaveToDatabase.name, LogLevel.ERROR);
        addLogJobExecution(LogLevel.ERROR, _getPricesSaveToDatabase.name, "Error getting prices", {error: errorMessage});
        return errorMessage;
    }
}

/**
 * Processes and updates EPS (Earnings Per Share) data for specified tickers and saves to database
 * @param tickerCodes - Optional array of primary_ticker_eodhd codes (e.g., ['NVDA.US', 'XP.US']). If provided, only these tickers will be processed instead of all tickers.
 * @param historyId - Optional job history ID for progress tracking
 * @returns Status message indicating successful processing or error message
 */
export async function _processEPSSaveToDatabase(tickerCodes?: string[], historyId?: string) {
    try {
        let data: ListOfTickers[];
        if (tickerCodes && tickerCodes.length > 0) {
            // Use the provided ticker codes to get specific tickers
            data = await getTickersByPrimaryTickerCodes(tickerCodes);
            consoleLog(`Processing ${data.length} specific tickers: ${tickerCodes.join(", ")}`, _processEPSSaveToDatabase.name);
            addLogJobExecution(LogLevel.INFO, _processEPSSaveToDatabase.name, "Processing specific tickers", {tickerCodesLength: tickerCodes.length});
        } else {
            data = await getAllListOfTickers();
            consoleLog(`Processing ${data.length} tickers`, _processEPSSaveToDatabase.name);
            addLogJobExecution(LogLevel.INFO, _processEPSSaveToDatabase.name, "Processing all tickers", {tickersLength: data.length});
        }
        await processEPSSaveToDatabase(data, historyId);
        return "EPS added to database";
    } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        consoleLog(errorMessage, _processEPSSaveToDatabase.name, LogLevel.ERROR);
        addLogJobExecution(LogLevel.ERROR, _processEPSSaveToDatabase.name, "Error processing EPS", {error: errorMessage});
        return errorMessage;
    }
}

/**
 * Retrieves and calculates statistical data for specified tickers and saves to database
 * @param tickerCodes - Optional array of primary_ticker_eodhd codes (e.g., ['NVDA.US', 'XP.US']). If provided, only these tickers will be processed instead of all tickers.
 * @param historyId - Optional job history ID for progress tracking
 * @returns Status message indicating successful processing or error message
 */
export async function _getTickersStatisticsAndCalculate(tickerCodes?: string[], historyId?: string) {
    try {
        let data: ListOfTickers[];
        if (tickerCodes && tickerCodes.length > 0) {
            // Use the provided ticker codes to get specific tickers
            data = await getTickersByPrimaryTickerCodes(tickerCodes);
            consoleLog(`Processing ${data.length} specific tickers: ${tickerCodes.join(", ")}`, _getTickersStatisticsAndCalculate.name);
            addLogJobExecution(LogLevel.INFO, _getTickersStatisticsAndCalculate.name, "Processing specific tickers", {tickerCodesLength: tickerCodes.length});
        } else {
            data = await getAllListOfTickers();
            consoleLog(`Processing ${data.length} tickers`, _getTickersStatisticsAndCalculate.name);
            addLogJobExecution(LogLevel.INFO, _getTickersStatisticsAndCalculate.name, "Processing all tickers", {tickersLength: data.length});
        }
        await getTickersStatisticsAndCalculate(data, historyId);
        return "Statistics set for tickers";
    } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        consoleLog(errorMessage, _getTickersStatisticsAndCalculate.name, LogLevel.ERROR);
        addLogJobExecution(LogLevel.ERROR, _getTickersStatisticsAndCalculate.name, "Error getting statistics", {error: errorMessage});
        return errorMessage;
    }
}

/**
 * Retrieves historical price data for specified tickers and saves to database
 * Implements delta logic - first run gets all history, subsequent runs get only new data
 * @param tickerCodes - Optional array of primary_ticker_eodhd codes (e.g., ['NVDA.US', 'XP.US']). If provided, only these tickers will be processed instead of all tickers.
 * @param historyId - Optional job history ID for progress tracking
 * @returns Status message indicating successful processing or error message
 */
export async function _getPriceHistorySaveToDatabase(tickerCodes?: string[], historyId?: string) {
    try {
        let data: ListOfTickers[];
        if (tickerCodes && tickerCodes.length > 0) {
            // Use the provided ticker codes to get specific tickers
            data = await getTickersByPrimaryTickerCodes(tickerCodes);
            addLogJobExecution(LogLevel.INFO, _getPriceHistorySaveToDatabase.name, "Processing specific tickers for price history", {
                requestedCount: tickerCodes.length,
                foundCount: data.length,
            });
        } else {
            // Get all enabled tickers
            data = await getAllListOfTickers();
            addLogJobExecution(LogLevel.INFO, _getPriceHistorySaveToDatabase.name, "Processing all tickers for price history", {
                totalCount: data.length,
            });
        }

        if (data.length === 0) {
            const message = "No tickers found to process for price history";
            addLogJobExecution(LogLevel.WARNING, _getPriceHistorySaveToDatabase.name, message, {});
            return message;
        }

        const result = await getPriceHistorySaveToDatabase(data, historyId);
        return result;
    } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        consoleLog(errorMessage, _getPriceHistorySaveToDatabase.name, LogLevel.ERROR);
        addLogJobExecution(LogLevel.ERROR, _getPriceHistorySaveToDatabase.name, "Error getting price history", {error: errorMessage});
        return errorMessage;
    }
}

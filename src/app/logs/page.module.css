.container {
    flex: 1;
    padding: 2rem;
    padding-top: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.loadingContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    gap: 1rem;
}

.spinner {
    width: 2rem;
    height: 2rem;
    border: 2px solid var(--muted);
    border-top: 2px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;
}

.title {
    font-size: 1.875rem;
    font-weight: 700;
    letter-spacing: -0.025em;
}

.description {
    color: var(--muted-foreground);
    font-size: 1rem;
    margin-top: 0.5rem;
}

.headerActions {
    display: flex;
    gap: 0.5rem;
}

.filterToggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filterIcon {
    height: 1rem;
    width: 1rem;
}

.filtersCard {
    background-color: var(--card);
    border-radius: 0.5rem;
    border: 1px solid var(--border);
    padding: 1.5rem;
}

.filtersHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.filtersTitle {
    font-size: 1.125rem;
    font-weight: 600;
}

.clearIcon {
    height: 1rem;
    width: 1rem;
}

.filtersGrid {
    display: grid;
    gap: 1rem;
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 768px) {
    .filtersGrid {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

@media (min-width: 1024px) {
    .filtersGrid {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
}

.filterGroup {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.card {
    background-color: var(--card);
    border-radius: 0.5rem;
    border: 1px solid var(--border);
    overflow: hidden;
}

.cardHeader {
    padding: 1.5rem 1.5rem 0.5rem 1.5rem;
}

.cardTitle {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.cardDescription {
    color: var(--muted-foreground);
    font-size: 0.875rem;
}

.cardContent {
    padding: 1.5rem;
}

.tableContainer {
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
    min-width: 1200px;
}

.tableHeader {
    background-color: var(--muted);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.tableHead {
    padding: 0.75rem;
    text-align: left;
    color: var(--muted-foreground);
    font-weight: 500;
    white-space: nowrap;
}

.tableRow {
    border-bottom: 1px solid var(--border);
}

.tableRow:last-child {
    border-bottom: none;
}

.tableRow:hover {
    background-color: var(--muted);
}

.tableCell {
    padding: 0.75rem;
    font-size: 0.875rem;
    vertical-align: top;
}

.sortButton {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    background: none;
    border: none;
    padding: 0;
    color: inherit;
    font: inherit;
    cursor: pointer;
    text-align: left;
}

.sortButton:hover {
    color: var(--foreground);
}

.sortIcon {
    display: flex;
    align-items: center;
    margin-left: 0.5rem;
}

.chevron {
    height: 0.875rem;
    width: 0.875rem;
}

.chevronPlaceholder {
    height: 0.875rem;
    width: 0.875rem;
}

.idCode {
    font-family: monospace;
    background-color: var(--muted);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
}

.timestampContainer {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
}

.timestampTime {
    font-size: 0.75rem;
    color: var(--muted-foreground);
}

.functionCode {
    font-family: monospace;
    background-color: var(--muted);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    color: var(--primary);
}

.messageContainer {
    max-width: 300px;
    word-wrap: break-word;
    line-height: 1.4;
}

.dataDetails {
    cursor: pointer;
}

.dataSummary {
    color: var(--primary);
    font-size: 0.75rem;
    cursor: pointer;
    user-select: none;
}

.dataSummary:hover {
    text-decoration: underline;
}

.dataContent {
    margin-top: 0.5rem;
    background-color: var(--muted);
    padding: 0.75rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    max-width: 400px;
    overflow-x: auto;
    white-space: pre-wrap;
}

.noData {
    color: var(--muted-foreground);
    font-style: italic;
}

/* Log Level Badge Styles */
.errorBadge {
    background-color: var(--destructive-light);
    color: var(--destructive);
    border: 1px solid var(--destructive);
}

.warningBadge {
    background-color: #fef3c7;
    color: #d97706;
    border: 1px solid #f59e0b;
}

.infoBadge {
    background-color: var(--primary-light);
    color: var(--primary);
    border: 1px solid var(--primary);
}

.debugBadge {
    background-color: #e0e7ff;
    color: #6366f1;
    border: 1px solid #8b5cf6;
}

.defaultBadge {
    background-color: var(--muted);
    color: var(--muted-foreground);
    border: 1px solid var(--border);
}

/* Dark theme adjustments */
.dark .warningBadge {
    background-color: #451a03;
    color: #fbbf24;
}

.dark .debugBadge {
    background-color: #312e81;
    color: #a5b4fc;
}

.tableControls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border);
}

.pageSizeSelector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pageSizeSelect {
    width: 80px;
}

.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border);
    gap: 1rem;
}

.paginationInfo {
    font-size: 0.875rem;
    color: var(--muted-foreground);
}

.paginationControls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.paginationButton {
    display: inline-flex;
    align-items: center;
    font-size: 0.875rem;
    min-width: 80px;
    justify-content: center;
}

.paginationIcon {
    height: 1rem;
    width: 1rem;
}

@media (max-width: 768px) {
    .tableControls {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .pagination {
        flex-direction: column;
        gap: 1rem;
    }

    .paginationControls {
        flex-wrap: wrap;
        justify-content: center;
    }
}

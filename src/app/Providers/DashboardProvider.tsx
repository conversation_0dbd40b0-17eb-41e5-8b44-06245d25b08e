import {UserProvider} from "@/context/user/UserContext";
import {TransactionProvider} from "@/context/wallet/TransactionContext";
import {WalletProvider} from "@/context/wallet/WalletContext";
import {WalletItemsProvider} from "@/context/wallet/WalletItems";
import {ReactNode} from "react";

type Props = {
    children: ReactNode;
};

export default function DashboardProvider({children}: Props) {
    return (
        <UserProvider>
            <WalletProvider>
                <WalletItemsProvider>
                    <TransactionProvider>{children}</TransactionProvider>
                </WalletItemsProvider>
            </WalletProvider>
        </UserProvider>
    );
}

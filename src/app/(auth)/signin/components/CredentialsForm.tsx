"use client";

import {CssTextField} from "@/app/(auth)/signup/components/registerStyle";
import Loading from "@/components/Loading";
import {Button} from "@/styles/components/Ui";
import {Visibility, VisibilityOff} from "@mui/icons-material";
import {IconButton, InputAdornment} from "@mui/material";
import {signIn} from "next-auth/react";
import Link from "next/link";
import {useRouter, useSearchParams} from "next/navigation";

import {useState} from "react";
import {useTranslation} from "react-i18next";
import {toast} from "react-toastify";

export default function CredentialsForm() {
    const {t} = useTranslation();
    const [loading, setLoading] = useState(false);
    const [showpassword, setShowPassword] = useState(false);
    const [loginInfo, setLoginInfo] = useState({
        identifier: {
            value: "",
        },
        password: {
            value: "",
        },
    });
    const searchParams = useSearchParams();
    const router = useRouter();
    const callback = searchParams?.get("callback");

    const errorMessage = (message) =>
        toast.error(message, {
            position: "top-right",
        });

    function handleChange(e) {
        setLoginInfo({
            ...loginInfo,
            [e.target.name]: {
                value: e.target.value,
            },
        });
    }

    async function handleform(e) {
        e.preventDefault();
        setLoading(true);
        try {
            const res = await signIn("credentials", {
                identifier: loginInfo["identifier"].value,
                password: loginInfo["password"].value,
                redirect: false,
            });

            if (res?.error && res.error.length > 0) {
                throw new Error(t("authflow.invalidcredentials"));
            }

            router.push(callback || "/dashboard/wallet");
        } catch (e: any) {
            errorMessage(e.message);
        } finally {
            setLoading(false);
        }
    }

    return (
        <div className="flex items-center justify-center" style={{minHeight: 297}}>
            {loading ? (
                <Loading />
            ) : (
                <form onSubmit={handleform} className="w-[100%]">
                    <div className="pt-5 pb-5">
                        <h5>{t("authflow.login")}</h5>
                        <div className="pt-4 pb-4">
                            <CssTextField
                                label={t("authflow.email")}
                                variant="outlined"
                                value={loginInfo["identifier"].value}
                                required={true}
                                onChange={handleChange}
                                // error={''}
                                name={"identifier"}
                                type="email"
                            />
                        </div>
                        <div className="pt-4 pb-4">
                            <CssTextField
                                label={t("authflow.password")}
                                variant="outlined"
                                value={loginInfo["password"].value}
                                required={true}
                                onChange={handleChange}
                                // error={''}
                                name={"password"}
                                type={showpassword ? "text" : "password"}
                                InputProps={{
                                    endAdornment: (
                                        <InputAdornment position="end">
                                            <IconButton aria-label="toggle password visibility" onClick={() => setShowPassword(!showpassword)} onMouseDown={(e) => e.preventDefault()} edge="end">
                                                {showpassword ? <VisibilityOff /> : <Visibility />}
                                            </IconButton>
                                        </InputAdornment>
                                    ),
                                }}
                            />
                        </div>

                        <p className="text-center">
                            <Link style={{fontSize: 12}} href={"/forgot-password"}>
                                {t("authflow.forgot")}
                            </Link>
                        </p>

                        <Button>{t("authflow.dologin")}</Button>

                        <p style={{fontSize: 13}} className="pt-6 pb-2 text-center">
                            {t("authflow.newuser")}{" "}
                            <Link style={{fontSize: 12}} href={"/signup"}>
                                {t("authflow.createaccount")}
                            </Link>
                        </p>
                    </div>
                </form>
            )}
        </div>
    );
}

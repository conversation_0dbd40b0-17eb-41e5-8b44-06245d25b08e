.container {
    flex: 1;
    padding: 2rem;
    padding-top: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.header {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.title {
    font-size: 1.875rem;
    font-weight: 700;
    letter-spacing: -0.025em;
}

.description {
    color: var(--muted-foreground);
    font-size: 1rem;
}

.card {
    background-color: var(--card);
    border-radius: 0.5rem;
    border: 1px solid var(--border);
    overflow: hidden;
}

.cardHeader {
    padding: 1.5rem 1.5rem 0.5rem 1.5rem;
}

.cardTitle {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.cardDescription {
    color: var(--muted-foreground);
    font-size: 0.875rem;
}

.cardContent {
    padding: 1.5rem;
}

.settingsForm {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.sectionTitle {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--foreground);
    border-bottom: 1px solid var(--border);
    padding-bottom: 0.5rem;
}

.settingsGrid {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 768px) {
    .settingsGrid {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

@media (min-width: 1024px) {
    .settingsGrid {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
}

.formGroup {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.formLabel {
    font-weight: 500;
    font-size: 0.875rem;
}

.formInput {
    width: 100%;
}

.formHelp {
    font-size: 0.75rem;
    color: var(--muted-foreground);
    line-height: 1.4;
}

.selectTrigger {
    width: 100%;
    background-color: var(--background);
    border: 1px solid var(--border);
}

.selectTrigger:focus {
    border-color: var(--ring);
    outline: none;
    box-shadow: 0 0 0 2px var(--ring);
}

.formActions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border);
}

.saveButton {
    min-width: 120px;
}

.summaryGrid {
    display: grid;
    gap: 1rem;
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 768px) {
    .summaryGrid {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

@media (min-width: 1024px) {
    .summaryGrid {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
}

.summaryItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background-color: var(--muted);
    border-radius: 0.375rem;
}

.summaryLabel {
    font-size: 0.875rem;
    color: var(--muted-foreground);
}

.summaryValue {
    font-weight: 600;
    font-size: 0.875rem;
}

"use client";

import {useEffect, useState} from "react";
import {Button} from "@/components/ui/button";
import {Input} from "@/components/ui/input";
import {Label} from "@/components/ui/label";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select";
import styles from "./page.module.css";
import axios from "axios";

interface SettingsData {
    checkAgendaStatusInterval: number;
    priceBatchSize: number;
    priceChunkSize: number;
    dividendsBatchSize: number;
    fundamentalDataBatchSize: number;
    processS3FilesBatchSize: number;
    splitsBatchSize: number;
    epsBatchSize: number;
    logLevel: "ERROR" | "WARNING" | "INFO" | "DEBUG";
    jobLogsRetentionDays: number;
}

const defaultSettings: SettingsData = {
    checkAgendaStatusInterval: 30,
    priceBatchSize: 30,
    priceChunkSize: 1000,
    dividendsBatchSize: 10,
    fundamentalDataBatchSize: 10,
    processS3FilesBatchSize: 10,
    splitsBatchSize: 10,
    epsBatchSize: 5,
    logLevel: "WARNING",
    jobLogsRetentionDays: 30,
};

const settingsLabels = {
    checkAgendaStatusInterval: "Check Agenda Status Interval",
    priceBatchSize: "Price Batch Size",
    priceChunkSize: "Price Chunk Size",
    dividendsBatchSize: "Dividends Batch Size",
    fundamentalDataBatchSize: "Fundamental Data Batch Size",
    processS3FilesBatchSize: "Process S3 Files Batch Size",
    splitsBatchSize: "Splits Batch Size",
    epsBatchSize: "EPS Batch Size",
    logLevel: "Log Level",
    jobLogsRetentionDays: "Job Logs Retention Days",
};

const settingsDescriptions = {
    checkAgendaStatusInterval: "How often to check Agenda status (in seconds)",
    priceBatchSize: "Number of price records to process in each batch",
    priceChunkSize: "Size of each price data chunk for processing",
    dividendsBatchSize: "Number of dividend records to process in each batch",
    fundamentalDataBatchSize: "Number of fundamental data records to process in each batch",
    processS3FilesBatchSize: "Number of S3 files to process in each batch",
    splitsBatchSize: "Number of stock split records to process in each batch",
    epsBatchSize: "Number of EPS records to process in each batch",
    logLevel: "Minimum level of logs to display and store",
    jobLogsRetentionDays: "Number of days to retain job logs before automatic cleanup",
};

export default function SettingsPage() {
    const [settings, setSettings] = useState<SettingsData>(defaultSettings);
    const [hasChanges, setHasChanges] = useState(false);
    const [isSaving, setIsSaving] = useState(false);

    const handleNumberChange = (key: keyof SettingsData, value: string) => {
        const numValue = Number.parseInt(value) || 1;
        if (numValue >= 1) {
            setSettings((prev) => ({...prev, [key]: numValue}));
            setHasChanges(true);
        }
    };

    const handleLogLevelChange = (value: string) => {
        setSettings((prev) => ({...prev, logLevel: value as SettingsData["logLevel"]}));
        setHasChanges(true);
    };

    const handleSave = async () => {
        setIsSaving(true);
        try {
            await axios.put("/api/agenda/settings", settings);
            console.log("Settings saved successfully");
            setHasChanges(false);
        } catch (error) {
            console.error("Failed to save settings:", error);
        } finally {
            setIsSaving(false);
        }
    };

    const handleReset = () => {
        setSettings(defaultSettings);
        setHasChanges(true);
    };

    const fetchAgendaSettings = async () => {
        try {
            const response = await axios.get("/api/agenda/settings");
            delete response.data.agendaStartDateTime;
            delete response.data._id;
            setSettings(response.data);
        } catch (error) {
            console.error("Failed to fetch agenda settings:", error);
        }
    };

    useEffect(() => {
        fetchAgendaSettings();
    }, []);

    return (
        <div className={styles.container}>
            <div className={styles.header}>
                <h2 className={styles.title}>Settings</h2>
                <p className={styles.description}>Configure system settings and batch processing parameters</p>
            </div>

            <div className={styles.card}>
                <div className={styles.cardHeader}>
                    <h3 className={styles.cardTitle}>System Configuration</h3>
                    <p className={styles.cardDescription}>Adjust batch sizes and processing intervals</p>
                </div>
                <div className={styles.cardContent}>
                    <form
                        className={styles.settingsForm}
                        onSubmit={(e) => {
                            e.preventDefault();
                            handleSave();
                        }}>
                        {/* Batch Size Settings */}
                        <div className={styles.section}>
                            <h4 className={styles.sectionTitle}>Batch Processing Settings</h4>
                            <div className={styles.settingsGrid}>
                                {Object.entries(settings)
                                    .filter(([key]) => key !== "logLevel")
                                    .map(([key, value]) => (
                                        <div key={key} className={styles.formGroup}>
                                            <Label htmlFor={key} className={styles.formLabel}>
                                                {settingsLabels[key as keyof typeof settingsLabels]}
                                            </Label>
                                            <Input
                                                id={key}
                                                type="number"
                                                min="1"
                                                value={value}
                                                onChange={(e) => handleNumberChange(key as keyof SettingsData, e.target.value)}
                                                className={styles.formInput}
                                            />
                                            <p className={styles.formHelp}>{settingsDescriptions[key as keyof typeof settingsDescriptions]}</p>
                                        </div>
                                    ))}
                            </div>
                        </div>

                        {/* Log Level Setting */}
                        <div className={styles.section}>
                            <h4 className={styles.sectionTitle}>Logging Configuration</h4>
                            <div className={styles.formGroup}>
                                <Label htmlFor="logLevel" className={styles.formLabel}>
                                    {settingsLabels.logLevel}
                                </Label>
                                <Select value={settings.logLevel} onValueChange={handleLogLevelChange}>
                                    <SelectTrigger className={styles.selectTrigger}>
                                        <SelectValue placeholder="Select log level" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="ERROR">Error</SelectItem>
                                        <SelectItem value="WARNING">Warning</SelectItem>
                                        <SelectItem value="INFO">Info</SelectItem>
                                        <SelectItem value="DEBUG">Debug</SelectItem>
                                    </SelectContent>
                                </Select>
                                <p className={styles.formHelp}>{settingsDescriptions.logLevel}</p>
                            </div>
                        </div>

                        {/* Action Buttons */}
                        <div className={styles.formActions}>
                            <Button type="button" variant="outline" onClick={handleReset} disabled={isSaving}>
                                Reset to Defaults
                            </Button>
                            <Button type="submit" disabled={!hasChanges || isSaving} className={styles.saveButton}>
                                {isSaving ? "Saving..." : "Save Settings"}
                            </Button>
                        </div>
                    </form>
                </div>
            </div>

            {/* Current Settings Summary */}
            <div className={styles.card}>
                <div className={styles.cardHeader}>
                    <h3 className={styles.cardTitle}>Current Configuration</h3>
                    <p className={styles.cardDescription}>Overview of current system settings</p>
                </div>
                <div className={styles.cardContent}>
                    <div className={styles.summaryGrid}>
                        {Object.entries(settings).map(([key, value]) => (
                            <div key={key} className={styles.summaryItem}>
                                <span className={styles.summaryLabel}>{settingsLabels[key as keyof typeof settingsLabels]}:</span>
                                <span className={styles.summaryValue}>{typeof value === "string" ? value.charAt(0).toUpperCase() + value.slice(1) : value}</span>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
}

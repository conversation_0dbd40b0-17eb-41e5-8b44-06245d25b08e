"use client";
import Loading from "@/components/Loading";
import {useWallets} from "@/context/wallet/WalletContext";
import {PageTitle} from "@/styles/components/Ui";
import {AccountBalance, AddCircleOutlineRounded, TrendingDown, TrendingUp} from "@mui/icons-material";
import Image from "next/image";
import {useRouter} from "next/navigation";
import {useEffect, useState} from "react";
import {useTranslation} from "react-i18next";
import NewWalletModal from "../components/NewWalletModal";

export default function WalletScreen() {
    const [isLoading, setIsLoading] = useState(false);
    const {wallets, fetchWallets} = useWallets();
    const [newWalletModal, setNewWalletModal] = useState(false);

    const {t} = useTranslation();

    const {push} = useRouter();

    useEffect(() => {
        async function fetchData() {
            setIsLoading(true);
            await fetchWallets();
            setIsLoading(false);
        }
        fetchData();
    }, [setIsLoading, fetchWallets]);

    return (
        <div className="md:container lg:container sm:container container mx-auto">
            <NewWalletModal isOpen={newWalletModal} setIsOpen={setNewWalletModal} />
            <PageTitle className="px-2">{t("footer.wallet")}</PageTitle>
            {isLoading ? (
                <div className="flex items-center justify-center w-full h-[60vh]">
                    <Loading />
                </div>
            ) : wallets?.length === 0 ? (
                <div className="flex flex-col md:flex-row lg:flex-row sm:flex-row">
                    <div className="flex items-center justify-center w-full h-[60vh]">
                        <div className="flex flex-col items-center">
                            <Image src="/imgs/wallet.png" alt="wallet" width={160} height={160} />
                            <h3 className="text-center mt-4">{t("wallet.no_wallet")}</h3>
                            <button className="btn btn-primary mt-4 bg-primary px-5 py-2 text-[18px] font-[600] uppercase rounded-[20px]" onClick={() => setNewWalletModal(true)}>
                                {t("wallet.new_wallet")}
                            </button>
                        </div>
                    </div>
                </div>
            ) : (
                <>
                    <div className="grid sm:grid-cols-3 grid-cols-1  gap-4 p-2">
                        <div
                            className="w-full h-[200px] rounded-[20px] cursor-pointer shadow-xl shadow-cyan-500/50 bg-primary flex items-center justify-center"
                            onClick={() => setNewWalletModal(true)}>
                            <AddCircleOutlineRounded sx={{fontSize: 80, color: "#FFF"}} />
                        </div>
                        {wallets &&
                            wallets?.length > 0 &&
                            wallets?.map((wallet, index) => {
                                return (
                                    <div
                                        className="w-full h-[200px] rounded-[20px] 
                    cursor-pointer shadow-xl shadow-cyan-500/50
                  flex flex-col justify-between p-[15px]"
                                        onClick={() => push(`/dashboard/wallet/${wallet?.uuid}`)}
                                        key={index}>
                                        <div className="flex flex-row justify-between py-2 w-full items-center">
                                            <h5 className="text-ellipsis">{wallet.name}</h5>
                                            <span className="text-primaryShadow uppercase">{wallet.currency}</span>
                                        </div>
                                        <div className="flex flex-row justify-between py-2 w-full items-center">
                                            <div>
                                                <span className="flex  flex-row items-center">
                                                    <AccountBalance sx={{color: "#25A9AD"}} /> &nbsp;
                                                    {wallet.patrimony?.toFixed(2) || 0}
                                                </span>
                                                <span>
                                                    {wallet.yield && wallet.yield >= 0 ? <TrendingUp sx={{color: "green", fontSize: 20}} /> : <TrendingDown sx={{color: "red", fontSize: 20}} />}
                                                    &nbsp; % {wallet.yield || 0}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                );
                            })}
                    </div>
                </>
            )}
        </div>
    );
}

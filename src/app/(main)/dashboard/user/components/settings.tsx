import { SubTabButton } from "@/components/Layout/SubTabButton";
import { useFilters } from "@/context/stocks/FiltersContext";
import { Touch } from "@/styles/components/Ui";
import { SelectedFilters } from "@/types/api/FiltersResponse";
import { FiltersSaved } from "@/types/api/FIltersSaved";
import { ArrowForwardIos, DeleteOutline } from "@mui/icons-material";
import { TabComponent } from "@syncfusion/ej2-react-navigations";
import axios from "axios";
import { t } from "i18next";
import { signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { toast } from "react-toastify";

export default function Settings() {
    const tabObj = useRef<TabComponent>(null);

    const [isLoading, setIsLoading] = useState(false);
    const [filters, setFilters] = useState<FiltersSaved[]>([]);

    const {setSelectedFilters} = useFilters();

    const router = useRouter();

    let headerText: any = [{text: t("filters.filters")}];

    const errorMessage = (message) =>
        toast.error(message, {
            position: "top-right",
        });

    useEffect(() => {
        async function fetchData() {
            try {
                setIsLoading(true);
                const {data} = await axios.get("/api/stocks/filters/saved");

                const res: FiltersSaved[] = data;

                setFilters(res);
            } catch (e: any) {
                let message = "";

                if (e.response.status === 401 || e.response.status === 403) {
                    message = t("error.session_expired");
                    setTimeout(
                        () =>
                            signOut({
                                redirect: true,
                                callbackUrl: "/signin",
                            }),
                        3000,
                    );
                } else {
                    message = e.message;
                }
                errorMessage(message);
            } finally {
                setIsLoading(false);
            }
        }

        fetchData();
    }, []);

    function changeFilter(payload: SelectedFilters) {
        const filters = {...payload};
        setSelectedFilters(filters);

        router.push("/stocks");
    }

    async function deleteFilter(id) {
        const copy = [...filters];

        try {
            setIsLoading(true);
            const res = copy.filter((item) => item.id !== id);
            await axios.delete(`/api/stocks/filters/${id}`);

            setFilters(res);
        } catch (e: any) {
            let message = "";

            if (e.response.status === 401 || e.response.status === 403) {
                message = t("error.session_expired");
                setTimeout(
                    () =>
                        signOut({
                            redirect: true,
                            callbackUrl: "/signin",
                        }),
                    3000,
                );

                setFilters(copy);
            } else {
                message = e.message;
            }
            errorMessage(message);
        } finally {
            setIsLoading(false);
        }
    }

    return (
        <TabComponent id="default" heightAdjustMode="Fill" width="100%" ref={tabObj} headerPlacement="Top" overflowMode="Popup" cssClass="mt-[-10px]">
            <SubTabButton action={() => tabObj.current?.select(0)} label={headerText[0].text} is_active={true} />

            <ul className="py-8">
                {filters?.length > 0 &&
                    filters?.map((filter) => (
                        <li
                            key={filter.id}
                            className="
                                w-full 
                                h-[30px] 
                                m-2 
                                pl-2 
                                flex items-center justify-between
                                border-b-primary border-b-[0.5px]
                                ">
                            <Touch onClick={() => changeFilter(filter.payload)}>
                                <span className="uppercase text-contrastText bold">{filter.name}</span>
                            </Touch>
                            <div className="flex items-center">
                                <div className="w-[26px]">
                                    <Touch onClick={() => deleteFilter(filter.id)}>
                                        <DeleteOutline color="secondary" sx={{fontSize: 20}} />
                                    </Touch>
                                </div>
                                <div className="w-[26px]">
                                    <Touch onClick={() => changeFilter(filter.payload)}>
                                        <ArrowForwardIos color="primary" />
                                    </Touch>
                                </div>
                            </div>
                        </li>
                    ))}
            </ul>
        </TabComponent>
    );
}

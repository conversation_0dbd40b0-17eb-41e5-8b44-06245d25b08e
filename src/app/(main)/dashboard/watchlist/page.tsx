"use client";

import Container from "@/components/Container";
import {PageTitle} from "@/components/Layout/PageTitle";
import {Favorite, FavoriteBorder} from "@mui/icons-material";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import axios from "axios";

import Loading from "@/components/Loading";
import WarningModal from "@/components/Modal/WarningModal";
import {TickerResponse} from "@/types/api/TickerResponse";
import {WatchListType} from "@/types/context/WatchListContextType";
import {StrapiDefaultResponseArray} from "@/types/utils/strapiAPI";
import {signOut} from "next-auth/react";
import {useCallback, useEffect, useState} from "react";
import {useTranslation} from "react-i18next";
import {toast} from "react-toastify";
import styles from "./styles.module.css";

export default function WatchListItems() {
    const [isLoading, setisLoading] = useState(false);
    const [list, setList] = useState<WatchListType[]>([]);
    const [warningModal, setWarningModal] = useState(false);
    const [message, setMessage] = useState("");
    const [id, setId] = useState(0);
    const {t} = useTranslation();
    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: 10,
    });

    const [totalItems, setTotalItems] = useState({
        rowCount: 0,
        totalPages: 0,
    });

    const errorMessage = (message) =>
        toast.error(message, {
            position: "top-right",
        });

    const fetchWatchList = useCallback(
        async ({pageIndex = 0, pageSize = 10}) => {
            setisLoading(true);

            try {
                const {data} = await axios.get(`/api/stocks/watchlist?page=${pageIndex}&pageSize=${pageSize}`);

                const res: StrapiDefaultResponseArray = data;

                const list: WatchListType[] = res.data.map((item) => {
                    const price = item.attributes.price;
                    const current_price = item.attributes.statistics_of_ticker.data.attributes.price || 0;

                    const calc = (current_price * 100) / price - 100;

                    const ticker: TickerResponse = item.attributes.statistics_of_ticker.data.attributes.ticker_internal.data.attributes;
                    const ticker_id: number = item.attributes.statistics_of_ticker.data.attributes.ticker_internal.data.id;

                    return {
                        id: item.id,
                        name: ticker.name || "",
                        symbol_code: item.attributes.statistics_of_ticker.data.attributes.symbol_code || 0,
                        price,
                        createdAt: item.attributes.createdAt,
                        updatedAt: item.attributes.updatedAt,
                        is_enable: item.attributes.is_enable,
                        current_price,
                        after_added: calc,
                        ticker_id,
                    };
                });

                setList(list);
                setisLoading(false);
                if (pageIndex !== pagination.pageIndex || pageSize !== pagination.pageSize) {
                    setPagination({
                        pageIndex,
                        pageSize,
                    });
                }
                setTotalItems({
                    rowCount: res.meta.pagination.total,
                    totalPages: res.meta.pagination.pageCount,
                });
            } catch (e: any) {
                let message = "";

                if (e.response.status === 401 || e.response.status === 403) {
                    message = t("error.session_expired");
                    setTimeout(
                        () =>
                            signOut({
                                redirect: true,
                                callbackUrl: "/signin",
                            }),
                        3000,
                    );
                } else {
                    message = e.message;
                }
                errorMessage(message);
            } finally {
                setisLoading(false);
            }
        },
        [setList, setPagination, setisLoading, setTotalItems, t],
    );

    useEffect(() => {
        if (pagination?.pageIndex === 0) setPagination({pageIndex: 1, pageSize: 10});
    }, [pagination.pageIndex]);

    useEffect(() => {
        if (pagination.pageIndex >= 1) {
            fetchWatchList({pageIndex: pagination.pageIndex, pageSize: pagination.pageSize});
        }
    }, [fetchWatchList, pagination.pageIndex, pagination.pageSize]);

    const confirmModal = useCallback(
        (id: number, name: string) => {
            setMessage(`${t("watchlist.remove_watchlist_first")} ${name} ${t("watchlist.remove_watchlist_final")}`);
            setId(id);
            setWarningModal(true);
        },
        [t],
    );

    function closeModal() {
        setId(0);
        setWarningModal(false);
        setMessage("");
    }

    async function handleFavorite(id) {
        try {
            setisLoading(true);
            await axios(`/api/stocks/watchlist/${id}/status`);

            const copy = [...list];

            const findIndex = list.findIndex((item) => item.id === id);

            if (findIndex !== -1) {
                copy.splice(findIndex, 1);

                setList(copy);
            }

            setWarningModal(false);
        } catch (e: any) {
            let message = "";

            if (e.response.status === 401 || e.response.status === 403) {
                message = t("error.session_expired");
                setTimeout(
                    () =>
                        signOut({
                            redirect: true,
                            callbackUrl: "/signin",
                        }),
                    3000,
                );
            } else {
                message = e.message;
            }
            errorMessage(message);
        } finally {
            setisLoading(false);
            setId(0);
            setMessage("");
        }
    }

    return (
        <Container>
            <PageTitle title={t("watchlist.title")} />
            <WarningModal isOpen={warningModal} handleClose={closeModal} handleConfirm={() => handleFavorite(id)} title={t("watchlist.title")} message={message} />
            <div className={`${styles.tableContainer}`}>
                {isLoading ? (
                    <Loading />
                ) : (
                    <table className={styles.table}>
                        <thead>
                            <tr className={`${styles.tableHeader}`}>
                                <th className={styles.headerCell}>{t("watchlist.symbol_code")}</th>
                                <th className={styles.headerCell}>{t("watchlist.name")}</th>
                                <th className={styles.headerCell}>{t("watchlist.current_price")}</th>
                                <th className={styles.headerCell}>{t("watchlist.price")}</th>
                                <th className={styles.headerCell}>{t("watchlist.after_added")}</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            {list.map((item, index) => {
                                const {id, ticker_id} = item;

                                return (
                                    <tr key={index} className={index % 2 === 0 ? styles.rowEven : styles.rowOdd}>
                                        <td className={styles.indicator}>{item["symbol_code"]?.split(".")[0]}</td>

                                        <td className={styles.valueCell}>{item["name"]}</td>

                                        <td className={styles.valueCell}>{item["current_price"]}</td>

                                        <td className={styles.valueCell}>{item["price"]}</td>

                                        <td className={`${styles.valueCell} ${styles.badge}`}>
                                            <span className={`${styles.badge} ${item["after_added"] >= 0 ? styles.badgeUp : styles.badgeDown}`}>{item["after_added"]} %</span>
                                        </td>
                                        <td className={styles.valueCell}>
                                            <div onClick={() => confirmModal(id, item["name"])} className="cursor-pointer">
                                                {item["is_enable"] ? <Favorite color="error" fontSize="large" /> : <FavoriteBorder color="error" fontSize="large" />}
                                            </div>
                                        </td>
                                    </tr>
                                );
                            })}
                        </tbody>
                    </table>
                )}
                <div className={styles.paginationContainer}>
                    <div className={`${styles.paginationText} text-sm text-gray-500 font-poppins`}>
                        {Math.min((pagination.pageSize + 1) * totalItems.rowCount, totalItems.rowCount)} {t("filters.of")} {totalItems.rowCount} {t("filters.items")}
                    </div>

                    <div className="flex items-center space-x-3">
                        <button
                            onClick={() => setPagination({pageIndex: pagination.pageIndex - 1, pageSize: pagination.pageSize})}
                            disabled={pagination.pageIndex === 0}
                            className={styles.paginationButton}>
                            <ChevronLeftIcon className="h-4 w-4" />
                            <span className="sr-only">Previous</span>
                        </button>
                        {Array.from({length: Math.min(5, totalItems.totalPages)}).map((_, index) => {
                            let pageNumber: number;

                            if (totalItems.totalPages <= 5) {
                                // If we have 5 or fewer pages, show all
                                pageNumber = index;
                            } else if (pagination.pageIndex < 3) {
                                // If we're near the start, show first 5 pages
                                pageNumber = index;
                            } else if (pagination.pageIndex > totalItems.totalPages - 4) {
                                // If we're near the end, show last 5 pages
                                pageNumber = totalItems.totalPages - 5 + index;
                            } else {
                                // Otherwise show 2 before and 2 after current page
                                pageNumber = pagination.pageIndex - 2 + index;
                            }

                            return (
                                <button
                                    key={pageNumber}
                                    onClick={() => setPagination({pageIndex: pageNumber, pageSize: pagination.pageSize})}
                                    className={pagination.pageIndex === pageNumber ? styles.activePaginationButton : styles.paginationButton}>
                                    {pageNumber + 1}
                                </button>
                            );
                        })}
                        <button
                            onClick={() => setPagination({pageIndex: pagination.pageIndex + 1, pageSize: pagination.pageSize})}
                            disabled={pagination.pageIndex === totalItems.totalPages - 1}
                            className={styles.paginationButton}>
                            <ChevronRightIcon className="h-4 w-4" />
                            <span className="sr-only">Next</span>
                        </button>
                    </div>

                    <div className="flex items-center space-x-2">
                        <span className={`${styles.itemsPage} text-gray-500 font-poppins`}>{t("filters.items_page")}:</span>
                        <select
                            value={pagination.pageSize}
                            onChange={(e) => {
                                setPagination({
                                    pageSize: parseInt(e.target.value, 10),
                                    pageIndex: 0,
                                }); // Reset to first page when changing items per page
                            }}
                            className={styles.pageSelector}>
                            <option value={5}>5</option>
                            <option value={10}>10</option>
                            <option value={20}>20</option>
                            <option value={50}>50</option>
                            <option value={100}>100</option>
                        </select>
                    </div>
                </div>
            </div>
        </Container>
    );
}

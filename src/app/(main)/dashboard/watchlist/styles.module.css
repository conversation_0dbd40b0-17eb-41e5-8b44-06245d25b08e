.tableContainer {
    background-color: rgb(var(--color-sf-surface));
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow-x: auto;
  }
  
  .table {
    width: 100%;
    min-width: 800px;
  }
  
  .tableHeader {
    border-bottom: 1px solid #e5e7eb;
  }
  
  .indicator {
    padding-left: 1rem;
    text-align: left;
    font-size: 1.4rem;
    font-weight: 600;
    color: rgb(var(--color-text-title));
    background-color: rgb(var(--color-sf-button-gray));
  }

  .headerCell {
    padding: 1rem 1.5rem;
    text-align: left;
    font-size: 1.4rem;
    font-weight: 600;
    color: rgb(var(--color-text-title));
  }
  
  .rowEven {
    background-color: #f9fafb;
  }
  
  .rowOdd {
    background-color: white;
  }
  
  .itemCell {
    padding: 1rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: rgb(var(--color-text-title));
  }
  
  .valueCell {
    padding: 1rem 1.5rem;
    font-size: 1.4rem;
    color: #6b7280;
  }

  .badge {
    padding: 0.1rem 1rem;
    border-radius: 1.2rem;
    font-weight: 600;
    /* display: flex; */
    /* align-items: center;
    justify-content: center;
    width: 60px; */
  }
  
  .badgeUp {
    background-color: #13DCD2;
  }

  .badgeDown {
    background-color: red;
  } 

  .paginationContainer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
  }

  .paginationText {
    font-size: 1.3rem;
    color: #4b5563;
  }
  
  .pageSelector {
    height: 2rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    padding: 0 0.5rem;
  }
"use client";

import Loading from "@/components/Loading";
import {useSession} from "next-auth/react";
import {useParams, useRouter} from "next/navigation";
import {useEffect, useState} from "react";

export default function Subscribe() {
    const {status} = useSession();
    const params: any = useParams();
    const {priceId} = params;
    const router = useRouter();
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        const fetchData = async () => {
            if (priceId && status === "authenticated") {
                const response = await fetch(`/api/stripe/${priceId}`, {
                    method: "GET",
                    headers: {
                        "Content-Type": "application/json",
                    },
                });
                if (response && response.ok) {
                    const data = await response.json();
                    if (data) {
                        setIsLoading(false);
                        return router.push(data);
                    }
                }
                setIsLoading(false);
                return router.push(`/signin?callback=/subscribe/${priceId}`);
            } else {
                if (status === "unauthenticated") {
                    setIsLoading(false);
                    return router.push(`/signin?callback=/subscribe/${priceId}`);
                }
            }
        };
        fetchData();
    }, [status]);

    return <div className="mt-10">{isLoading ? <Loading /> : <></>}</div>;
}

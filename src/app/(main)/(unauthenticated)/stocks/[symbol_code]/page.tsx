"use client";

import {DashboardStock} from "@/app/(main)/(unauthenticated)/stocks/[symbol_code]/components/Dashboard";
import Container from "@/components/Container";
import {useDocuments} from "@/context/stocks/DocumentsContext";
import {useStatistics} from "@/context/stocks/StatisticsContext";
import {BalanceSheetResponse} from "@/types/api/BalanceSheetResponse";
import {CashFlowResponse} from "@/types/api/CashFlowResponse";
import {DividendsResponse} from "@/types/api/DividendsResponse";
import {FilterCategory, FilterCategoryAttributes} from "@/types/api/FiltersResponse";
import {IncomeStatementResponse} from "@/types/api/IncomeStatementResponse";
import {PricesResponse} from "@/types/api/PricesResponse";
import {Assets, StatisticsHistoryResponse} from "@/types/api/StatisticsHistoryResponse";
import {StatisticsResponse} from "@/types/api/StatisticsResponse";
import {TickerResponse} from "@/types/api/TickerResponse";
import {FirstPriceType} from "@/types/context/PricesContext";
import {StrapiItem} from "@/types/utils/strapiAPI";
import moment from "moment";
import {useSession} from "next-auth/react";
import {useParams, useRouter} from "next/navigation";
import {useEffect, useState} from "react";
import {useTranslation} from "react-i18next";

type ApiStockResponse = {
    ticker: TickerResponse;
    statistics: StatisticsResponse;
};

const stages = {
    dashboard: "dashboard",
    documents: "documents",
};

export default function Page() {
    const {symbol_code} = useParams() as {symbol_code: string};
    const [ticker, setTicker] = useState<TickerResponse | null>(null);
    const [statistics, setStatistics] = useState<StatisticsResponse | null>(null);
    const [dividends, setDividends] = useState<DividendsResponse | null>(null);
    const [prices, setPrices] = useState<PricesResponse | null>(null);
    const [stage, setStage] = useState(stages.dashboard);
    const [firstPrice, setFirstPrice] = useState<FirstPriceType>({
        fisrt_price_month: 0,
        fisrt_price_year: 0,
    });
    const {t} = useTranslation();
    const {data: session, status} = useSession();
    const [id, setId] = useState<number | null>(null);
    const [isFavorite, setIsFavorite] = useState(false);
    const [loadingFavorite, setLoadingFavorite] = useState(false);
    const [categories, setCategories] = useState<FilterCategory[]>([]);
    const [statisticsHistory, setStatisticsHistory] = useState<StatisticsHistoryResponse[]>([]);
    const [assetsHistory, setAssetsHistory] = useState<Assets[]>([]);

    const router = useRouter();
    const {calcStatisticsHistory} = useStatistics();

    const {incomeStatementHolder, balanceSheetHolder, cashFlowHolder, setBalanceSheetHistory, setIncomeStatementHistory, setCashFlowHistory} = useDocuments();

    useEffect(() => {
        if (
            prices &&
            dividends &&
            Object.keys(incomeStatementHolder).length > 0 &&
            Object.keys(balanceSheetHolder).length > 0 &&
            Object.keys(cashFlowHolder).length > 0 &&
            statistics &&
            Object.keys(prices).length > 0 &&
            Object.keys(dividends).length > 0
        ) {
            const {statisticsHistory, assetsHistory} = calcStatisticsHistory({
                balance_sheet: balanceSheetHolder,
                cash_flow: cashFlowHolder,
                income_statement: incomeStatementHolder,
                prices,
                dividends,
                statistics,
            });

            setStatisticsHistory(statisticsHistory);
            setAssetsHistory(assetsHistory);
        }
    }, [setStatisticsHistory, calcStatisticsHistory, incomeStatementHolder, balanceSheetHolder, cashFlowHolder, prices, dividends, statistics]);

    useEffect(() => {
        async function fetchData() {
            try {
                if (symbol_code) {
                    const response = await fetch(`/api/stocks/${symbol_code}`);

                    const data: ApiStockResponse = await response.json();

                    const {ticker, statistics} = data;

                    const {id} = ticker;

                    setId(id || null);

                    setTicker(ticker);
                    setStatistics(statistics || null);

                    if (ticker.primary_ticker_eodhd) {
                        const res = await fetch(`/api/dividend_yield/${id}?symbol_code=${symbol_code}`);

                        const {dividends, prices}: {dividends: DividendsResponse; prices: PricesResponse} = await res.json();

                        setDividends(dividends);
                        setPrices(prices);

                        const current_date = new Date();

                        const current_year = current_date.getFullYear();

                        const current_month = moment(current_date).format("MM");

                        const response_prices = await fetch(`/api/price/${ticker?.primary_ticker_eodhd}/history?from=${current_year}-${current_month}-01`);

                        if (response_prices) {
                            const data = await response_prices?.json();

                            const {first_result_month, first_result_year} = data;

                            if (first_result_month && first_result_year) {
                                setFirstPrice({
                                    fisrt_price_month: first_result_month.close,
                                    fisrt_price_year: first_result_year.close,
                                });
                            }
                        }
                    }
                }
            } catch (e) {}
        }

        fetchData();
    }, [id]);

    useEffect(() => {
        async function checkWatchList() {
            try {
                setLoadingFavorite(true);

                const res = await fetch(`/api/stocks/${id}/watchlist`);

                if (res.ok) {
                    const data: any[] = await res.json();

                    if (data.length > 0) {
                        const item = data[0];

                        if (item?.attributes.is_enable) {
                            setIsFavorite(true);
                        } else {
                            setIsFavorite(false);
                        }
                    }
                }
            } catch (e) {
            } finally {
                setLoadingFavorite(false);
            }
        }

        if (status === "authenticated" && id) {
            checkWatchList();
        }
    }, [status, id]);

    useEffect(() => {
        async function fetchCategories() {
            const response = await fetch(`/api/stocks/filters/categories`);
            const data: StrapiItem[] = await response.json();

            const categories: FilterCategoryAttributes[] = data.map((item) => ({
                id: item.id,
                ...item.attributes,
                isEnabled: false,
            }));

            setCategories(categories);
        }

        fetchCategories();
    }, []);

    useEffect(() => {
        async function fetchStock() {
            if (id) {
                const res_balanceSheet = await fetch(`/api/stocks/${id}/documents/balance_sheet`);
                const res_cash_flow = await fetch(`/api/stocks/${id}/documents/cash_flow`);
                const res_income_statement = await fetch(`/api/stocks/${id}/documents/income_statement`);

                const balance_sheet: BalanceSheetResponse[] = await res_balanceSheet.json();
                const cash_flow: CashFlowResponse[] = await res_cash_flow.json();
                const income_statement: IncomeStatementResponse[] = await res_income_statement.json();

                setBalanceSheetHistory(balance_sheet);
                setCashFlowHistory(cash_flow);
                setIncomeStatementHistory(income_statement);
            }
        }

        fetchStock();
    }, [id, setBalanceSheetHistory, setCashFlowHistory, setIncomeStatementHistory, setStatistics, setTicker]);

    async function handleFavorite() {
        try {
            if (!session) {
                return router.push(`/signin?callback=dashboard/stocks/${id}`);
            }

            setLoadingFavorite(true);

            const favorite = !isFavorite;

            setIsFavorite(favorite);

            const res = await fetch(`/api/stocks/${id}/watchlist/save`);

            if (!res.ok) {
                setIsFavorite(!favorite);
            }
        } catch (e) {
        } finally {
            setLoadingFavorite(false);
        }
    }

    return (
        <Container>
            {stage === stages.dashboard && id && (
                <DashboardStock
                    ticker={ticker}
                    statistics={statistics}
                    loadingFavorite={loadingFavorite}
                    isFavorite={isFavorite}
                    dividends={dividends}
                    firstPrice={firstPrice}
                    current_price={statistics?.price || 0}
                    prices={prices}
                    handleFavorite={handleFavorite}
                    categories={categories}
                    id={id}
                    statisticsHistory={statisticsHistory}
                    assetsHistory={assetsHistory}
                />
            )}
        </Container>
    );
}

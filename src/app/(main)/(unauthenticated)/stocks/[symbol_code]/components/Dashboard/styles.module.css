.cardTitle {
    font-size: 1.6rem;
    font-weight: 600;
    color: rgb(var(--color-sf-text-title));
    margin-bottom: 1rem;
}

.categoriesButtons {
    width: 100%;
    background: rgb(var(--color-sf-white));
    padding: 1.5rem;
    border-radius: 15px;
    min-height: 100px;
    margin-top: 1.2rem;
    display: flex;
    justify-items: center;
}

.button {
    font-size: 1.9rem;
    font-weight: 400;
    color: rgb(var(--color-sf-text-title));
    cursor: pointer;
}

.buttonActive {
    border-bottom: 3px solid rgb(var(--color-sf-primary));
    transition: 0.3s;
}
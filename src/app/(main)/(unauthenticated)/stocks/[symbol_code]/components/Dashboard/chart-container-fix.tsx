"use client";

import type React from "react";

import {useEffect} from "react";
import styles from "./chart-container-fix.module.css";

// This is a utility component to fix chart overflow issues
export function useChartContainerFix(chartRef: React.RefObject<HTMLDivElement>) {
    useEffect(() => {
        if (!chartRef.current) return;

        // Add the CSS module class to the chart container
        chartRef.current.classList.add(styles.fixedChart);

        // Function to ensure chart stays within container
        const fixChartOverflow = () => {
            const container = chartRef.current;
            if (!container) return;

            // Find all SVG elements within the container
            const svgs = container.querySelectorAll("svg");

            svgs.forEach((svg) => {
                // Ensure SVG has proper viewBox and preserveAspectRatio
                if (!svg.getAttribute("viewBox")) {
                    const width = svg.getAttribute("width") || "100%";
                    const height = svg.getAttribute("height") || "100%";
                    svg.setAttribute("viewBox", `0 0 ${width} ${height}`);
                }

                svg.setAttribute("preserveAspectRatio", "xMidYMid meet");

                // Ensure SVG is properly contained
                svg.style.maxWidth = "100%";
                svg.style.overflow = "hidden";

                // Fix for Recharts specific issues
                const rechartsSurface = svg.querySelector(".recharts-surface");
                if (rechartsSurface) {
                    rechartsSurface.setAttribute("overflow", "hidden");
                }

                // Fix for right-side overflow - ensure Y-axis labels are visible
                const rightAxisTicks = svg.querySelectorAll(".recharts-cartesian-axis-tick text");
                rightAxisTicks.forEach((tick) => {
                    // Make sure text is not cut off
                    const textEl = tick as SVGTextElement;
                    if (textEl.textContent && textEl.textContent.includes("$")) {
                        textEl.setAttribute("text-anchor", "end");

                        // Add extra padding for medium screens
                        const windowWidth = window.innerWidth;
                        if (windowWidth >= 480 && windowWidth < 1024) {
                            // Add more space for tablet-sized screens
                            const currentX = Number.parseFloat(textEl.getAttribute("x") || "0");
                            textEl.setAttribute("x", `${currentX - 5}`);
                        }
                    }
                });

                // Fix for right-side overflow
                const rightElements = svg.querySelectorAll(".recharts-cartesian-axis-tick:last-child, .recharts-bar-rectangle:last-child");
                rightElements.forEach((el) => {
                    const bbox = el.getBoundingClientRect();
                    const svgBbox = svg.getBoundingClientRect();

                    if (bbox.right > svgBbox.right) {
                        (el as HTMLElement).style.display = "none";
                    }
                });
            });
        };

        // Apply fix on mount
        fixChartOverflow();

        // Create a ResizeObserver to reapply fix when container size changes
        const resizeObserver = new ResizeObserver(() => {
            fixChartOverflow();
        });

        const currentChartRef = chartRef.current;
        resizeObserver.observe(currentChartRef);

        return () => {
            if (currentChartRef) {
                resizeObserver.unobserve(currentChartRef);
            }
        };
    }, [chartRef]);
}

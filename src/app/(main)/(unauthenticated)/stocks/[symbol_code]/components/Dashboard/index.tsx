import {Debt<PERSON><PERSON>} from "@/components/Charts/DebtChart";
import {DividendChart} from "@/components/Charts/DividendChart";
import {ProfitabilityChart} from "@/components/Charts/ProfitabilityChart";
import {ValuationChart} from "@/components/Charts/ValuationChart";
import {TabButton} from "@/components/Layout/TabButton";
import TabPage from "@/components/Layout/TabPage";
import Loading from "@/components/Loading";
import {StockComparison} from "@/components/Stocks/ComparisonComponent";
import {DocumentsTable} from "@/components/Stocks/DocumentsTable";
import {HistoricalTable} from "@/components/Stocks/HistoricalTable";
import {Indicators} from "@/components/Stocks/Indicators";
import {PriceChart} from "@/components/Stocks/PricesChart";
import {BalanceSheetResponse} from "@/types/api/BalanceSheetResponse";
import {CashFlowResponse} from "@/types/api/CashFlowResponse";
import {DividendsResponse} from "@/types/api/DividendsResponse";
import {FilterCategory} from "@/types/api/FiltersResponse";
import {IncomeStatementResponse} from "@/types/api/IncomeStatementResponse";
import {PricesResponse} from "@/types/api/PricesResponse";
import {Assets, StatisticsHistoryResponse} from "@/types/api/StatisticsHistoryResponse";
import {StatisticsResponse} from "@/types/api/StatisticsResponse";
import {TickerResponse} from "@/types/api/TickerResponse";
import {FirstPriceType} from "@/types/context/PricesContext";
import {CURRENCY_SYMBOL} from "@/types/utils/constants";
import {AccountBalance, Favorite, FavoriteBorder, TrendingDown, TrendingUp} from "@mui/icons-material";
import {LayoutDashboard, TableProperties} from "lucide-react";
import {useEffect, useRef, useState} from "react";
import {useTranslation} from "react-i18next";
import {useChartContainerFix} from "./chart-container-fix";
import styles from "./styles.module.css";

interface DashboardStockProps {
    ticker: TickerResponse | null;
    statistics: StatisticsResponse | null;
    loadingFavorite: boolean;
    isFavorite: boolean;
    dividends: DividendsResponse | null;
    firstPrice: FirstPriceType;
    current_price: number;
    prices: PricesResponse | null;
    categories: FilterCategory[];
    id: number;
    statisticsHistory: StatisticsHistoryResponse[];
    assetsHistory: Assets[];
    handleFavorite: () => void;
}

enum Tabs {
    Dashboard = "DASHBOARD",
    Documents = "DOCUMENTS",
}

export function generateColorPalette(baseColor: string, count: number): string[] {
    // Extract HSL components
    const [h, s, _] = baseColor.split(" ");

    // Generate colors with different lightness values
    const colors: string[] = [];
    const minLightness = 30;
    const maxLightness = 65;
    const step = (maxLightness - minLightness) / (count - 1);

    for (let i = 0; i < count; i++) {
        const lightness = maxLightness - i * step;
        colors.push(`${h} ${s} ${lightness}%`);
    }

    return colors;
}

export function DashboardStock({
    ticker,
    statistics,
    loadingFavorite,
    isFavorite,
    dividends,
    firstPrice,
    current_price,
    categories,
    prices,
    id,
    statisticsHistory,
    assetsHistory,
    handleFavorite,
}: DashboardStockProps) {
    const [selectedCategories, setSelectedCategories] = useState(["Profitability"]);
    const [isHistory, setIsHistory] = useState(false);
    const [isIndicator, setIsIndicator] = useState(true);
    const [baseColor, setBaseColor] = useState("187 86% 53%");
    const [windowWidth, setWindowWidth] = useState(typeof window !== "undefined" ? window.innerWidth : 0);
    const [tab, setTab] = useState<Tabs>(Tabs.Dashboard);
    const [balance_sheet, setBalanceSheet] = useState<BalanceSheetResponse[]>([]);
    const [income_statement, setIncomeStatement] = useState<IncomeStatementResponse[]>([]);
    const [cash_flow, setCashFlow] = useState<CashFlowResponse[]>([]);
    const [comparison, setComparison] = useState<StatisticsResponse[]>([]);
    const dividendChartRef = useRef<HTMLDivElement>(null);

    useChartContainerFix(dividendChartRef);

    useEffect(() => {
        const handleResize = () => {
            setWindowWidth(window.innerWidth);
        };

        if (typeof window !== "undefined") {
            window.addEventListener("resize", handleResize);
            return () => window.removeEventListener("resize", handleResize);
        }
    }, []);

    // Determine if we're on mobile
    const isMobile = windowWidth < 640;

    // Dividend data
    const dividendData = dividends
        ? Object.keys(dividends).map((year) => ({
              year,
              dividendYield: dividends[year]?.dy,
              dividendsPaid: dividends[year].value,
              payoutRatio: statisticsHistory.find((item) => item.date === year)?.payout_ratio,
          }))
        : [];

    const {
        t,
        i18n: {language: lang},
    } = useTranslation();

    const dividends_years = dividends ? Object.keys(dividends) : [];

    const last_year = dividends_years[dividends_years.length - 1];

    const dividends_values: {
        dy: number;
        value: number;
    } =
        dividends && dividends[last_year]
            ? dividends[last_year]
            : {
                  dy: 0,
                  value: 0,
              };

    const valuation_month = (current_price - firstPrice.fisrt_price_month) / current_price;

    const valuation_year = (current_price - firstPrice.fisrt_price_year) / current_price;

    // Define color palettes
    const multiColorPalette = {
        // Dividend chart colors
        dividendYield: "0 91% 42%", // Red
        dividendsPaid: "187 86% 53%", // Bright Cyan (primary)
        payoutRatio: "45 93% 47%", // Yellow

        // Profitability chart colors
        netMargin: "45 93% 47%", // Yellow
        operatingMargin: "199 89% 48%", // Blue Cyan
        eps: "187 86% 53%", // Bright Cyan (primary)

        // Debt chart colors
        ebitda: "152 69% 31%", // Green
        netDebt: "0 91% 42%", // Red
        totalDebt: "16 85% 54%", // Orange
        assets: "187 86% 53%", // Bright Cyan (primary)

        // Valuation chart colors
        priceToSales: "199 89% 48%", // Blue Cyan
        pe: "187 86% 53%", // Bright Cyan (primary)
        priceToBook: "174 100% 29%", // Teal

        // Price chart colors
        price: "174 100% 45%", // Teal/Cyan
    };

    // Add price color to singleColorPalette generation
    const singleColorPalette = {
        // Dividend chart colors
        ...Object.fromEntries(generateColorPalette(baseColor, 3).map((color, index) => [["dividendYield", "dividendsPaid", "payoutRatio"][index], color])),

        // Profitability chart colors
        ...Object.fromEntries(generateColorPalette(baseColor, 3).map((color, index) => [["netMargin", "operatingMargin", "eps"][index], color])),

        // Debt chart colors
        ...Object.fromEntries(generateColorPalette(baseColor, 4).map((color, index) => [["ebitda", "netDebt", "totalDebt", "assets"][index], color])),

        // Valuation chart colors
        ...Object.fromEntries(generateColorPalette(baseColor, 3).map((color, index) => [["priceToSales", "pe", "priceToBook"][index], color])),

        // Price chart colors
        price: generateColorPalette(baseColor, 5)[0],
    };

    useEffect(() => {
        if (id && balance_sheet?.length === 0 && income_statement.length === 0) {
            fetch(`/api/stocks/${id}/documents/balance_sheet/y`)
                .then((data) => data.json())
                .then((res: BalanceSheetResponse[]) => setBalanceSheet(res));

            fetch(`/api/stocks/${id}/documents/income_statement/y`)
                .then((data) => data.json())
                .then((res: IncomeStatementResponse[]) => setIncomeStatement(res));

            fetch(`/api/stocks/${id}/documents/cash_flow/y`)
                .then((data) => data.json())
                .then((res: CashFlowResponse[]) => setCashFlow(res));
        }
    }, [id, balance_sheet?.length, income_statement?.length, cash_flow?.length]);

    useEffect(() => {
        if (ticker && ticker.sector && ticker.country_code) {
            fetch(`/api/stocks/filters/location/${ticker.country_code}/sector/${ticker.sector}/current_symbol/${ticker.primary_ticker_eodhd}`)
                .then((res) => res.json())
                .then((data: StatisticsResponse[]) => setComparison(data));
        }
    }, [ticker]);

    return (
        <>
            <div className="flex gap-4 mt-10 mb-10">
                <TabPage label="Dashboard" action={() => setTab(Tabs.Dashboard)} isActived={tab === Tabs.Dashboard}>
                    <LayoutDashboard className="mr-2" />
                </TabPage>
                <TabPage label="Documents" action={() => setTab(Tabs.Documents)} isActived={tab === Tabs.Documents}>
                    <TableProperties className="mr-2" />
                </TabPage>
            </div>
            {tab === Tabs.Dashboard && (
                <>
                    <div className="w-full grid sm:grid-cols-3 grid-cols-1 sm:gap-4 mt-10">
                        <div className="col">
                            <div className="flex flex-col h-[180px] shadow-lg justify-between p-4 rounded-[15px] bg-white">
                                <div className="flex justify-between items-center">
                                    <div className="text-text">
                                        <h4 className="text-ellipsis px-2">{ticker?.symbol_code}</h4>
                                        <span className="text-[14px] ml-3">{ticker?.name}</span>
                                    </div>
                                    <div>
                                        <span className="ml-2 cursor-pointer">
                                            {loadingFavorite ? (
                                                <Loading />
                                            ) : (
                                                <span onClick={handleFavorite}>
                                                    {isFavorite ? <Favorite sx={{fontSize: 40}} color="error" /> : <FavoriteBorder sx={{fontSize: 40}} color="error" />}
                                                </span>
                                            )}
                                        </span>
                                    </div>
                                </div>
                                <div className="flex flex-col items-center text-primaryShadow w-full bg-lightPrimary rounded-[15px] mt-3">
                                    <span className="pt-3">{t("watchlist.current_price")}</span>
                                    <h4 className="text-primaryShadow leading-tight">
                                        <span>{ticker?.currency_code && CURRENCY_SYMBOL[ticker?.currency_code] && CURRENCY_SYMBOL[ticker?.currency_code]}</span>
                                        {(statistics?.price || 0)?.toFixed(2)}
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <div className="col-span-1 w-full mt-2 sm:mt-0">
                            <div className="w-100">
                                <div className="bg-white flex items-center w-100  h-[88px] shadow-lg rounded-[15px] px-4">
                                    <div
                                        className={`h-[60px] w-[60px] ${
                                            statistics?.dividend_yield && statistics?.dividend_yield >= 0 ? "bg-lightPrimary" : "bg-errorLight "
                                        } rounded-[50%] flex items-center justify-center mr-5`}>
                                        {statistics?.dividend_yield && statistics.dividend_yield >= 0 ? (
                                            <TrendingUp sx={{color: "green", fontSize: 30}} />
                                        ) : (
                                            <TrendingDown sx={{color: "#FF82AC", fontSize: 30}} />
                                        )}
                                    </div>
                                    <div>
                                        <span className="text-[14px] text-text">{t("indicator.dividend_yield")}</span>
                                        <h4 className="text-dark leading-none">{statistics?.dividend_yield?.toFixed(2) || 0}%</h4>
                                    </div>
                                </div>
                            </div>
                            <div className="w-100 mt-2">
                                <div className="bg-white flex items-center w-100  h-[88px] shadow-lg rounded-[15px] px-4">
                                    <div className="h-[60px] w-[60px] bg-warningLight rounded-[50%] flex items-center justify-center mr-5">
                                        <AccountBalance sx={{color: "#ffbb38", fontSize: 30}} />
                                    </div>
                                    <div>
                                        <span className="text-[14px] text-text">{t("stock_charts.last_12_months")}</span>
                                        <h4 className="text-dark leading-none">{dividends_values.value?.toFixed(2)}</h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="col-span-1 w-full mt-2 sm:mt-0">
                            <div className="w-100">
                                <div className="bg-white flex items-center w-100  h-[88px] shadow-lg rounded-[15px] px-4">
                                    <div
                                        className={`h-[60px] w-[60px] ${
                                            valuation_year && valuation_year >= 0 ? "bg-lightPrimary" : "bg-errorLight "
                                        } rounded-[50%] flex items-center justify-center mr-5`}>
                                        {valuation_year && valuation_year >= 0 ? <TrendingUp sx={{color: "green", fontSize: 30}} /> : <TrendingDown sx={{color: "#FF82AC", fontSize: 30}} />}
                                    </div>
                                    <div>
                                        <span className="text-[14px] text-text">{t("indicator.Valuation")}</span>
                                        <h4 className="text-dark leading-none">{valuation_year.toFixed(2)}</h4>
                                    </div>
                                </div>
                            </div>
                            <div className="w-100 mt-2">
                                <div className="bg-white flex items-center w-100  h-[88px] shadow-lg rounded-[15px] px-4">
                                    <div className="h-[60px] w-[60px] bg-warningLight rounded-[50%] flex items-center justify-center mr-5">
                                        <AccountBalance sx={{color: "#ffbb38", fontSize: 30}} />
                                    </div>
                                    <div>
                                        <span className="text-[14px] text-text">{t("indicator.current_month")}</span>
                                        <h4 className="text-dark leading-none">{valuation_month?.toFixed(2)}</h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="grid sm:grid-cols-2 grid-cols-1 sm:gap-4 mt-10">
                        <div className="col">
                            <h2 className={styles.cardTitle}>{t("price_chart.title")}</h2>
                            {prices && <PriceChart prices={prices} />}
                        </div>
                        <div className={styles.chartCard}>
                            <h2 className={`mt-10 sm:mt-0 ${styles.cardTitle}`}>{t("filters.compare")}</h2>
                            {comparison?.length > 0 && <StockComparison tickers={comparison} />}
                        </div>
                    </div>
                    <div className={styles.categoriesButtons}>
                        <div className="grid grid-cols-1 sm:grid-cols-2 w-full">
                            <div className="grid sm:flex items-center sm:pb-0 pb-4">
                                {isIndicator &&
                                    categories.map((item, i) => (
                                        <button
                                            key={i}
                                            className={`mr-9 mb-2 pb-5 sm:mb-0 mt-0 ${styles.button}
                    ${Array.isArray(selectedCategories) ? (selectedCategories.includes(item.en) ? styles.buttonActive : "") : selectedCategories === item.en ? styles.buttonActive : ""}
                    `}
                                            onClick={() => {
                                                if (Array.isArray(selectedCategories)) {
                                                    // If item.en is already in the array, remove it, otherwise add it
                                                    if (selectedCategories.includes(item.en)) {
                                                        setSelectedCategories(selectedCategories.filter((cat) => cat !== item.en));
                                                    } else {
                                                        setSelectedCategories([...selectedCategories, item.en]);
                                                    }
                                                } else {
                                                    // If selectedCategories is not an array, convert it to an array with item.en
                                                    setSelectedCategories([item.en]);
                                                }
                                            }}>
                                            {item[lang]}
                                        </button>
                                    ))}
                            </div>
                            <div className="flex justify-start sm:justify-end items-center ">
                                <TabButton
                                    action={() => {
                                        setIsHistory(!isHistory);
                                        setIsIndicator(!isIndicator);
                                    }}
                                    is_active={isIndicator}
                                    label={t("indicator.indicator")}
                                />
                                <TabButton
                                    action={() => {
                                        setIsHistory(!isHistory);
                                        setIsIndicator(!isIndicator);
                                    }}
                                    is_active={isHistory}
                                    label={t("wallet.history")}
                                />
                            </div>
                        </div>
                    </div>
                    <div>{statistics && !isHistory && <Indicators statistics={statistics} selectedCategories={selectedCategories} />}</div>
                    <div>{statisticsHistory && isHistory && <HistoricalTable historical={statisticsHistory} />}</div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        {dividendData && dividendData.length > 0 && (
                            <DividendChart
                                data={dividendData}
                                colors={{
                                    dividendYield: multiColorPalette.dividendYield,
                                    dividendsPaid: multiColorPalette.dividendsPaid,
                                    payoutRatio: multiColorPalette.payoutRatio,
                                }}
                            />
                        )}

                        <div>
                            {statisticsHistory && statisticsHistory.length > 0 && (
                                <ProfitabilityChart
                                    data={statisticsHistory}
                                    colors={{
                                        netMargin: multiColorPalette.netMargin,
                                        operatingMargin: multiColorPalette.operatingMargin,
                                        eps: multiColorPalette.eps,
                                    }}
                                />
                            )}
                        </div>
                        <div>
                            {statisticsHistory && assetsHistory && statisticsHistory.length > 0 && (
                                <DebtChart
                                    assets={assetsHistory}
                                    data={statisticsHistory}
                                    colors={{
                                        ebitda: multiColorPalette.ebitda,
                                        net_debt: multiColorPalette.netDebt,
                                        total_debt: multiColorPalette.totalDebt,
                                        assets: multiColorPalette.assets,
                                    }}
                                />
                            )}
                        </div>
                        <div>
                            {statisticsHistory && statisticsHistory.length > 0 && (
                                <ValuationChart
                                    data={statisticsHistory}
                                    colors={{
                                        price_sales_ratio: multiColorPalette.priceToSales,
                                        price_to_book: multiColorPalette.priceToBook,
                                        pe: multiColorPalette.pe,
                                    }}
                                />
                            )}
                        </div>
                    </div>
                </>
            )}
            {tab === Tabs.Documents && <DocumentsTable balance_sheet={balance_sheet} income_statement={income_statement} cash_flow={cash_flow} />}
        </>
    );
}

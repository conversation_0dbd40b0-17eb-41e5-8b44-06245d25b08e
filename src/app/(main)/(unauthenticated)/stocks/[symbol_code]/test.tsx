
'use client'

import { useDividends } from '@/context/stocks/DividendsContext';
import { useDocuments } from '@/context/stocks/DocumentsContext';
import { useFilters } from '@/context/stocks/FiltersContext';
import { usePrices } from '@/context/stocks/PricesContext';
import { useStatistics } from '@/context/stocks/StatisticsContext';
import { useTicker } from '@/context/stocks/TickerContext';
import { BalanceSheetResponse } from '@/types/api/BalanceSheetResponse';
import { CashFlowResponse } from '@/types/api/CashFlowResponse';
import { DividendsResponse } from '@/types/api/DividendsResponse';
import { FilterCategoryAttributes } from '@/types/api/FiltersResponse';
import { IncomeStatementResponse } from '@/types/api/IncomeStatementResponse';
import { PricesResponse } from '@/types/api/PricesResponse';
import { StockResponse } from '@/types/api/StockResponse';
import { StrapiItem } from '@/types/utils/strapiAPI';
import moment from 'moment';
import { useParams } from 'next/navigation';
import { useEffect } from 'react';


type Props = {
    children: React.ReactNode;
}

export default function Laouyt({ children }: Props) {
  
  const params: any = useParams()

  const {  ticker, setTicker } = useTicker()
  const { statistics, calcStatisticsHistory, setStatistics } = useStatistics()
  const { setCategories } = useFilters()
  const { prices, setPrices, setFirstPrice } = usePrices()
  const { dividends, setDividends } = useDividends()

  const {
    incomeStatementHolder, balanceSheetHolder, cashFlowHolder,
    setBalanceSheetHistory, 
    setIncomeStatementHistory, 
    setCashFlowHistory 
  } = useDocuments()

  const {id} = params

  useEffect(() => {
    async function fetchStock() { 
        if (id) {
            const response = await fetch(`/api/stocks/${id}`)

            const data: StockResponse = await response.json()

            setTicker(data.ticker)
            setStatistics(data.statistics || null)

            const res_balanceSheet = await fetch(`/api/stocks/${id}/documents/balance_sheet`)
            const res_cash_flow = await fetch(`/api/stocks/${id}/documents/cash_flow`)
            const res_income_statement = await fetch(`/api/stocks/${id}/documents/income_statement`)

            const balance_sheet: BalanceSheetResponse[] = await res_balanceSheet.json()
            const cash_flow: CashFlowResponse[] = await res_cash_flow.json()
            const income_statement: IncomeStatementResponse[] = await res_income_statement.json()

            setBalanceSheetHistory(balance_sheet)
            setCashFlowHistory(cash_flow)
            setIncomeStatementHistory(income_statement)
        }
      }

      fetchStock()
    }, [
      id, 
      setBalanceSheetHistory, 
      setCashFlowHistory, 
      setIncomeStatementHistory, 
      setStatistics, 
      setTicker
  ])

  useEffect(() => {
    async function fetchCategories() {
        const response = await fetch(`/api/stocks/filters/categories`)
        const data: StrapiItem[] = await response.json()

        const categories: FilterCategoryAttributes[] = data.map(item => ({id: item.id, ...item.attributes, isEnabled: false }))

        setCategories(categories)
    }

    fetchCategories()
    
  }, [setCategories])


  useEffect(() => {
    async function fetchDividends() {

        const res = await fetch(`/api/dividend_yield/${id}?symbol_code=${ticker?.primary_ticker_eodhd}`)
  
        const { dividends, prices }: { dividends: DividendsResponse, prices: PricesResponse } = await res.json()
        
        setDividends(dividends)
        setPrices(prices)
          
    }

    async function fetchPriceHistory() {

        const current_date = new Date()

        const current_year = current_date.getFullYear()

        const current_month = moment(current_date).format('MM');

        const res = await fetch(`/api/price/${ticker?.primary_ticker_eodhd}/history?from=${current_year}-${current_month}-01`)

       
        if (res) {
            const data = await res?.json()

            const { first_result_month, first_result_year } = data
            
            if (first_result_month && first_result_year) {
                setFirstPrice({ fisrt_price_month: first_result_month.close, fisrt_price_year: first_result_year.close })   
            }
        }


    }

    if (ticker?.primary_ticker_eodhd && ticker?.primary_ticker_eodhd !== '') {
        fetchDividends()

        fetchPriceHistory()
    }
  }, [id, setDividends, setFirstPrice, setPrices, ticker?.primary_ticker_eodhd])

  useEffect(() => {
    if (
        Object.keys(balanceSheetHolder).length > 0 &&
        Object.keys(incomeStatementHolder).length > 0 &&
        Object.keys(cashFlowHolder).length > 0 &&
        Object.keys(prices).length > 0 
    ) {
        calcStatisticsHistory({
            balance_sheet: balanceSheetHolder, 
            cash_flow: cashFlowHolder, 
            income_statement: incomeStatementHolder,
            ticker: {}, 
            prices,
            dividends,
            statistics
        })
    }

    }, [balanceSheetHolder, incomeStatementHolder, cashFlowHolder, prices, calcStatisticsHistory, dividends, statistics ])
  
  return (
    <>
        {children}
    </>
  )
} 

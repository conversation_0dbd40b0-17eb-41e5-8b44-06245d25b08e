"use client";

import Container from "@/components/Container";
import {PageTitle} from "@/components/Layout/PageTitle";
import Loading from "@/components/Loading";
import {useFilters} from "@/context/stocks/FiltersContext";
import {useStatistics} from "@/context/stocks/StatisticsContext";
import {StatisticsResponse} from "@/types/api/StatisticsResponse";
import {slider_types} from "@/utils/constants";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import ArrowDropUpIcon from "@mui/icons-material/ArrowDropUp";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import FilterAltIcon from "@mui/icons-material/FilterAlt";
import SettingsIcon from "@mui/icons-material/Settings";
import Checkbox from "@mui/material/Checkbox";
import {useRouter} from "next/navigation";
import {useCallback, useEffect, useState} from "react";
import {useTranslation} from "react-i18next";
import styles from "./styles.module.css";

interface ApiResponse {
    total: number;
    page: number;
    totalPages: number;
    rows: StatisticsResponse[];
}

// Define column interface
interface Column {
    id: string;
    label: string;
    accessor: (stock: StatisticsResponse) => any;
    format?: (value: any) => string;
}

export default function SearchResultsPage() {
    const router = useRouter();

    const [showFilterMenu, setShowFilterMenu] = useState(false);
    const [showColumnSelector, setShowColumnSelector] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [apiData, setApiData] = useState<ApiResponse>({
        total: 0,
        page: 0,
        totalPages: 0,
        rows: [],
    });

    const {selectedFilters} = useFilters();
    const {pagination, setPagination, setStatisticsHistory} = useStatistics();
    const {pageSize: itemsPerPage} = pagination;

    const fetchData = useCallback(async () => {
        try {
            setIsLoading(true);
            const params: any = {
                filters: {},
                location: {},
            };

            const filters = selectedFilters.filters;

            Object.keys(filters).forEach((entry) => {
                const item = selectedFilters.stage === filters[entry].search_type && filters[entry] && filters[entry].actived ? filters[entry] : null;

                if (item) {
                    const mult = item.filter_type ? slider_types[item.filter_type] : 1;

                    params.filters[`${entry}_gte`] = item.values && item.values[0] * mult;
                    params.filters[`${entry}_lte`] = item.values && item.values[1] * mult;
                }
            });

            if (selectedFilters.sector !== "ALL") {
                params.sector = selectedFilters.sector;
            }

            params.location = selectedFilters.location;

            const response = await fetch(`/api/stocks/statistics?page=${pagination.pageIndex}&pageSize=${pagination.pageSize}`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(params),
            });

            const data = await response.json();

            const {rows, total} = data;

            if (rows && total) {
                setApiData(data);
                setStatisticsHistory(rows);
            }
        } catch (error) {
        } finally {
            setIsLoading(false);
        }
    }, [selectedFilters.filters, selectedFilters.location, selectedFilters.sector, pagination.pageIndex, pagination.pageSize, setStatisticsHistory]);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    const {t, i18n} = useTranslation();

    const {language: lang} = i18n;

    // Add sort state management to the component
    const [sortColumn, setSortColumn] = useState<string | null>(null);
    const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");

    // Format currency
    const formatCurrency = (value: number | null): string => {
        return typeof value === "number"
            ? new Intl.NumberFormat("en-US", {
                  style: "currency",
                  currency: "USD",
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
              }).format(value)
            : "0.00";
    };

    // Format number with 2 decimal places
    const formatNumber = (value: number | null): string => {
        return typeof value === "number" ? value.toFixed(2) : "0.00";
    };

    // Format percentage
    const formatPercent = (value: number): string => {
        return typeof value === "number" ? `${value.toFixed(2)}%` : "0.00%";
    };

    // Define all available columns
    const allColumns: Column[] = [
        {
            id: "symbol_code",
            label: "Symbol",
            accessor: (stock) => stock.ticker_internal.symbol_code,
        },
        {
            id: "name",
            label: "Name",
            accessor: (stock) => stock.ticker_internal.name,
        },
        {
            id: "price",
            label: "Price",
            accessor: (stock) => stock.price,
            format: formatCurrency,
        },
        {
            id: "eps_current",
            label: "EPS Current",
            accessor: (stock) => stock.eps_current,
            format: formatCurrency,
        },
        {
            id: "eps_last_year",
            label: "EPS Last Year",
            accessor: (stock) => stock.eps_last_year,
            format: formatCurrency,
        },
        {
            id: "pe",
            label: "P/E",
            accessor: (stock) => stock.pe,
            format: formatNumber,
        },
        {
            id: "peg_ratio",
            label: "PEG Ratio",
            accessor: (stock) => stock.peg_ratio,
            format: formatNumber,
        },
        {
            id: "bvps",
            label: "BVPS",
            accessor: (stock) => stock.bvps,
            format: formatCurrency,
        },
        {
            id: "price_to_book",
            label: "Price/Book",
            accessor: (stock) => stock.price_to_book,
            format: formatNumber,
        },
        {
            id: "net_margin",
            label: "Net Margin",
            accessor: (stock) => stock.net_margin,
            format: formatPercent,
        },
        {
            id: "roe",
            label: "ROE",
            accessor: (stock) => stock.roe,
            format: formatPercent,
        },
        {
            id: "dividend_yield",
            label: "Dividend Yield",
            accessor: (stock) => stock.dividend_yield,
            format: formatPercent,
        },
        {
            id: "market_cap",
            label: "Market Cap",
            accessor: (stock) => stock.market_capitalization,
            format: (value) => {
                if (value >= 1e12) return `$${(value / 1e12).toFixed(2)}T`;
                if (value >= 1e9) return `$${(value / 1e9).toFixed(2)}B`;
                if (value >= 1e6) return `$${(value / 1e6).toFixed(2)}M`;
                return formatCurrency(value);
            },
        },
        {
            id: "sector",
            label: "Sector",
            accessor: (stock) => stock.ticker_internal?.sector?.[lang] ?? "N/A",
        },
        {
            id: "country",
            label: "Country",
            accessor: (stock) => stock.ticker_internal.country_code,
        },
    ];

    // State to track which columns are visible - default to the first 5 columns
    const [visibleColumns, setVisibleColumns] = useState<string[]>(["symbol_code", "name", "price", "eps_current", "eps_last_year"]);

    // Toggle column visibility
    const toggleColumn = (columnId: string) => {
        if (visibleColumns.includes(columnId)) {
            // Don't allow removing the last column
            if (visibleColumns.length > 1) {
                setVisibleColumns(visibleColumns.filter((id) => id !== columnId));
            }
        } else {
            setVisibleColumns([...visibleColumns, columnId]);
        }
    };

    // Get currently visible columns
    const getVisibleColumns = () => {
        return allColumns.filter((col) => visibleColumns.includes(col.id));
    };

    // Use the API response for pagination
    const {total: totalItems, totalPages} = apiData;

    // Change page
    const paginate = (pageNumber: number) => {
        setPagination((old) => ({...old, pageIndex: pageNumber}));

        // In a real app, you would fetch data for the new page here
    };

    // Go to next page
    const nextPage = () => {
        if (pagination.pageIndex < totalPages - 1) {
            setPagination((old) => ({...old, pageIndex: old.pageIndex + 1}));
            // In a real app, you would fetch data for the new page here
        }
    };

    // Go to previous page
    const prevPage = () => {
        if (pagination.pageIndex > 0) {
            setPagination((old) => ({...old, pageIndex: old.pageIndex - 1}));
            // In a real app, you would fetch data for the new page here
        }
    };

    // Add a function to handle sorting
    const handleSort = (columnId: string) => {
        if (sortColumn === columnId) {
            // Toggle direction if clicking the same column
            setSortDirection(sortDirection === "asc" ? "desc" : "asc");
        } else {
            // Set new column and default to ascending
            setSortColumn(columnId);
            setSortDirection("asc");
        }
    };

    // Update the useEffect for data fetching to include sort parameters
    useEffect(() => {
        // Simulating API fetch
        if (sortColumn) {
            const sortedData = {...apiData};
            sortedData.rows = [...apiData.rows].sort((a, b) => {
                const columnDef = allColumns.find((col) => col.id === sortColumn);
                if (!columnDef) return 0;

                const valueA = columnDef.accessor(a);
                const valueB = columnDef.accessor(b);

                if (valueA < valueB) return sortDirection === "asc" ? -1 : 1;
                if (valueA > valueB) return sortDirection === "asc" ? 1 : -1;
                return 0;
            });

            setApiData(sortedData);
        }
    }, [pagination.pageIndex, itemsPerPage, sortColumn, sortDirection]);

    return (
        <Container>
            {isLoading ? (
                <Loading />
            ) : (
                <>
                    <div className="flex items-center justify-between mb-6 mt-5">
                        <div className="flex items-center">
                            <button onClick={() => router.back()} className={`${styles.button} ${styles.backButton}`}>
                                <ArrowBackIcon className="h-5 w-5 mr-2" />
                                <span className="font-inter hidden md:block">{t("filters.back")}</span>
                            </button>
                            <PageTitle title={t("indicator.results")} />
                        </div>

                        <div className="flex gap-2">
                            <div className="relative">
                                <button className={`${styles.button} ${styles.filterButton} ${showFilterMenu && styles.activedButton}`} onClick={() => setShowFilterMenu(!showFilterMenu)}>
                                    <FilterAltIcon className="h-4 w-4 mr-2" />
                                    {t("filters.filter")}
                                    {sortColumn && <span className={styles.badge}>1</span>}
                                </button>

                                {showFilterMenu && (
                                    <div className={styles.dropdownContainer}>
                                        <div className={styles.dropdownHeader}>
                                            <h3 className={`${styles.filterDropdownTitle} font-medium text-gray-800 font-inter`}>{t("filters.sort_option")}</h3>
                                            <button onClick={() => setShowFilterMenu(false)} className={styles.closeButton}>
                                                ✕
                                            </button>
                                        </div>
                                        <div className={styles.dropdownBody}>
                                            <div className="space-y-2">
                                                {allColumns.map((column) => (
                                                    <div
                                                        key={column.id}
                                                        className={`${styles.sortItem} ${sortColumn === column.id ? styles.activeSortItem : ""}`}
                                                        onClick={() => {
                                                            handleSort(column.id);
                                                            setShowFilterMenu(false);
                                                        }}>
                                                        <span className="text-sm font-poppins text-gray-700">{t(`indicator.${column.id}`)}</span>
                                                        {sortColumn === column.id && <span className="text-purple-600">{sortDirection === "asc" ? <ArrowDropUpIcon /> : <ArrowDropDownIcon />}</span>}
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                        {sortColumn && (
                                            <div className="p-3 border-t border-gray-200">
                                                <button
                                                    className={`${styles.button} ${styles.clearButton} w-full`}
                                                    onClick={() => {
                                                        setSortColumn(null);
                                                        setShowFilterMenu(false);
                                                    }}>
                                                    {t("filters.clear_filter")}
                                                </button>
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>

                            <div className="relative">
                                <button className={`${styles.button} ${styles.columnButton} ${showColumnSelector && styles.activedButton}`} onClick={() => setShowColumnSelector(!showColumnSelector)}>
                                    <SettingsIcon className="h-4 w-4 mr-2" />
                                    {t("filters.columns")}
                                    <span className={styles.badge}>{visibleColumns.length}</span>
                                </button>

                                {showColumnSelector && (
                                    <div className={styles.dropdownContainer}>
                                        <div className={styles.dropdownHeader}>
                                            <h3 className={`${styles.filterDropdownTitle} font-medium text-gray-800 font-inter`}>Select Columns</h3>
                                            <button onClick={() => setShowColumnSelector(false)} className={styles.closeButton}>
                                                ✕
                                            </button>
                                        </div>
                                        <div className={styles.dropdownBody}>
                                            <div className="space-y-2">
                                                {allColumns.map((column) => (
                                                    <div key={column.id} className="flex items-center">
                                                        <Checkbox
                                                            id={`column-${column.id}`}
                                                            checked={visibleColumns.includes(column.id)}
                                                            onChange={() => toggleColumn(column.id)}
                                                            size="small"
                                                            className="mr-1"
                                                        />
                                                        <label htmlFor={`column-${column.id}`} className={`${styles.filterItem} text-sm font-poppins text-gray-700`}>
                                                            {column.label}
                                                        </label>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    <div className={styles.tableContainer}>
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead>
                                    <tr className=" border-gray-100">
                                        {getVisibleColumns().map((column, index) => (
                                            <th key={column.id} className={`${styles.headerColumn} ${index === 0 ? styles.firstColumn : ""}`} onClick={() => handleSort(column.id)}>
                                                <div className="flex items-center">
                                                    {t(`indicator.${column.id}`)}
                                                    <div className="ml-1">
                                                        {sortColumn === column.id ? (
                                                            sortDirection === "asc" ? (
                                                                <ArrowDropUpIcon className="text-purple-600" />
                                                            ) : (
                                                                <ArrowDropDownIcon className="text-purple-600" />
                                                            )
                                                        ) : (
                                                            <div className="w-6 h-6"></div> // Empty space to maintain alignment
                                                        )}
                                                    </div>
                                                </div>
                                            </th>
                                        ))}
                                    </tr>
                                </thead>
                                <tbody>
                                    {apiData.rows.map((stock, index) => (
                                        <tr key={stock.id} className={`border-gray-100 hover:bg-gray-50 transition-colors ${index % 2 === 0 ? styles.rowOdd : styles.rowEven}`}>
                                            {getVisibleColumns().map((column, index) => {
                                                const value = column.accessor(stock);
                                                const formattedValue = column.format ? column.format(value) : value;

                                                return (
                                                    <td
                                                        key={column.id}
                                                        className={`${styles.tableCell} ${index === 0 ? styles.firstColumnCell : ""}`}
                                                        onClick={index === 0 ? () => router.push(`/stocks/${stock.ticker_internal.primary_ticker_eodhd}`) : undefined}>
                                                        {formattedValue}
                                                    </td>
                                                );
                                            })}
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                        <div className={styles.paginationContainer}>
                            <div className={`${styles.paginationText} text-sm text-gray-500 font-poppins`}>
                                {t("filters.showing")} {pagination.pageIndex * itemsPerPage + 1}-{Math.min((pagination.pageIndex + 1) * itemsPerPage, totalItems)} {t("filters.of")} {totalItems}{" "}
                                {t("filters.items")}
                            </div>

                            <div className="flex items-center space-x-3">
                                <button onClick={prevPage} disabled={pagination.pageIndex === 0} className={styles.paginationButton}>
                                    <ChevronLeftIcon className="h-4 w-4" />
                                    <span className="sr-only">Previous</span>
                                </button>
                                {Array.from({length: Math.min(5, totalPages)}).map((_, index) => {
                                    let pageNumber: number;

                                    if (totalPages <= 5) {
                                        // If we have 5 or fewer pages, show all
                                        pageNumber = index;
                                    } else if (pagination.pageIndex < 3) {
                                        // If we're near the start, show first 5 pages
                                        pageNumber = index;
                                    } else if (pagination.pageIndex > totalPages - 4) {
                                        // If we're near the end, show last 5 pages
                                        pageNumber = totalPages - 5 + index;
                                    } else {
                                        // Otherwise show 2 before and 2 after current page
                                        pageNumber = pagination.pageIndex - 2 + index;
                                    }

                                    return (
                                        <button
                                            key={pageNumber}
                                            onClick={() => paginate(pageNumber)}
                                            className={pagination.pageIndex === pageNumber ? styles.activePaginationButton : styles.paginationButton}>
                                            {pageNumber + 1}
                                        </button>
                                    );
                                })}
                                <button onClick={nextPage} disabled={pagination.pageIndex === totalPages - 1} className={styles.paginationButton}>
                                    <ChevronRightIcon className="h-4 w-4" />
                                    <span className="sr-only">Next</span>
                                </button>
                            </div>

                            <div className="flex items-center space-x-2">
                                <span className={`${styles.itemsPage} text-gray-500 font-poppins`}>{t("filters.items_page")}:</span>
                                <select
                                    value={itemsPerPage}
                                    onChange={(e) => {
                                        setPagination({
                                            pageSize: parseInt(e.target.value, 10),
                                            pageIndex: 0,
                                        }); // Reset to first page when changing items per page
                                    }}
                                    className={styles.pageSelector}>
                                    <option value={5}>5</option>
                                    <option value={10}>10</option>
                                    <option value={20}>20</option>
                                    <option value={50}>50</option>
                                    <option value={100}>100</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </>
            )}
        </Container>
    );
}

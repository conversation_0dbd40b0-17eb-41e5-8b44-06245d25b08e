
  /* Button styles */
  .button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.875rem;
    font-weight: 500;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
    cursor: pointer;
    padding: .5rem .8rem;
  }

  .clearButton {
    color: #9333ea;
    font-size: 14px;
  }
  
  .backButton {
    color: #4b5563;
    margin-right: 1rem;
    border: none;
  }
  
  .filterButton {
    background-color: var(--color-sf-light-purple);
    color: var(--color-sf-dark-purple);
    border: 2px solid var(--color-sf-dark-purple);
  }
  
  .columnButton {
    background-color: var(--color-sf-light-purple);
    color: var(--color-sf-dark-purple);
    border: 2px solid var(--color-sf-dark-purple);
  }

  .activedButton {
    background-color: var(--color-sf-dark-purple);
    transition: 0.5ms;
    color: var(--colors-white);
  }
  
  .paginationButton {
    height: 2rem;
    width: 2rem;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e2e8f0;
    padding: 13px;
    border-radius: 4px;
  }
  
  .activePaginationButton {
    height: 2rem;
    width: 2rem;
    display: flex;
    align-items: center;
    padding: 13px;
    justify-content: center;
    background-color: #1a1a47;
    color: var(--colors-white);
    border-radius: 4px;
  }
  
  .activePaginationButton:hover {
    background-color: #2a2a57;
  }
  
  /* Badge styles */
  .badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 9999px;
    padding: 0 0.5rem;
    height: 1.25rem;
    font-size: 0.75rem;
    background-color: #f3e8fe;
    color: #9333ea;
    margin-left: 0.25rem;
  }
  
  /* Dropdown styles */
  .dropdownContainer {
    position: absolute;
    right: 0;
    margin-top: 0.5rem;
    width: 16rem;
    background-color: var(--colors-white);
    border-radius: 0.375rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    z-index: 10;
    border: 1px solid #e5e7eb;
  }
  
  .dropdownHeader {
    padding: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .filterDropdownTitle {
    font-size: 14px;
  }

  .filterItem {
    font-size: 12px;
  }
  
  .dropdownBody {
    padding: 0.75rem;
    max-height: 20rem;
    overflow-y: auto;
  }
  
  .closeButton {
    height: 1.5rem;
    width: 1.5rem;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 9999px;
  }
  
  /* Table styles */
  .tableContainer {
    background-color: var(--colors-white);
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    overflow: hidden;
  }
  
  .headerColumn {
    padding: 1rem 1.5rem;
    text-align: left;
    font-weight: 500;
    background-color: #f8f9fa;
    cursor: pointer;
    font-family: var(--poppins);
    font-size: 16px;
  }
  
  .headerColumn:hover {
    background-color: #f3f4f6;
  }

  .headerColumn div {
    font-size: 16px;
    color: rgb(var(--color-text-title));
  }
  
  .firstColumn {
    background-color: #eeeeee;
  }
  
  .tableCell {
    padding: 1rem 1.5rem;
    color: #4b5563;
  }
  
  .firstColumnCell {
    font-weight: 500;
    color: #1f2937;
    background-color: #eeeeee;
    cursor: pointer;
    font-size: 2.1rem;
  }
  
  .paginationContainer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
  }

  .paginationText {
    font-size: 1.3rem;
    color: #4b5563;
  }
  
  .pageSelector {
    height: 2rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    padding: 0 0.5rem;
  }
  
  /* Sort item styles */
  .sortItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem;
    border-radius: 0.25rem;
    cursor: pointer;
  }

  .sortItem span {
    font-size: 12px;
  }
  
  .sortItem:hover {
    background-color: #f9fafb;
  }
  
  .activeSortItem {
    background-color: #f3e8fe;
  }

  .itemsPage {
    font-size: 13px;
  }

  .rowEven {
    background-color: #f9fafb;
  }
  
  .rowOdd {
    background-color: white;
  }
  
  @media screen and (max-width: 639px) {
    .button {
      font-size: 12px;
      padding: 0.5rem 0.5rem;
    }
  }
.select-button-container {
    background-color: rgb(var(--color-sf-button-gray));
    height: 40px;
    width: 300px;
    border-radius: 55px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.select-button-container .select-button {
    background-color: rgb(var(--color-sf-button-gray));
    color: var(--colors-primary);
    width: 50%;
    height: 100%;
    border-radius: 50px;
    font-weight: 500;
}

.select-button-container .select-button-active {
    background-color: var(--colors-primary);
    color: var(--colors-white);
    width: 50%;
    transition: all 0.5s;
}

.filters-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    background-color: rgb(var(--color-sf-white));
    border-radius: 55px;
    padding: 0 10px;
    border-radius: 24px;
}

.filter-label {
    font-weight: 600;
    color: rgb(var(--color-sf-black));
    font-size: 18px;
}

.filterGrid {
    /* display: grid; */
    /* grid-template-columns: repeat(3, 1fr); */
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  @media (max-width: 1024px) {
    .filterGrid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  @media (max-width: 768px) {
    .searchFields {
      grid-template-columns: 1fr;
    }
  
    .filterGrid {
      grid-template-columns: 1fr;
    }
  
    .actionButtons {
      flex-direction: column;
    }
  }

  .categoryTitle {
    font-family: var(--inter);
    font-weight: 600;
    font-size: 18px;
    color: rgb(var(--color-text-title));
    display: flex;
    align-items: center;
  }
  .categoryTitle svg {
      margin-right: 0.3rem;
  }

  .categoryCount {
    font-family: var(--inter);
    font-size: 14px;
    background:rgb(var(--color-sf-button-gray));
    color: rgb(var(--color-text-title));
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
  }

  .modal {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .modal .content {
    width: 600px;
    height: 500px;
    background-color: rgb(var(--color-sf-white));
  }

  .modal .content .close {
    position: relative;
    right: -96%;
    top: 3px;
  }

  .modal .content h3 {
    color: rgb(var(--color-text-title));
  }

  .modalList {
    overflow-y: auto;
    height: 80%;
    display: flex;
    align-items: center;
    flex-direction: column;
    gap: 2rem;
  }

  .modalList .item {
    height: 20%;
    width: 90%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: solid 2px rgb(var(--color-sf-button-gray));
    border-radius: 20px;
    cursor: pointer;
    color: rgb(var(--color-text-title));
  }

  @media (max-width: 639px) {
    .modal .content {
      width: 90%;
      height: 600px;
    }

    .modal .content .close {
      right: -93%;
    }
  }

  .container {
    padding: 2rem 1rem;
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .title {
    font-size: 1.5rem;
    font-weight: 600;
    color: rgb(var(--color-sf-dark-purple));
    margin-bottom: 1.5rem;
  }
  
  .tabsContainer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }
  
  .tabsList {
    display: flex;
    gap: 0.5rem;
  }
  
  .tabTrigger {
    padding: 0.5rem 1.5rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    border: none;
    background: none;
  }
  
  .tabTriggerActive {
    background-color: rgb(var(--color-sf-primary));
    color: white;
  }
  
  .tabTriggerInactive {
    background-color: #f0f0f0;
    color: #666;
  }
  
  .filterActions {
    display: flex;
    gap: 0.5rem;
  }
  
  .filterButton {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    border: 1px solid #d1d5db;
    background-color: white;
    color: #374151;
  }
  
  .topFilters {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
  }
  
  .inputGroup {
    position: relative;
    display: flex;
    align-items: center;
    gap: 1rem;
    width: 97%
  }
  
  .inputLabel {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    width: 100px;
    flex-shrink: 0;
  }
  
  .inputWrapper {
    flex: 1;
    width: 100%;
  }
  
  .inputField {
    width: 100%;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    border: 1px solid #d1d5db;
    background-color: white;
  }
  
  .searchButton {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: rgb(var(--color-sf-primary));
    color: white;
    border: none;
    border-radius: 9999px;
    font-weight: 500;
    cursor: pointer;
    margin: 0 auto;
    width: auto;
  }
  
  .searchButton:hover {
    background-color: rgb(var(--color-sf-primary-container));
  }
  
  .multiSelectContainer {
    position: relative;
    width: 100%;
  }
  
  .selectedTags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 1.2rem;
    min-height: 2.5rem;
    border: 1px solid #d1d5db;
    border-radius: 1.8rem;
    background-color: white;
    margin-bottom: 0.5rem;
  }
  
  .selectedTag {
    display: flex;
    align-items: center;
    background-color: var(--color-sf-light-primary);
    color: var(--color-sf-dark-purple);
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 1.2rem;
  }
  
  .removeTag {
    margin-left: 0.25rem;
    font-size: 1rem;
    line-height: 1;
    color: rgb(var(--color-sf-dark-purple));
    background: none;
    border: none;
    cursor: pointer;
  }
  
  .multiSelect {
    width: 100%;
    padding: 1.1rem 1rem;
    border-radius: 1.9rem;
    border: 1px solid #d1d5db;
    background-color: white;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
  }
  
  .footer {
    margin-top: 4rem;
    padding: 3rem 0;
    background: linear-gradient(to bottom right, #1a6b7a, #13b8a6);
    color: white;
    border-radius: 1rem;
  }
  
  .footerContent {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  @media (min-width: 768px) {
    .footerContent {
      grid-template-columns: 1fr 1fr 1fr;
    }
  }
  
  .footerLogo {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .footerContact {
    margin-bottom: 1rem;
  }
  
  .footerSocial {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
  }
  
  .footerSocialIcon {
    width: 2.5rem;
    height: 2.5rem;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .footerSection {
    margin-bottom: 1rem;
  }
  
  .footerSectionTitle {
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1.125rem;
  }
  
  .footerLink {
    display: block;
    margin-bottom: 0.5rem;
    color: white;
    text-decoration: none;
  }
  
  .footerLink:hover {
    text-decoration: underline;
  }
  
  .copyright {
    margin-top: 3rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.8);
  }
  
  .copyrightLinks {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 0.5rem;
  }
  
  .copyrightLink {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
  }
  
  .copyrightLink:hover {
    text-decoration: underline;
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .inputGroup {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
  
    .inputLabel {
      width: 100%;
      margin-bottom: 0.25rem;
    }
  
    .topFilters {
      grid-template-columns: 1fr;
    }
  }
  
  /* Simplified version for the basic filter page */
  .filterGrid {
    display: none;
  }
  
  @media (max-width: 639px) {
    .filters-container {
      padding-bottom: 10px;
      align-items: start;
    }

    .filters-container span {
      text-align: left;
    }
  }
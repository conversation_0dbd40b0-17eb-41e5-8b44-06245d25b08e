@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    --background: #ffffff;
    --foreground: #0f172a;

    --card: #ffffff;
    --card-foreground: #0f172a;

    --popover: #ffffff;
    --popover-foreground: #0f172a;

    --primary: #0f172a;
    --primary-foreground: #f8fafc;
    --primary-light: #f1f5f9;

    --secondary: #f1f5f9;
    --secondary-foreground: #0f172a;

    --muted: #f1f5f9;
    --muted-foreground: #64748b;

    --accent: #f1f5f9;
    --accent-foreground: #0f172a;

    --destructive: #ef4444;
    --destructive-foreground: #f8fafc;
    --destructive-light: #fee2e2;

    --success: #10b981;

    --border: #e2e8f0;
    --input: #e2e8f0;
    --ring: #94a3b8;

    --radius: 0.5rem;
}

.dark {
    --background: #0f172a;
    --foreground: #f8fafc;

    --card: #1e293b;
    --card-foreground: #f8fafc;

    --popover: #1e293b;
    --popover-foreground: #f8fafc;

    --primary: #f8fafc;
    --primary-foreground: #0f172a;
    --primary-light: #1e293b;

    --secondary: #1e293b;
    --secondary-foreground: #f8fafc;

    --muted: #1e293b;
    --muted-foreground: #94a3b8;

    --accent: #1e293b;
    --accent-foreground: #f8fafc;

    --destructive: #ef4444;
    --destructive-foreground: #f8fafc;
    --destructive-light: #450a0a;

    --success: #10b981;

    --border: #334155;
    --input: #334155;
    --ring: #94a3b8;
}

* {
    @apply border-border;
}

body {
    background-color: var(--background);
    color: var(--foreground);
}

/* Select Component Fixes */
[data-radix-select-content] {
    background-color: var(--popover) !important;
    border: 1px solid var(--border) !important;
    border-radius: 0.375rem !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    z-index: 50 !important;
    min-width: var(--radix-select-trigger-width) !important;
}

[data-radix-select-viewport] {
    background-color: var(--popover) !important;
}

[data-radix-select-item] {
    background-color: var(--popover) !important;
    color: var(--popover-foreground) !important;
    padding: 0.5rem 0.75rem !important;
    cursor: pointer !important;
    border-radius: 0.25rem !important;
    margin: 0.125rem !important;
}

[data-radix-select-item]:hover,
[data-radix-select-item][data-highlighted] {
    background-color: var(--accent) !important;
    color: var(--accent-foreground) !important;
    outline: none !important;
}

[data-radix-select-item][data-state="checked"] {
    background-color: var(--primary) !important;
    color: var(--primary-foreground) !important;
}

/* Enhanced Select Component Fixes with Complete Opacity */
[data-radix-select-content] {
    background-color: var(--popover) !important;
    border: 1px solid var(--border) !important;
    border-radius: 0.375rem !important;
    box-shadow: 0 10px 38px -10px rgba(22, 23, 24, 0.35), 0 10px 20px -15px rgba(22, 23, 24, 0.2) !important;
    z-index: 50 !important;
    min-width: var(--radix-select-trigger-width) !important;
    opacity: 1 !important;
    backdrop-filter: none !important;
}

[data-radix-select-viewport] {
    background-color: var(--popover) !important;
    opacity: 1 !important;
}

[data-radix-select-item] {
    background-color: var(--popover) !important;
    color: var(--popover-foreground) !important;
    padding: 0.5rem 0.75rem !important;
    cursor: pointer !important;
    border-radius: 0.25rem !important;
    margin: 0.125rem !important;
    opacity: 1 !important;
    position: relative !important;
}

[data-radix-select-item]:hover,
[data-radix-select-item][data-highlighted] {
    background-color: var(--accent) !important;
    color: var(--accent-foreground) !important;
    outline: none !important;
    opacity: 1 !important;
}

[data-radix-select-item][data-state="checked"] {
    background-color: var(--primary) !important;
    color: var(--primary-foreground) !important;
    opacity: 1 !important;
}

/* Light theme specific */
:root [data-radix-select-content] {
    background-color: #ffffff !important;
}

:root [data-radix-select-item] {
    background-color: #ffffff !important;
    color: #0f172a !important;
}

/* Dark theme specific */
.dark [data-radix-select-content] {
    background-color: #1e293b !important;
}

.dark [data-radix-select-item] {
    background-color: #1e293b !important;
    color: #f8fafc !important;
}

.dark [data-radix-select-item]:hover,
.dark [data-radix-select-item][data-highlighted] {
    background-color: #334155 !important;
    color: #f8fafc !important;
}

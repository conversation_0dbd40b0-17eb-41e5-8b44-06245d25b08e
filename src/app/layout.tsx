import {dm, inter, poppins} from "@/styles/fonts";
import "@/styles/global.css";
import {SessionProvider} from "next-auth/react";
import GlobalProvider from "./Providers/GlobalProvider";
import LanguageProvider from "./Providers/LanguageProvider";
import {ThemeProvider} from "./Providers/ThemeProvider";

export const metadata = {
    title: "Tiba Invest",
    description: "Tiba Invest",
};

export default function RootLayout({children}: {children: React.ReactNode}) {
    return (
        <html lang="en" className={`${poppins.variable} ${inter.variable} ${dm.variable}`}>
            <head>
                <meta charSet="utf-8" />
                <meta name="viewport" content="width=device-width, initial-scale=1" />
                <meta name="description" content={metadata.description} />
                <title>{metadata.title}</title>
                <link rel="icon" href="/icons/favicon-32x32.png" sizes="32x32" />
                <link rel="icon" href="/icons/favicon-192x192.png" sizes="192x192" />
                <link rel="apple-touch-icon" href="/icons/favicon-180x180.png" />
                <meta name="msapplication-TileImage" content="/icons/favicon-270x270.png" />
            </head>
            <body className="flex flex-col min-h-screen">
                <ThemeProvider>
                    <SessionProvider>
                        <LanguageProvider>
                            <GlobalProvider>{children}</GlobalProvider>
                        </LanguageProvider>
                    </SessionProvider>
                </ThemeProvider>
            </body>
        </html>
    );
}

import { StrapiDefaultResponseArray, StrapiItem } from "@/types/utils/strapiAPI"
import { YEAR_TO_SEARCH } from "@/utils/constants"
import moment from "moment"

type HistoricalDividend = {
    document_date: string
    document_type_year_or_quarter?:	string
    currency_symbol?:	string
    declaration_date?:	string
    record_date?:	string
    payment_date?:	string
    value?:	number
    unadjusted_value: number
    createdAt?:	string
    updatedAt?:	string
}


export async function GET(
    request: Request,
    { params }: { params: { id: string } }
) {

    try {

        const { API_URL, API_EODHD, EODHD_TOKEN } = process.env
    
        const { searchParams } = new URL(request.url)
        const  symbol_code = searchParams.get('symbol_code')
        const { id } = params
    
        const current_date = new Date()
    
        const end_date = moment(current_date).format('yyyy-MM-DD')
        const start_date = moment(current_date).subtract(YEAR_TO_SEARCH, 'y').format('yyyy-MM-DD')
    
        const api_url = `${API_URL}/api/historical-dividends?sort=document_date:DESC&filters[ticker_internal][id][$eq]=${id}&filters[document_date][$gte]=${start_date}&pagination[pageSize]=100`
    
        const res = await fetch(api_url)
    
        const data: StrapiDefaultResponseArray = await res.json()
    
        const dividends: HistoricalDividend[] = data.data.map((item: StrapiItem) => ({
            ...item.attributes,
            document_date: item.attributes.document_date.split('-')[0] || ''
        }))
    
        const holder = {}
    
        dividends.forEach(function(d) {
            if (holder.hasOwnProperty(d.document_date)) {    
              holder[d.document_date] = { 
                value: holder[d.document_date].value + d.unadjusted_value, 
                dy: 0,
                count: holder[d.document_date].count + 1
            };
            } else {
              holder[d.document_date] = { 
                value: d.unadjusted_value, 
                dy: 0,
                count: 1
            };
            }
        });
          
        const eodhd_url =  `${API_EODHD}/eod/${symbol_code}?api_token=${EODHD_TOKEN}&fmt=json&from=${start_date}&to=${end_date}&period=m`

        const response_history = await fetch(eodhd_url)
        
        const price_history = await response_history.json()
    
        const holder_price = {}
    
        price_history.forEach(function(d) {
            const year  = d?.date.split('-')[0]
    
            if (!holder_price.hasOwnProperty(year)) {
                holder_price[year] = []
            }
            holder_price[year].push({ price: d.close, date: d.date });
        });
    
        const years = Object.keys(holder_price)
    
        for (let i in years) {
            const year = years[i]
            const price = holder_price[year][holder_price[year].length - 1].price
            
            if (holder[year] && holder[year].value) {
    
                const dividends_avg = holder[year].value/holder[year].count
    
                holder[year].dy = parseFloat(((dividends_avg/price)*100).toFixed(2))
            }
        }
    
    
        return Response.json({dividends: holder, prices: holder_price })
    } catch(err: any)  {

        console.log(err)

        return Response.json({ message: err.message })
    }
}

import {NextRequest, NextResponse} from "next/server";

const API_URL = process.env.API_URL;

export async function GET(req: NextRequest, {params}: {params: {id: string}}) {
    const {id} = params;

    const year = new Date().getFullYear() - 5;

    try {
        const strapiRes = await fetch(`${API_URL}/api/cash-flow/find/${id}/y`);

        if (!strapiRes.ok) {
            const error = await strapiRes.json();

            console.log("error", error);

            return NextResponse.json({error: "Failed to fetch from Strapi"}, {status: strapiRes.status});
        }

        const data = await strapiRes.json();

        return NextResponse.json(data);
    } catch (error) {
        return NextResponse.json({error: "Internal server error"}, {status: 500});
    }
}

import {NextRequest, NextResponse} from "next/server";

const API_URL = process.env.API_URL;

export async function GET(req: NextRequest, {params}: {params: {id: string}}) {
    const {id} = params;

    const year = new Date().getFullYear() - 5;

    try {
        const strapiRes = await fetch(
            `${API_URL}/api/fundamentals-balance-sheets?filters[ticker_internal][$eq]=${id}&filters[document_date][$gte]=${year}-01-01&filters[document_type_year_or_quarter][$eq]=y&sort=document_date:desc&pagination[limit]=5`,
        );

        if (!strapiRes.ok) {
            return NextResponse.json({error: "Failed to fetch from Strapi"}, {status: strapiRes.status});
        }

        const {data} = await strapiRes.json();

        const balance_sheet = data.map((item) => ({id: item.id, ...item.attributes}));

        return NextResponse.json(balance_sheet);
    } catch (error) {
        return NextResponse.json({error: "Internal server error"}, {status: 500});
    }
}

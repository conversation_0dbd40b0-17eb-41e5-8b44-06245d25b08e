import { StrapiDefaultResponseArray } from "@/types/utils/strapiAPI";
import { NextRequest } from "next/server";

export async function GET(req: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const { id: symbol_code } = params
        const { API_URL } = process.env
        
        const tickerResponse = await fetch(`${API_URL}/api/list-of-tickers?filters[primary_ticker_eodhd][$eq]=${symbol_code}&populate=*`, { next: { revalidate: 60 } })

        const tickerDataArray: StrapiDefaultResponseArray = await tickerResponse.json()

        const ticker = { id: tickerDataArray?.data[0].id, ...tickerDataArray?.data[0].attributes }
                
        const statisticsResponse = await fetch(`${API_URL}/api/statistics-of-tickers?filters[symbol_code][$eq]=${ticker.primary_ticker_eodhd}`, { next: { revalidate: 60 } })
        const statistics = await statisticsResponse.json()

        let statisticsData: any = {}

        if (statistics && statistics.data) {
            [statisticsData] = statistics.data
        }

        const sector = ticker?.sector?.data
            ? ticker.sector.data.id
            : null

        return Response.json({
            ticker: {...ticker, sector },
            statistics: statisticsData?.attributes || {}
        })

    } catch(error: any) {
        throw new Error(`failed to load data ${error.message}`)
    }
}
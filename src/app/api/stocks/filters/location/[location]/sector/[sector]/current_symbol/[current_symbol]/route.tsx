import {StrapiDefaultResponseArray} from "@/types/utils/strapiAPI";
import {NextRequest} from "next/server";

export async function GET(request: NextRequest, {params}: {params: {location: string; sector: string; current_symbol: string}}) {
    try {
        try {
            const {location, sector, current_symbol} = params;

            const url = `${process.env.API_URL}/api/statistics-of-tickers?filters[$and][0][ticker_internal][sector][$eq]=${sector}&[$and][1][ticker_internal][country_code][$eq]=${location}&[$and][2][ticker_internal][primary_ticker_eodhd][$ne]=${current_symbol}&pagination[pageSize]=5&populate=*`;

            const res = await fetch(url);

            const {data}: StrapiDefaultResponseArray = await res.json();

            if (!res.ok) throw Error("Error when find comparison items");

            const response = data.map((item) => ({
                id: item.id,
                ...item.attributes,
                ticker_internal: {
                    id: item.attributes.ticker_internal.data.id,
                    ...item.attributes.ticker_internal.data.attributes,
                },
            }));

            const different = response.filter((item) => item.ticker_internal.primary_ticker_eodhd !== current_symbol).slice(0, 4) || [];

            return Response.json(different);
        } catch (e: any) {
            console.log("error", e.message);
        }

        return Response.json({});
    } catch (error: any) {
        if (error.response) {
            return Response.json(
                {
                    data: error.response.data,
                    message: error.response.message,
                },
                {
                    status: error.response.status,
                },
            );
        }

        return Response.json(
            {
                message: error.message,
            },
            {
                status: 400,
            },
        );
    }
}

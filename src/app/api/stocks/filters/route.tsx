import {FilterResponse} from "@/types/api/FiltersResponse";
import {StrapiDefaultResponseArray} from "@/types/utils/strapiAPI";

export async function GET() {
    const {API_URL} = process.env;

    let api_url = `${API_URL}/api/statistics-of-filters?pagination[pageSize]=50&populate=*`;

    const res = await fetch(api_url, {cache: "no-store"});

    if (!res.ok) {
        return Response.json([]);
    }

    const {data}: StrapiDefaultResponseArray = await res.json();

    const response: FilterResponse[] = data.map((item) => ({
        id: item.id,
        ...item.attributes,
        actived: true,
        filter_category: {
            id: item.attributes.filter_category.data.id,
            ...item.attributes.filter_category.data.attributes,
            isEnabled: true,
        },
    }));

    return Response.json(response);
}

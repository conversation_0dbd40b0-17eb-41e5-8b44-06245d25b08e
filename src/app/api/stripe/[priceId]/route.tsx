import {auth} from "@/auth";
import {api} from "@/services/Requests";
import {NextRequest} from "next/server";

export const fetchCache = "force-no-store";
export const revalidate = 0;

export async function GET(request: NextRequest, {params}: {params: {priceId: string}}) {
    try {
        const session: any = await auth();
        const {access_token, user} = session;
        const {id} = user;

        const {priceId} = params;

        const url = `${process.env.API_URL}/api/product/getCustomerToken?id=${id}&priceId=${priceId}`;
        const {data} = await api.get(url, {
            headers: {
                Authorization: `Bearer ${access_token}`,
                "Content-Type": "application/json",
            },
        });
        return Response.json(data);
    } catch (error: any) {
        if (error.response) {
            return Response.json(
                {
                    data: error.response.data,
                    message: error.response.message,
                },
                {
                    status: error.response.status,
                },
            );
        }
        return Response.json(
            {
                message: error.message,
            },
            {
                status: 400,
            },
        );
    }
}

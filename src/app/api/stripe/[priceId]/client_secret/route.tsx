import { auth } from "@/auth"
import { api } from "@/services/Requests"
import { NextRequest } from "next/server"

export const fetchCache = 'force-no-store'
export const revalidate = 0

export async function GET(request: NextRequest) {
    try {
        const session: any = await auth()

        const { access_token, user } = session
        const { id } = user

        const { searchParams } = new URL(request.url)
        const page = searchParams.get('page')
        const pageSize = searchParams.get('pageSize')

        const url = `${process.env.API_URL}/api/watchlists?filters[$and][0][user][$eq]=${id}&filters[$and][1][is_enable][$eq]=true&populate[statistics_of_ticker][populate][ticker_internal]=*&pagination[page]=${page}&pagination[pageSize]=${pageSize}`

        const { data } = await api.get(url,
            {
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                }
            }
        )
        return Response.json(data)
    } catch (error: any) {
        if (error.response) {
            return Response.json({
                data: error.response.data,
                message: error.response.message,
            }, {
                status: error.response.status,
            })
        }
        return Response.json({
            message: error.message
        }, {
            status: 400,
        })
    }
}
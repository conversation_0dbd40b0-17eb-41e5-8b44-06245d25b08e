import type {NextApiRequest, NextApiResponse} from "next";
import {checkIfAgendaIsConnected} from "@/lib/utils";
import {_cleanJobLogs} from "../../../lib/jobDefinitions";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== "POST") {
        return res.status(405).json({error: "Method not allowed"});
    }

    await checkIfAgendaIsConnected();

    try {
        // Run the job logs cleanup function
        await _cleanJobLogs();

        return res.status(200).json({
            message: "Job logs cleanup completed successfully",
        });
    } catch (error) {
        console.error("Error cleaning up job logs:", error);
        return res.status(500).json({error: "Failed to clean up job logs"});
    }
}

import type {NextApiRequest, NextApiResponse} from "next";
import {getAgenda} from "../../../lib/agenda";
import {checkIfAgendaIsConnected} from "@/lib/utils";
import {ObjectId} from "mongodb";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== "POST") {
        return res.status(405).json({error: "Method not allowed"});
    }

    await checkIfAgendaIsConnected();

    const {id} = req.body;

    if (!id) {
        return res.status(400).json({error: "Job ID is required"});
    }

    try {
        const agenda = getAgenda();
        const db = agenda._mdb;

        // Find the job by ID
        let objectId;
        try {
            objectId = new ObjectId(id);
        } catch (error) {
            console.error("Invalid job ID:", error);
            return res.status(400).json({error: "Invalid job ID format"});
        }

        // Update the job to set lockedAt to null (unlock)
        const agendaJobsCollection = db.collection("agendaJobs");
        const updateResult = await agendaJobsCollection.updateOne({_id: objectId}, {$set: {lockedAt: null}});

        if (updateResult.matchedCount === 0) {
            return res.status(404).json({error: "Job not found"});
        }

        // Delete any job history records with status "started" for this job (clear history)
        const jobHistoryCollection = db.collection("jobHistory");
        const deleteResult = await jobHistoryCollection.deleteMany({
            jobId: id,
            status: "started",
        });

        console.log(`Unlocked job ${id}, set lockedAt to null`);
        console.log(`Deleted ${deleteResult.deletedCount} started job history records`);

        return res.status(200).json({
            message: "Job unlocked successfully",
            jobId: id,
            deletedHistoryRecords: deleteResult.deletedCount,
        });
    } catch (error) {
        console.error("Error unlocking job:", error);
        return res.status(500).json({error: "Failed to unlock job"});
    }
}

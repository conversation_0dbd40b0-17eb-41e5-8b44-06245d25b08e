import { NextApiRequest, NextApiResponse } from 'next';
import { ListOfTickers } from '../../../entities/consolidated_data/ListOfTickers';
import { getPrices } from '../../../controller/priceController';
import { addLogJobExecution } from '../../../lib/agenda';
import { LogLevel } from '../../../utils/types/logs/log';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'GET') {
        return res.status(405).json({ error: 'Method not allowed' });
    }

    try {
        const { limit = '10', tickers } = req.query;
        const limitNum = parseInt(limit as string, 10);

        let testTickers: ListOfTickers[] = [];

        if (tickers && typeof tickers === 'string') {
            // Test with specific tickers
            const tickerCodes = tickers.split(',').map(t => t.trim());
            console.log(`Testing with specific tickers: ${tickerCodes.join(', ')}`);
            
            testTickers = await ListOfTickers.findAll({
                where: {
                    primary_ticker_eodhd: tickerCodes,
                    is_enable: 1
                },
                limit: limitNum
            });
        } else {
            // Test with random tickers
            console.log(`Testing with ${limitNum} random tickers`);
            testTickers = await ListOfTickers.findAll({
                where: {
                    is_enable: 1
                },
                order: [['id', 'ASC']], // Use deterministic order for testing
                limit: limitNum
            });
        }

        if (testTickers.length === 0) {
            return res.status(404).json({
                success: false,
                error: 'No tickers found for testing'
            });
        }

        console.log(`Starting price test with ${testTickers.length} tickers`);
        const startTime = Date.now();

        // Test the price fetching functionality
        const [validPrices, invalidPrices] = await getPrices(testTickers);
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        const averageTimePerTicker = testTickers.length > 0 ? Math.round(duration / testTickers.length) : 0;

        const testResults = {
            tickersProcessed: testTickers.length,
            validPrices: validPrices.length,
            invalidPrices: invalidPrices.length,
            durationMs: duration,
            averageTimePerTicker,
            tickerCodes: testTickers.map(t => t.primary_ticker_eodhd)
        };

        console.log('Price test completed:', testResults);

        // Log the test execution
        addLogJobExecution(LogLevel.INFO, 'price-test-batch', 'Price batch test completed', testResults);

        return res.status(200).json({
            success: true,
            testResults,
            message: 'Price batch processing test completed successfully'
        });

    } catch (error) {
        console.error('Error in price test batch:', error);
        
        const errorMessage = error instanceof Error ? error.message : String(error);
        addLogJobExecution(LogLevel.ERROR, 'price-test-batch', 'Price batch test failed', { error: errorMessage });

        return res.status(500).json({
            success: false,
            error: errorMessage,
            message: 'Price batch processing test failed'
        });
    }
}

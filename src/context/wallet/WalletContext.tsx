"use client";

import {Wallet} from "@/types/api/Wallet";
import {WalletContextType} from "@/types/context/WalletContext";
import {createContext, useCallback, useContext, useState} from "react";

type Props = {
    children;
};

export const WalletContext = createContext<WalletContextType>({
    wallets: [],
    setWallets: (Wallets) => {},
    fetchWallets: () => {},
});

export function WalletProvider({children}: Props) {
    const [wallets, setWallets] = useState<Wallet[]>([]);

    const fetchWallets = useCallback(async () => {
        const response = await fetch("/api/wallets");

        if (response.ok) {
            const data: Wallet[] = await response.json();
            setWallets(data || []);
        }
    }, [setWallets]);

    return (
        <WalletContext.Provider
            value={{
                wallets,
                setWallets,
                fetchWallets,
            }}>
            {children}
        </WalletContext.Provider>
    );
}

export function useWallets(): WalletContextType {
    const {wallets, setWallets, fetchWallets} = useContext(WalletContext);

    return {
        wallets,
        setWallets,
        fetchWallets,
    };
}

"use client";

import {StatisticOfTickerIndicatorRepository} from "@/repository/stock/StatisticOfTickerIndicatorRepository";
import {Assets, StatisticsHistoryResponse} from "@/types/api/StatisticsHistoryResponse";
import {StatisticsResponse} from "@/types/api/StatisticsResponse";
import {CalcHistory, StatisticsContextType} from "@/types/context/StatisticsContext";
import {createContext, useContext, useState} from "react";

type Props = {
    children;
};

export const StatisticsContext = createContext<StatisticsContextType>({
    statistics: null,
    pagination: {pageIndex: 0, pageSize: 50},
    statisticsHistory: [],
    assetsHistory: [],
    setStatistics: (statistics) => {},
    setPagination: (pagination) => {},
    setStatisticsHistory: (statistics) => {},
    setAssetsHistory: (statistics) => {},
    calcStatisticsHistory: ({balance_sheet, cash_flow, income_statement, prices, dividends, statistics}: CalcHistory) => ({statisticsHistory: [], assetsHistory: []}),
});

export function StatisticsProvider({children}: Props) {
    const [statistics, setStatistics] = useState<StatisticsResponse | null>(null);
    const [statisticsHistory, setStatisticsHistory] = useState<StatisticsHistoryResponse[]>([]);
    const [assetsHistory, setAssetsHistory] = useState<Assets[]>([]);
    const [pagination, setPagination] = useState({pageIndex: 0, pageSize: 50});

    function calcStatisticsHistory({balance_sheet, cash_flow, income_statement, prices, dividends, statistics}: CalcHistory): {
        statisticsHistory: StatisticsHistoryResponse[];
        assetsHistory: Assets[];
    } {
        const years = Object.keys(balance_sheet);

        const ticker = {};

        const statisticsHistory: StatisticsHistoryResponse[] = [];

        const assetsHistory: Assets[] = [];

        for (let i = 0; i < years.length; i++) {
            const year = parseInt(years[i]);

            const dividend = dividends[year] || [];

            const dividend_value = dividend?.value || 0;

            const prices_year: any = prices[year] || [];

            const current_price = prices_year[prices_year?.length - 1]?.price || 0;

            const oldTicker = {...ticker, date: year, price: current_price};

            const current_balance_sheet = balance_sheet[year];
            const current_income_statement = income_statement[year];

            const current_cash_flow = cash_flow[year] || null;
            const income_statement_last_year = income_statement[year - 1] || [];

            const indicator = new StatisticOfTickerIndicatorRepository(oldTicker, current_balance_sheet, current_income_statement, income_statement_last_year, current_cash_flow);

            indicator.createTotalRevenue();

            indicator.createAssetTurnover();
            indicator.createBvps();
            indicator.createCurrentRatio();
            indicator.createEbit();
            indicator.createEbitda();
            indicator.createEbitdaMargin();
            indicator.createEpsDilutedCurrent();
            indicator.createEpsLastDate();

            indicator.createGrossMargin();
            indicator.createNetDebt();
            indicator.createNetDebtEbit();
            indicator.createNetDebtEbitda();
            indicator.createNetDebtShareholdersEquity();
            indicator.createNetMargin();
            indicator.createOperationMargin();
            indicator.createPayoutRatio(dividend_value);

            indicator.createRoeRoaRoic();
            indicator.createShareholderEquityRatio();
            indicator.createTotalDebtToTotalAssetsRatio();
            indicator.createDividendYield();
            indicator.createMarketCapitalization();
            indicator.createPriceToCashFlow();
            indicator.createEbitRatio();
            indicator.createPe();
            indicator.createPegRatio();

            indicator.createPriceWorkingCapitalShare();
            indicator.createPriceToBook();
            indicator.createPriceSalesRatio();
            indicator.createEV();
            indicator.createEvEbit();

            indicator.ticker.date = year.toString();

            statisticsHistory.push(indicator.ticker);
            indicator.total_assets;

            const asset: Assets = {
                date: year.toString(),
                total_assets: indicator.total_assets,
                total_debt: indicator.total_debt,
            };

            delete indicator.ticker.createdAt;
            delete indicator.ticker.updatedAt;
            delete indicator.ticker.symbol_code;

            assetsHistory.push(asset);
        }

        const current_year = new Date().getFullYear();

        if (statisticsHistory[statisticsHistory.length - 1] && statisticsHistory[statisticsHistory.length - 1].date === statistics.date) {
            statisticsHistory[statisticsHistory.length - 1] = {
                ...statisticsHistory[statisticsHistory.length - 1],
                ...statistics,
            };
        }

        return {
            statisticsHistory: statisticsHistory,
            assetsHistory: assetsHistory,
        };
    }

    return (
        <StatisticsContext.Provider
            value={{
                statistics,
                statisticsHistory,
                assetsHistory,
                pagination,
                setPagination,
                setStatistics,
                setStatisticsHistory,
                calcStatisticsHistory: calcStatisticsHistory as unknown as ({balance_sheet, cash_flow, income_statement, prices, dividends, statistics}: CalcHistory) => {
                    statisticsHistory: StatisticsHistoryResponse[];
                    assetsHistory: Assets[];
                },
                setAssetsHistory,
            }}>
            {children}
        </StatisticsContext.Provider>
    );
}

export function useStatistics(): StatisticsContextType {
    const {statistics, pagination, statisticsHistory, assetsHistory, setStatistics, setStatisticsHistory, calcStatisticsHistory, setAssetsHistory, setPagination} = useContext(StatisticsContext);

    return {statistics, pagination, statisticsHistory, assetsHistory, setStatistics, setStatisticsHistory, calcStatisticsHistory, setAssetsHistory, setPagination};
}

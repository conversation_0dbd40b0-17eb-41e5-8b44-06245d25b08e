import {TickerResponse} from "@/types/api/TickerResponse";
import {SearchTickersContextType} from "@/types/context/SearchTickersContext";
import {createContext, useCallback, useContext, useEffect, useState} from "react";

type Props = {
    children;
};

export const SearchTickersContext = createContext<SearchTickersContextType>({
    tickers: [],

    fetchTickers: () => {},
    setTickers: (tickers) => {},
});

export function SearchTickersProvider({children}: Props) {
    const [tickers, setTickers] = useState<TickerResponse[]>([]);

    const fetchTickers = useCallback(async () => {
        const res = await fetch("/api/stocks/search");

        if (res.ok) {
            const data: TickerResponse[] = await res.json();

            setTickers(data);
        }
    }, [setTickers]);

    useEffect(() => {
        fetchTickers();
    }, [fetchTickers]);

    return (
        <SearchTickersContext.Provider
            value={{
                tickers,

                fetchTickers,
                setTickers,
            }}>
            {children}
        </SearchTickersContext.Provider>
    );
}

export function useSearchTicker(): SearchTickersContextType {
    const {
        tickers,

        fetchTickers,
        setTickers,
    } = useContext(SearchTickersContext);

    return {
        tickers,

        fetchTickers,
        setTickers,
    };
}

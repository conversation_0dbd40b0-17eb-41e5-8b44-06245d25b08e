"use client";

import {TickerResponse} from "@/types/api/TickerResponse";
import {WatchListContextType, WatchListType} from "@/types/context/WatchListContextType";
import {PaginationReactTable} from "@/types/utils/MaterialTable";
import {StrapiDefaultResponseArray} from "@/types/utils/strapiAPI";
import axios from "axios";
import {signOut} from "next-auth/react";
import {createContext, useCallback, useContext, useState} from "react";
import {useTranslation} from "react-i18next";
import {toast} from "react-toastify";

type Props = {
    children;
};

export const WatchListContext = createContext<WatchListContextType>({
    list: [],
    pagination: {
        pageIndex: 1,
        pageSize: 10,
    },
    rowCount: 10,
    isLoading: false,
    pageCount: 0,
    setList: (list) => {},
    setPagination: (pagination) => {},
    fetchWatchList: (pagination) => {},
    setRowCount: (rowCount) => {},
    setisLoading: (isLoading) => {},
});

export function WatchListProvider({children}: Props) {
    const [list, setList] = useState<WatchListType[]>([]);
    const [isLoading, setisLoading] = useState<boolean>(false);
    const [pagination, setPagination] = useState<PaginationReactTable>({
        pageIndex: 1,
        pageSize: 10,
    });
    const [pageCount, setPageCount] = useState(0);
    const [rowCount, setRowCount] = useState(10);

    const {t} = useTranslation();

    const errorMessage = (message) =>
        toast.error(message, {
            position: "top-right",
        });

    const fetchWatchList = useCallback(
        async ({pageIndex = 0, pageSize = 10}) => {
            setisLoading(true);

            try {
                const {data} = await axios.get(`/api/stocks/watchlist?page=${pageIndex}&pageSize=${pageSize}`);

                const res: StrapiDefaultResponseArray = data;

                const list: WatchListType[] = res.data.map((item) => {
                    const price = item.attributes.price;
                    const current_price = item.attributes.statistics_of_ticker.data.attributes.price || 0;

                    const calc = (current_price * 100) / price - 100;

                    const ticker: TickerResponse = item.attributes.statistics_of_ticker.data.attributes.ticker_internal.data.attributes;
                    const ticker_id: number = item.attributes.statistics_of_ticker.data.attributes.ticker_internal.data.id;

                    return {
                        id: item.id,
                        name: ticker.name || "",
                        symbol_code: item.attributes.statistics_of_ticker.data.attributes.symbol_code || 0,
                        price,
                        createdAt: item.attributes.createdAt,
                        updatedAt: item.attributes.updatedAt,
                        is_enable: item.attributes.is_enable,
                        current_price,
                        after_added: calc,
                        ticker_id,
                    };
                });

                setList(list);
                setisLoading(false);
                setRowCount(res.meta.pagination.total);
                setPageCount(res.meta.pagination.pageCount);
            } catch (e: any) {
                let message = "";

                if (e.response.status === 401 || e.response.status === 403) {
                    message = t("error.session_expired");
                    setTimeout(
                        () =>
                            signOut({
                                redirect: true,
                                callbackUrl: "/signin",
                            }),
                        3000,
                    );
                } else {
                    message = e.message;
                }
                errorMessage(message);
            } finally {
                setisLoading(false);
            }
        },
        [setList, setRowCount, setisLoading, t],
    );

    return (
        <WatchListContext.Provider
            value={{
                list,
                pagination,
                isLoading,
                rowCount,
                pageCount,
                setRowCount,
                setList,
                fetchWatchList,
                setPagination,
                setisLoading,
            }}>
            {children}
        </WatchListContext.Provider>
    );
}

export function useWatchlist(): WatchListContextType {
    const {list, pagination, isLoading, rowCount, pageCount, setRowCount, setList, setPagination, fetchWatchList, setisLoading} = useContext(WatchListContext);

    return {
        list,
        pagination,
        isLoading,
        rowCount,
        pageCount,
        setRowCount,
        setList,
        setPagination,
        fetchWatchList,
        setisLoading,
    };
}

import {useFilters} from "@/context/stocks/FiltersContext";
import {Button} from "@/styles/components/Ui";
import {Drawer} from "@mui/material";
import axios from "axios";
import {useState} from "react";
import {useTranslation} from "react-i18next";
import {toast} from "react-toastify";
import Loading from "../Loading";
import {Input} from "../UI/Input";
import {Label} from "../UI/Label";

type Props = {
    isOpen: boolean;
    setIsOpen: (value: boolean) => void;
};

export default function SaveFilter({isOpen, setIsOpen}: Props) {
    const [isLoading, setIsLoading] = useState(false);
    const [filterName, setFilterName] = useState("");

    const {selectedFilters, setSelectedFilters} = useFilters();

    const {t} = useTranslation();

    const errorMessage = (message) =>
        toast.error(message, {
            position: "top-right",
        });

    const successMessage = (message) =>
        toast.success(message, {
            position: "top-right",
        });

    async function handleform(e) {
        e.preventDefault();
        setIsLoading(true);

        const filters = {...selectedFilters};

        filters.name = filterName;

        setSelectedFilters(filters);

        try {
            const {data} = await axios({
                url: "/api/stocks/filters/save",
                data: {
                    name: filterName,
                    payload: filters,
                },
                method: "POST",
            });

            setFilterName("");
            successMessage(t("saved_filters.success"));
            setIsOpen(false);
        } catch (e) {
        } finally {
            setIsLoading(false);
        }
    }

    return (
        <Drawer anchor="right" open={isOpen} onClose={() => setIsOpen(false)}>
            <div className="px-10 pt-5">
                <div>
                    <h5>{t("saved_filters.save_filter")}</h5>
                </div>
                <div className="mt-10">
                    <p>{t("saved_filters.save_desc")}</p>

                    {isLoading ? (
                        <Loading />
                    ) : (
                        <form onSubmit={handleform} className="w-[100%]">
                            <div className="grid gap-2 mt-4">
                                <Label  htmlFor="filter_name">{t("saved_filters.filter_name")}</Label>                                <Input id="text" name="filter_name" placeholder={t("saved_filters.filter_name")} value={filterName} required={true} onChange={(e) => setFilterName(e.target.value)} />
                            </div>

                            <div>
                                <Button className="w-full mt-8">{t("addasset.save")}</Button>
                            </div>
                        </form>
                    )}
                </div>
            </div>
        </Drawer>
    );
}

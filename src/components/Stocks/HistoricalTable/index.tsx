"use client";

import NoData from "@/components/NoData";
import {useMobile} from "@/context/use-mobile";
import type {StatisticsHistoryResponse} from "@/types/api/StatisticsHistoryResponse";
import {formatNumber} from "@/utils/functions";
import {ArrowDown, ArrowUp, Calendar, ChevronDown, ChevronRight, Filter, SortAsc, TrendingDown, TrendingUp} from "lucide-react";
import {useMemo, useState} from "react";
import {useTranslation} from "react-i18next";
import styles from "./styles.module.css";

interface StockHistoricalTableProps {
    historical: StatisticsHistoryResponse[];
}

interface IndicatorGroup {
    title: string;
    icon: string;
    indicators: string[];
}

type SortConfig = {
    year: string;
    direction: "asc" | "desc";
};

type YearSortDirection = "asc" | "desc" | null;

export function HistoricalTable({historical}: StockHistoricalTableProps) {
    const isMobile = useMobile();
    const allYears = useMemo(() => historical.map((item) => item.date || ""), [historical]);
    const {t} = useTranslation();

    const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
        price: true,
        profitability: false,
        debt: false,
        efficiency: false,
        growth: false,
    });

    const [selectedYears, setSelectedYears] = useState<string[]>(allYears);
    const [showYearFilter, setShowYearFilter] = useState(false);
    const [sortConfig, setSortConfig] = useState<SortConfig | null>(null);
    const [activeGroup, setActiveGroup] = useState<string | null>("price");
    const [yearSortDirection, setYearSortDirection] = useState<YearSortDirection>("desc");

    const indicatorGroups: Record<string, IndicatorGroup> = {
        price: {
            title: "history_table.categories.price_market",
            icon: "💰",
            indicators: ["price", "market_capitalization", "price_to_book", "price_to_cash_flow", "price_sales_ratio", "ev_ebit", "pe", "peg_ratio", "price_working_capital_share"],
        },
        profitability: {
            title: "history_table.categories.rentability",
            icon: "📈",
            indicators: ["eps_current", "eps_last_year", "gross_margin", "operation_margin", "net_margin", "ebitda_margin", "ebit", "ebitda", "roe", "roa", "roic"],
        },
        debt: {
            title: "history_table.categories.debt",
            icon: "⚖️",
            indicators: ["net_debt", "net_debt_ebitda", "net_debt_ebit", "net_debt_shareholders_equity", "shareholder_equity_ratio", "total_debt_to_total_assets_ratio"],
        },
        efficiency: {
            title: "history_table.categories.operational_efficiency",
            icon: "⚡",
            indicators: ["asset_turnover", "current_ratio"],
        },
        growth: {
            title: "history_table.categories.growth",
            icon: "🚀",
            indicators: ["payout_ratio", "ebit_ratio"],
        },
    };

    // Sort years based on the current year sort direction
    const sortedYears = useMemo(() => {
        if (!yearSortDirection) return selectedYears;

        return [...selectedYears].sort((a, b) => {
            const numA = Number.parseInt(a, 10);
            const numB = Number.parseInt(b, 10);

            if (!isNaN(numA) && !isNaN(numB)) {
                return yearSortDirection === "asc" ? numA - numB : numB - numA;
            }

            return yearSortDirection === "asc" ? a.localeCompare(b) : b.localeCompare(a);
        });
    }, [selectedYears, yearSortDirection]);

    // Filtered historical data based on selected years
    const filteredHistorical = useMemo(() => {
        return historical.filter((item) => selectedYears.includes(item.date || ""));
    }, [historical, selectedYears]);

    // Sort indicators based on the current sort configuration
    const getSortedIndicators = (indicators: string[], groupKey: string) => {
        if (!sortConfig || activeGroup !== groupKey) return indicators;

        return [...indicators].sort((a, b) => {
            const yearIndex = allYears.findIndex((year) => year === sortConfig.year);
            if (yearIndex === -1) return 0;

            const valueA = historical[yearIndex][a] || 0;
            const valueB = historical[yearIndex][b] || 0;

            return sortConfig.direction === "asc" ? valueA - valueB : valueB - valueA;
        });
    };

    const toggleSection = (sectionKey: string) => {
        setExpandedSections((prev) => ({
            ...prev,
            [sectionKey]: !prev[sectionKey],
        }));
        setActiveGroup(sectionKey);
    };

    const handleSort = (year: string) => {
        setSortConfig((current) => {
            if (current?.year === year) {
                return current.direction === "asc" ? {year, direction: "desc"} : null;
            }
            return {year, direction: "asc"};
        });
    };

    const toggleYearSelection = (year: string) => {
        setSelectedYears((prev) => (prev.includes(year) ? prev.filter((y) => y !== year) : [...prev, year]));
    };

    const selectAllYears = () => {
        setSelectedYears([...allYears]);
    };

    const deselectAllYears = () => {
        setSelectedYears([]);
    };

    const toggleYearSort = () => {
        setYearSortDirection((current) => {
            if (current === null || current === "desc") return "asc";
            if (current === "asc") return "desc";
            return "desc";
        });
    };

    const getTrendIcon = (indicator: string, years: string[]) => {
        if (years.length < 2) return null;

        const firstYear = years[0];
        const lastYear = years[years.length - 1];

        const firstValue = filteredHistorical.find((h) => h.date === firstYear)?.[indicator] || 0;
        const lastValue = filteredHistorical.find((h) => h.date === lastYear)?.[indicator] || 0;

        if (lastValue > firstValue) {
            return <TrendingUp className="w-4 h-4 text-green-500" />;
        } else if (lastValue < firstValue) {
            return <TrendingDown className="w-4 h-4 text-red-500" />;
        }
        return null;
    };

    const renderMobileCards = (groupKey: string, group: IndicatorGroup) => {
        const availableIndicators = group.indicators.filter((indicator) => historical.some((item) => item[indicator] !== undefined && item[indicator] !== null));

        if (availableIndicators.length === 0) return null;

        const sortedIndicators = getSortedIndicators(availableIndicators, groupKey);

        return (
            <div key={groupKey} className="mb-8">
                <button onClick={() => toggleSection(groupKey)} className={`${styles.categoryButton} ${expandedSections[groupKey] ? styles.categoryButtonActive : ""}`}>
                    <span className="text-2xl">{group.icon}</span>
                    <h2 className="text-lg font-bold text-left flex-1 uppercase">{t(group.title)}</h2>
                    {expandedSections[groupKey] ? <ChevronDown className="w-5 h-5" /> : <ChevronRight className="w-5 h-5" />}
                </button>

                {expandedSections[groupKey] && (
                    <div className="space-y-4 animate-in slide-in-from-top-2 duration-300">
                        {sortedYears.map((year) => (
                            <div key={year} className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
                                <div className="bg-[rgb(var(--color-sf-primary))] px-4 py-3 text-[rgb(var(--color-sf-on-primary))]">
                                    <div className="flex items-center justify-between">
                                        <h3 className="font-bold text-lg">{year}</h3>
                                        <span className="text-sm opacity-90">{group.icon}</span>
                                    </div>
                                </div>

                                <div className="p-4">
                                    <div className="space-y-3">
                                        {sortedIndicators.map((indicator) => {
                                            const item = filteredHistorical.find((h) => h.date === year);
                                            const value = item?.[indicator] ?? 0;

                                            return (
                                                <div key={`${indicator}-${year}`} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                                                    <div className="flex items-center gap-2 flex-1">
                                                        <span className="text-sm font-medium text-gray-800 leading-tight">{t(`indicator.${indicator}`)}</span>
                                                        {/* {getTrendIcon(indicator, sortedYears)} */}
                                                    </div>
                                                    <span className="text-sm font-bold text-gray-900 ml-2 text-right">{formatNumber(value, "pt-BR")}</span>
                                                </div>
                                            );
                                        })}
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        );
    };

    const renderDesktopTable = (groupKey: string, group: IndicatorGroup) => {
        const availableIndicators = group.indicators.filter((indicator) => historical.some((item) => item[indicator] !== undefined && item[indicator] !== null));

        if (availableIndicators.length === 0) return null;

        const sortedIndicators = getSortedIndicators(availableIndicators, groupKey);

        return (
            <div key={groupKey} className="mb-8">
                <button onClick={() => toggleSection(groupKey)} className={`${styles.categoryButton} ${expandedSections[groupKey] ? styles.categoryButtonActive : ""}`}>
                    <span className="text-2xl">{group.icon}</span>
                    <h2 className="text-xl font-bold text-left flex-1 uppercase">{t(group.title)}</h2>
                    {expandedSections[groupKey] ? <ChevronDown className="w-5 h-5" /> : <ChevronRight className="w-5 h-5" />}
                </button>

                {expandedSections[groupKey] && (
                    <div className={`${styles.tableContainer} animate-in slide-in-from-top-2 duration-300`}>
                        <table className={styles.table}>
                            <thead>
                                <tr className={styles.tableHeader}>
                                    <th className={`${styles.headerCell} text-gray-800`}>Indicador</th>
                                    {sortedYears.map((year) => (
                                        <th key={year} className={`${styles.headerCell} cursor-pointer hover:bg-gray-50 text-gray-800`} onClick={() => handleSort(year)}>
                                            <div className="flex items-center gap-1">
                                                {year}
                                                {sortConfig?.year === year && (sortConfig.direction === "asc" ? <ArrowUp className="w-4 h-4" /> : <ArrowDown className="w-4 h-4" />)}
                                            </div>
                                        </th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody>
                                {sortedIndicators.map((indicator, index) => (
                                    <tr key={indicator} className={index % 2 === 0 ? styles.rowEven : styles.rowOdd}>
                                        <td className={`${styles.indicator} text-gray-800`}>
                                            <div className="flex items-center gap-2">
                                                {t(`indicator.${indicator}`)}
                                                {/* {getTrendIcon(indicator, sortedYears)} */}
                                            </div>
                                        </td>
                                        {sortedYears.map((year) => {
                                            const item = filteredHistorical.find((h) => h.date === year);
                                            const value = item?.[indicator] ?? 0;

                                            return (
                                                <td key={`${indicator}-${year}`} className={`${styles.valueCell} text-gray-700`}>
                                                    {formatNumber(value, "pt-BR")}
                                                </td>
                                            );
                                        })}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>
        );
    };

    return (
        <div className="space-y-6">
            {historical.length > 0 ? (
                <div className="space-y-4">
                    <div className="flex flex-col sm:flex-row gap-4 mb-6 justify-between">
                        <div className="flex flex-wrap gap-2">
                            <div className="relative">
                                <button onClick={() => setShowYearFilter(!showYearFilter)} className={`${styles.filterButton} ${showYearFilter ? styles.filterButtonActive : ""}`}>
                                    <Filter className="w-5 h-5" />
                                    <span>{t("history_table.buttons.filter_years")}</span>
                                </button>

                                {showYearFilter && (
                                    <div className="absolute right-[-150px] top-full mt-2 bg-white border border-gray-200 rounded-lg shadow-xl z-50 p-4 min-w-[280px]">
                                        <div className="flex items-center justify-between mb-4">
                                            <h4 className="text-[14px] font-semibold text-gray-800">{t("history_table.buttons.select_years")}</h4>
                                            <div className="flex gap-2">
                                                <button
                                                    onClick={selectAllYears}
                                                    className="text-[14px] bg-[rgba(var(--color-sf-primary),0.1)] text-[rgb(var(--color-sf-primary))] hover:bg-[rgba(var(--color-sf-primary),0.2)] px-2 py-1 rounded font-medium transition-colors">
                                                    {t("history_table.buttons.all")}
                                                </button>
                                                <button onClick={deselectAllYears} className="text-[14px] bg-gray-50 text-gray-600 hover:bg-gray-100 px-2 py-1 rounded font-medium transition-colors">
                                                    {t("history_table.buttons.clear")}
                                                </button>
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-3 gap-2 max-h-[200px] overflow-y-auto">
                                            {allYears.map((year) => (
                                                <label key={year} className={`${styles.yearOption} ${selectedYears.includes(year) ? styles.yearOptionSelected : ""}`}>
                                                    <input type="checkbox" checked={selectedYears.includes(year)} onChange={() => toggleYearSelection(year)} className="sr-only" />
                                                    <span className="text-[16px] font-bold select-none">{year}</span>
                                                    {selectedYears.includes(year) && <div className={styles.yearDot}></div>}
                                                </label>
                                            ))}
                                        </div>

                                        <div className="mt-4 pt-3 border-t border-gray-200">
                                            <div className="text-xs text-gray-500 text-center">{/* {selectedYears.length} de {allYears.length} anos selecionados */}</div>
                                        </div>
                                    </div>
                                )}
                            </div>

                            <button onClick={toggleYearSort} className={`${styles.sortButton} `}>
                                <Calendar className="w-5 h-5" />
                                <span className="hidden sm:inline">
                                    {yearSortDirection === "asc" ? t("history_table.buttons.ascending") : yearSortDirection === "desc" ? t("history_table.buttons.descending") : "Ordenar"}
                                </span>
                            </button>

                            {!isMobile && (
                                <button
                                    onClick={() => {
                                        setSortConfig(null);
                                        setYearSortDirection("desc");
                                        setSelectedYears([...allYears]);
                                    }}
                                    className={styles.resetButton}>
                                    <span className="flex items-center gap-2">
                                        <SortAsc className="w-4 h-4" />
                                        <span className="hidden sm:inline">{t("history_table.buttons.reset")}</span>
                                    </span>
                                </button>
                            )}
                        </div>

                        <div className="flex flex-wrap gap-2">
                            <button onClick={() => setExpandedSections((prev) => Object.keys(prev).reduce((acc, key) => ({...acc, [key]: true}), {}))} className={styles.expandButton}>
                                {t("history_table.buttons.expand")}
                            </button>

                            <button onClick={() => setExpandedSections((prev) => Object.keys(prev).reduce((acc, key) => ({...acc, [key]: false}), {}))} className={styles.collapseButton}>
                                {t("history_table.buttons.collapse")}
                            </button>
                        </div>
                    </div>

                    {Object.entries(indicatorGroups).map(([groupKey, group]) => (isMobile ? renderMobileCards(groupKey, group) : renderDesktopTable(groupKey, group)))}
                </div>
            ) : (
                <NoData />
            )}
        </div>
    );
}

.tableContainer {
  background-color: rgb(var(--color-sf-surface));
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow-x: auto;
}

.table {
  width: 100%;
  min-width: 800px;
}

.tableHeader {
  border-bottom: 1px solid #e5e7eb;
}

.indicator {
  padding-left: 1rem;
  text-align: left;
  font-size: 1.4rem;
  font-weight: 600;
  color: rgb(var(--color-text-title));
  background-color: rgb(var(--color-sf-button-gray));
}

.headerCell {
  padding: 1rem 1.5rem;
  text-align: left;
  font-size: 1.4rem;
  font-weight: 600;
  color: rgb(var(--color-text-title));
}

.rowEven {
  background-color: #f9fafb;
}

.rowOdd {
  background-color: white;
}

.itemCell {
  padding: 1rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: rgb(var(--color-text-title));
}

.valueCell {
  padding: 1rem 1.5rem;
  font-size: 1.4rem;
  color: #6b7280;
}

.sectionHeader {
  background: linear-gradient(135deg, rgb(var(--color-sf-primary-container)), rgb(var(--color-sf-secondary-container)));
  border: 1px solid rgb(var(--color-sf-outline-variant));
  border-radius: 0.75rem;
  padding: 1rem 1.5rem;
  margin-bottom: 1rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.sectionHeader:hover {
  background: linear-gradient(135deg, rgb(var(--color-sf-primary)), rgb(var(--color-sf-secondary)));
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.sectionTitle {
  color: rgb(var(--color-text-title));
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
}

.expandIcon {
  color: rgb(var(--color-sf-on-surface-variant));
  transition: transform 0.2s ease-in-out;
}

.expandIcon.expanded {
  transform: rotate(90deg);
}

.categoryContainer {
  margin-bottom: 2rem;
}

.animateIn {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.controlButtons {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.controlButton {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.expandAllButton {
  background-color: rgb(var(--color-sf-primary-container));
  color: rgb(var(--color-sf-on-primary-container));
}

.collapseAllButton {
  background-color: rgb(var(--color-sf-surface-variant));
  color: rgb(var(--color-sf-on-surface-variant));
}

.controlButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Category Header Buttons */
.categoryButton {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 1rem;
  background-color: rgb(var(--color-sf-white));
  color: rgb(var(--color-sf-primary-container));
  border: 2px solid rgba(var(--color-sf-primary-container), 0.8);
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
  margin-bottom: 1rem;
  cursor: pointer;
}

.categoryButton h2 {
  color: rgb(var(--color-sf-primary-container));
}

.categoryButton:hover {
  background-color: rgb(var(--color-sf-primary));
}

.categoryButton:hover h2 {
  color: rgb(var(--color-sf-black));
}

.categoryButtonActive {
  background-color: rgb(var(--color-sf-primary-container));
  color: rgb(var(--color-sf-white));
  border-color: rgba(var(--color-sf-primary), 0.3);
}

.categoryButtonActive h2 {
  color: rgb(var(--color-sf-black));
}

/* Filter Button */
.filterButton {
  display: flex;
  align-items: center;
  height: 43px;
  gap: 0.5rem;
  padding: 0.8rem 0.75rem;
  font-size: 1.575rem;
  background-color: var(--color-sf-light-purple);
  border: 2px solid var(--color-sf-dark-purple);
  color: var(--color-sf-dark-purple);
  border-radius: 1.5rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.filterButton:hover {
   background-color: var(--color-sf-light-purple);
  border: 2px solid var(--color-sf-dark-purple);
  color: var(--color-sf-dark-purple);
}

.filterButtonActive {
  background-color: var(--color-sf-dark-purple);
  color: rgb(var(--color-sf-white));
}

/* Sort Button */
.sortButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  height: 43px;
  padding: 0 1.5rem;
  border-radius: 1.5rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  background-color: var(--color-sf-dark-purple);
  color: rgb(var(--color-sf-white));
}

.sortButton:hover {
  background-color: var(--color-sf-light-purple);
  border: 2px solid var(--color-sf-dark-purple);
  color: var(--color-sf-dark-purple);
}

.sortButtonActive {
  background-color: var(--color-sf-light-purple);
  border: 2px solid var(--color-sf-dark-purple);
  color: var(--color-sf-dark-purple);
}

/* Reset Button */
.resetButton {
  display: flex;
  align-items: center;
  height: 43px;
  gap: 0.5rem;
  padding: 0.8rem 0.75rem;
  font-size: 1.575rem;
  background-color: var(--color-sf-light-purple);
  border: 2px solid var(--color-sf-dark-purple);
  color: var(--color-sf-dark-purple);
  border-radius: 1.5rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.resetButton:hover {
  background-color: #f9fafb;
  border-color: #d1d5db;
}

/* Expand/Collapse Buttons */
.expandButton {
  padding: 0.5rem 0.75rem;
  font-size: 1.575rem;
  background-color: var(--color-sf-light-purple);
  border: 2px solid var(--color-sf-dark-purple);
  color: var(--color-sf-dark-purple);
  border-radius: 1.5rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.expandButton:hover {
  background-color: var(--color-sf-dark-purple);
  border: 2px solid var(--color-sf-dark-purple);
  color: rgb(var(--color-sf-white));
}

.expandButtoActived {
  background-color: var(--color-sf-dark-purple);
  border: 2px solid var(--color-sf-dark-purple);
  color: rgb(var(--color-sf-white));
}

.collapseButton {
  padding: 0.5rem 0.75rem;
  font-size: 1.575rem;
  background-color: var(--color-sf-light-purple);
  border: 2px solid var(--color-sf-dark-purple);
  color: var(--color-sf-dark-purple);
  border-radius: 1.5rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.collapseButton:hover {
  background-color: var(--color-sf-dark-purple);
  border: 2px solid var(--color-sf-dark-purple);
  color: rgb(var(--color-sf-white));
}

.collapseButtonActived {
  background-color: var(--color-sf-dark-purple);
  border: 2px solid var(--color-sf-dark-purple);
  color: rgb(var(--color-sf-white));
}

/* Year Selection */
.yearOption {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: pointer;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: 2px solid #e5e7eb;
  background-color: #f9fafb;
  color: #6b7280;
  transition: all 0.2s ease-in-out;
}

.yearOption:hover {
  background-color: #f3f4f6;
  border-color: #d1d5db;
}

.yearOptionSelected {
  background-color: var(--color-sf-dark-purple);
  color: white;
  border-color: rgba(var(--color-sf-dark-purple), 0.3);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Badge */
.badge {
  padding: 0.125rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 700;
}

.badgeDefault {
  background-color: rgba(var(--color-sf-on-primary), 0.2);
  color: rgb(var(--color-sf-on-primary));
}

.badgeActive {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Year Indicator Dot */
.yearDot {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: white;
}

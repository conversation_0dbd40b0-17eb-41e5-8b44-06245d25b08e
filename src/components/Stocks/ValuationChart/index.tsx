import {StatisticsHistoryResponse} from "@/types/api/StatisticsHistoryResponse";
import {BarController, BarElement, CategoryScale, Chart as ChartJS, Legend, LineController, LineElement, LinearScale, PointElement, Tooltip} from "chart.js";
import {useEffect, useState} from "react";
import {Chart} from "react-chartjs-2";
import {useTranslation} from "react-i18next";
import styles from "./styles.module.css";

type Props = {
    statisticsHistory: StatisticsHistoryResponse[];
};

ChartJS.register(LinearScale, CategoryScale, BarElement, PointElement, LineElement, Legend, Tooltip, LineController, BarController);

export function ValuationChart({statisticsHistory}: Props) {
    const [priceSales, setPriceSales] = useState<any[]>([]);
    const [priceBook, setPriceBook] = useState<any[]>([]);
    const [pe, setPe] = useState<any[]>([]);
    const [labels, setlabels] = useState<string[]>([]);

    const {t} = useTranslation();

    useEffect(() => {
        if (statisticsHistory.length > 0) {
            const labels = statisticsHistory.map((item) => item.date || "");
            const price_sales = statisticsHistory.map((item) => item.price_sales_ratio || null);
            const price_to_book = statisticsHistory.map((item) => item.price_to_book || null);
            const pe = statisticsHistory.map((item) => item.pe || null);

            setPriceSales(price_sales);
            setlabels(labels);
            setPriceBook(price_to_book);
            setPe(pe);
        }
    }, [statisticsHistory]);

    const options = {
        elements: {
            line: {
                tension: 0.4,
            },
        },
        responsive: true,
        interaction: {
            mode: "index" as const,
            intersect: false,
        },
        stacked: false,
        scales: {
            y: {
                type: "linear" as const,
                display: true,
                position: "left" as const,
            },
        },
    };

    const data = {
        labels,
        responsive: true,
        datasets: [
            {
                type: "bar" as const,
                label: t("indicator.price_sales_ratio"),
                backgroundColor: "yellow",
                data: priceSales,
                borderColor: "yellow",
                borderWidth: 3,
                borderRadius: 10,
                yAxisID: "y",
            },
            {
                type: "bar" as const,
                label: t("indicator.pe"),
                backgroundColor: "#13DCD2",
                data: pe,
                borderColor: "#13DCD2",
                borderWidth: 3,
                borderRadius: 10,
                yAxisID: "y",
            },
            {
                type: "bar" as const,
                label: t("indicator.price_to_book"),
                backgroundColor: "blue",
                data: priceBook,
                borderColor: "blue",
                borderWidth: 3,
                borderRadius: 10,
                yAxisID: "y",
            },
        ],
    };
    return (
        <div className="mt-10">
            <h2 className={styles.chartTitle}>{t("stock_charts.valuation_indicators")}</h2>
            <div className={styles.container}>
                <div className="flex sm:flex-row flex-col items-baseline justify-start  py-2">
                    <p className="text-text">
                        {" "}
                        <span className="text-dark font-bold uppercase">X</span>: {t("chart.year")}
                    </p>
                </div>
                <Chart type={"bar"} data={data} options={options} className="bg-white" />
            </div>
        </div>
    );
}

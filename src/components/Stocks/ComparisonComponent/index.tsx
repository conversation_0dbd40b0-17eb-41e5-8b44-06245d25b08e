import {StatisticsResponse} from "@/types/api/StatisticsResponse";
import {Folder} from "@mui/icons-material";
import {t} from "i18next";
import Link from "next/link";
import {useRouter} from "next/navigation";
import styles from "./styles.module.css";

interface StockComparisonProps {
    tickers: StatisticsResponse[];
}

export function StockComparison({tickers}: StockComparisonProps) {
    const router = useRouter();

    return (
        <div className="space-y-4 p-4 rounded-lg">
            {tickers.map((item, index) => (
                <div key={index} className={styles.comparisonItem}>
                    <div className={styles.comparisonIcon}>
                        <Folder sx={{fontSize: 30}} />
                    </div>
                    <div className={styles.comparisonInfo}>
                        <Link href={`/stocks/${item.ticker_internal.primary_ticker_eodhd}`}>
                            <div className={styles.comparisonSymbol}>{item?.ticker_internal?.symbol_code}</div>
                            <div className={styles.comparisonName}>{item?.ticker_internal?.name}</div>
                        </Link>
                    </div>
                    <div className={styles.comparisonValue}>
                        <div className={styles.comparisonInvestment}>{item.price?.toFixed(2) || 0.0}</div>
                        <div className={styles.comparisonLabel}>{t("indicator.price")}</div>
                    </div>
                    <div className={styles.comparisonReturn}></div>
                </div>
            ))}
        </div>
    );
}

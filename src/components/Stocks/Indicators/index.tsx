import {StatisticsResponse} from "@/types/api/StatisticsResponse";
import {formatNumber} from "@/utils/functions";
import {useTranslation} from "react-i18next";
import styles from "./styles.module.css";

const categories_labels = {
    Valuation: ["bvps", "ebit", "ebit_ratio", "ev_ebit", "pe", "price_sales_ratio", "price_to_book", "shareholder_equity_ratio", "price_to_cash_flow"],
    Profitability: ["ebitda_margin", "eps_current", "eps_last_year", "gross_margin", "net_margin", "operation_margin", "peg_ratio", "price", "roa", "roe", "roic"],
    "Cash/Share": ["asset_turnover", "current_ratio", "market_capitalization", "price_working_capital_share"],
    Debt: ["ebitda", "net_debt", "net_debt_ebit", "net_debt_ebitda", "net_debt_shareholders_equity", "total_debt_to_total_assets_ratio"],
    Dividends: ["dividend_yield", "payout_ratio"],
};

interface IndicatorsProps {
    statistics: StatisticsResponse;
    selectedCategories: string[];
}

export function Indicators({statistics, selectedCategories}: IndicatorsProps) {
    const {
        t,
        i18n: {language},
    } = useTranslation();

    const statisticsKeys = Object.keys(statistics).filter((key) => {
        // Find which category this key belongs to
        const category = Object.entries(categories_labels).find(([_, indicators]) => indicators.includes(key))?.[0];

        // Only include if the key's category is in selectedCategories
        return category && selectedCategories.includes(category);
    });

    return (
        <div>
            <div className="grid  grid-cols-1 sm:grid-cols-5 gap-6 mt-10">
                {statisticsKeys?.map((key, index) => {
                    // const value: number = statistics[key] !== undefined && statistics[key] !== null && typeof statistics[key] === 'number' ?  Number(statistics[key]) : 0
                    const value: number = statistics && statistics[key] !== undefined && statistics[key] !== null && typeof statistics[key] === "number" ? Number(statistics[key]) : 0;

                    return (
                        <div className={styles.cardIndicator} key={index}>
                            <div className={styles.cardTitle}>{t(`indicator.${key as string}`)}</div>
                            <p>{formatNumber(value, language)}</p>
                        </div>
                    );
                })}
            </div>
        </div>
    );
}

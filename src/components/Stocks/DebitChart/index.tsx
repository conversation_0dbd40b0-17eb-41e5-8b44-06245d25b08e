import {Assets, StatisticsHistoryResponse} from "@/types/api/StatisticsHistoryResponse";
import {TickerResponse} from "@/types/api/TickerResponse";
import {BarController, BarElement, CategoryScale, Chart as ChartJS, Legend, LineController, LineElement, LinearScale, PointElement, Tooltip} from "chart.js";
import {useEffect, useState} from "react";
import {Chart} from "react-chartjs-2";
import {useTranslation} from "react-i18next";
import styles from "./styles.module.css";

type Props = {
    statisticsHistory: StatisticsHistoryResponse[];
    assetsHistory: Assets[];
    ticker: TickerResponse;
};

ChartJS.register(LinearScale, CategoryScale, BarElement, PointElement, LineElement, Legend, Tooltip, LineController, BarController);

export function DebitChart({statisticsHistory, assetsHistory, ticker}: Props) {
    const [netDebit, setNetDebit] = useState<any[]>([]);
    const [ebitida, setEbitida] = useState<any[]>([]);
    const [totalDebt, setTotalDebt] = useState<any[]>([]);
    const [assets, setAssets] = useState<any[]>([]);
    const [labels, setlabels] = useState<string[]>([]);

    const {t} = useTranslation();

    useEffect(() => {
        if (statisticsHistory.length > 0) {
            const labels: any[] = [];
            const net_debt: any[] = [];
            const ebitida: any[] = [];
            const assets: any[] = [];
            const totalDebts: any[] = [];

            for (let i = 0; i < statisticsHistory.length; i++) {
                const item = statisticsHistory[i];
                labels.push(item.date || "");
                net_debt.push(item.net_debt || "");
                ebitida.push(item.ebitda || null);
                const asset = assetsHistory[i];
                totalDebts.push(asset.total_debt);
                assets.push(asset.total_assets);
            }

            setNetDebit(net_debt);
            setlabels(labels);
            setEbitida(ebitida);
            setAssets(assets);
            setTotalDebt(totalDebts);
        }
    }, [statisticsHistory, assetsHistory]);

    const options = {
        responsive: true,
        interaction: {
            mode: "index" as const,
            intersect: false,
        },
        stacked: false,
        scales: {
            y: {
                type: "linear" as const,
                display: true,
                position: "left" as const,
            },
        },
    };

    const data = {
        labels,
        responsive: true,
        datasets: [
            {
                type: "bar" as const,
                label: t("indicator.ebitda"),
                backgroundColor: "yellow",
                data: ebitida,
                borderColor: "yellow",
                borderWidth: 3,
                borderRadius: 10,
                yAxisID: "y",
            },
            {
                type: "bar" as const,
                label: t("indicator.net_debt"),
                backgroundColor: "red",
                data: netDebit,
                borderColor: "red",
                borderWidth: 3,
                borderRadius: 10,
                yAxisID: "y",
            },
            {
                type: "bar" as const,
                label: t("indicator.total_debt"),
                backgroundColor: "blue",
                data: totalDebt,
                borderColor: "blue",
                borderWidth: 3,
                borderRadius: 10,
                yAxisID: "y",
            },
            {
                type: "bar" as const,
                label: t("indicator.total_assets"),
                backgroundColor: "#13DCD2",
                data: assets,
                borderColor: "#13DCD2",
                borderWidth: 3,
                borderRadius: 10,
                yAxisID: "y",
            },
        ],
    };
    return (
        <div className="mt-10">
            <h2 className={styles.chartTitle}>{t("stock_charts.debt_indicators")}</h2>
            <div className={styles.container}>
                <div className={`flex sm:flex-row flex-col items-baseline justify-start`}>
                    <p className="text-text">
                        {" "}
                        <span className="text-dark font-bold uppercase">Y</span>: {t(`currency.${ticker?.currency_code}`)} &nbsp;
                    </p>
                    <p className="text-text">
                        {" "}
                        <span className="text-dark font-bold uppercase">X</span>: {t("chart.year")}
                    </p>
                </div>
                <Chart type={"bar"} data={data} options={options} className="bg-white pt-2" />
            </div>
        </div>
    );
}

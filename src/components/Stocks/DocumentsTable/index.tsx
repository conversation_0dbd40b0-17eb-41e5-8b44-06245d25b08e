"use client";

import React from "react";

import NoData from "@/components/NoData";
import {formatNumber} from "@/utils/functions";
import {ArrowDown, ArrowUp, Calendar, ChevronDown, ChevronRight, Filter, Minus, SortAsc, TrendingDown, TrendingUp} from "lucide-react";
import {useMemo, useState} from "react";

import {useMobile} from "@/context/use-mobile";
import {BalanceSheetResponse} from "@/types/api/BalanceSheetResponse";
import {CashFlowResponse} from "@/types/api/CashFlowResponse";
import {IncomeStatementResponse} from "@/types/api/IncomeStatementResponse";
import {useTranslation} from "react-i18next";
import styles from "./styles.module.css";

interface DocumentsTableProps {
    balance_sheet: BalanceSheetResponse[];
    income_statement: IncomeStatementResponse[];
    cash_flow: CashFlowResponse[];
}

interface DocumentGroup {
    title: string;
    icon: string;
    fields: string[];
    data: any[];
}

type SortConfig = {
    year: string;
    direction: "asc" | "desc";
};

const languages = {
    en: "en-US",
    fr: "fr-FR",
    pt: "pt-BR",
};

type YearSortDirection = "asc" | "desc" | null;

export function DocumentsTable({balance_sheet, income_statement, cash_flow}: DocumentsTableProps) {
    const isMobile = useMobile();
    const {
        t,
        i18n: {language: lang},
    } = useTranslation();

    // Get all dates from any document type - show last 5 dates
    const allDates = useMemo(() => {
        const dates = new Set<string>();
        balance_sheet.forEach((item) => {
            if (item.document_date) {
                dates.add(item.document_date);
            }
        });
        income_statement.forEach((item) => {
            if (item.document_date) {
                dates.add(item.document_date);
            }
        });
        cash_flow.forEach((item) => {
            if (item.document_date) {
                dates.add(item.document_date);
            }
        });
        return Array.from(dates)
            .sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
            .slice(0, 5);
    }, [balance_sheet, cash_flow, income_statement]);

    const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
        balance_sheet: true,
        income_statement: false,
        cash_flow: false,
    });

    const [expandedComparisons, setExpandedComparisons] = useState<Record<string, boolean>>({
        "income_statement-total_revenue-comparison": true,
        "income_statement-gross_profit-comparison": true,
        "income_statement-research_development-comparison": true,
        "income_statement-selling_general_administrative-comparison": true,
        "income_statement-operating_income-comparison": true,
        "income_statement-ebit-comparison": true,
        "income_statement-ebitda-comparison": true,
        "income_statement-net_income-comparison": true,
        "income_statement-eps_diluted_current-comparison": true,
    });

    const [selectedYears, setSelectedYears] = useState<string[]>(allDates);
    const [showYearFilter, setShowYearFilter] = useState(false);
    const [sortConfig, setSortConfig] = useState<SortConfig | null>(null);
    const [activeGroup, setActiveGroup] = useState<string | null>("balance_sheet");
    const [yearSortDirection, setYearSortDirection] = useState<YearSortDirection>("desc");

    const documentGroups: Record<string, DocumentGroup> = {
        balance_sheet: {
            title: "details.balance",
            icon: "📊",
            data: balance_sheet,
            fields: [
                "total_assets",
                "total_current_assets",
                "cash_and_short_term_investments",
                "cash_and_equivalents",
                "short_term_investments",
                "net_receivables",
                "inventory",
                "other_current_assets",
                "non_current_assets_total",
                "property_plant_and_equipment_net",
                "long_term_investments",
                "intangible_assets",
                "total_current_liabilities",
                "accounts_payable",
                "short_term_debt",
                "current_deferred_revenue",
                "other_current_liab",
                "non_current_liabilities_total",
                "long_term_debt",
                "deferred_long_term_liab",
                "non_current_liabilities_other",
                "total_liab",
                "total_stockholder_equity",
                "common_stock",
                "retained_earnings",
                "accumulated_other_comprehensive_income",
                "treasury_stock",
            ],
        },
        income_statement: {
            title: "details.incomeStatement",
            icon: "💰",
            data: income_statement,
            fields: [
                "total_revenue",
                "cost_of_revenue",
                "gross_profit",
                "total_operating_expenses",
                "research_development",
                "selling_general_administrative",
                "selling_and_marketing_expenses",
                "depreciation_and_amortization",
                "reconciled_depreciation",
                "other_operating_expenses",
                "operating_income",
                "ebit",
                "ebitda",
                "interest_income",
                "interest_expense",
                "net_interest_income",
                "non_operating_income_net_other",
                "total_other_income_expense_net",
                "income_before_tax",
                "tax_provision",
                "income_tax_expense",
                "net_income",
                "net_income_from_continuing_ops",
                "net_income_applicable_to_common_shares",
                "eps_diluted_current",
                "eps_diluted_last_date",
                "discontinued_operations",
                "minority_interest",
                "extraordinary_items",
                "non_recurring",
                "other_items",
                "effect_of_accounting_charges",
            ],
        },
        cash_flow: {
            title: "details.cashFlow",
            icon: "💸",
            data: cash_flow,
            fields: [
                "total_cash_from_operating_activities",
                "net_income",
                "depreciation",
                "stock_based_compensation",
                "other_non_cash_items",
                "change_in_working_capital",
                "change_to_inventory",
                "change_to_account_receivables",
                "change_receivables",
                "change_to_liabilities",
                "change_to_operating_activities",
                "cash_flows_other_operating",
                "total_cash_flows_from_investing_activities",
                "capital_expenditures",
                "investments",
                "other_cash_flows_from_investing_activities",
                "total_cash_from_financing_activities",
                "dividends_paid",
                "sale_purchase_of_stock",
                "issuance_of_capital_stock",
                "net_borrowings",
                "other_cash_flows_from_financing_activities",
                "change_in_cash",
                "begin_period_cash_flow",
                "end_period_cash_flow",
                "exchange_rate_changes",
                "cash_and_cash_equivalents_changes",
                "free_cash_flow",
            ],
        },
    };

    // Sort dates based on the current date sort direction
    const sortedDates = useMemo(() => {
        if (!yearSortDirection) return selectedYears;

        return [...selectedYears].sort((a, b) => {
            const dateA = new Date(a).getTime();
            const dateB = new Date(b).getTime();
            return yearSortDirection === "asc" ? dateA - dateB : dateB - dateA;
        });
    }, [selectedYears, yearSortDirection]);

    // Filter data based on selected dates
    const getFilteredData = (data: any[]) => {
        return data.filter((item) => {
            return selectedYears.includes(item.document_date || "");
        });
    };

    const getSortedFields = (fields: string[], groupKey: string, data: any[]) => {
        if (!sortConfig || activeGroup !== groupKey) return fields;

        return [...fields].sort((a, b) => {
            const dateData = data.find((item) => item.document_date === sortConfig.year);
            if (!dateData) return 0;

            const valueA = dateData[a] || 0;
            const valueB = dateData[b] || 0;

            return sortConfig.direction === "asc" ? valueA - valueB : valueB - valueA;
        });
    };

    const toggleSection = (sectionKey: string) => {
        setExpandedSections((prev) => ({
            ...prev,
            [sectionKey]: !prev[sectionKey],
        }));
        setActiveGroup(sectionKey);
    };

    const toggleComparison = (comparisonKey: string) => {
        setExpandedComparisons((prev) => ({
            ...prev,
            [comparisonKey]: !prev[comparisonKey],
        }));
    };

    const handleSort = (year: string) => {
        setSortConfig((current) => {
            if (current?.year === year) {
                return current.direction === "asc" ? {year, direction: "desc"} : null;
            }
            return {year, direction: "asc"};
        });
    };

    const toggleYearSelection = (year: string) => {
        setSelectedYears((prev) => (prev.includes(year) ? prev.filter((y) => y !== year) : [...prev, year]));
    };

    const selectAllYears = () => {
        setSelectedYears([...allDates]);
    };

    const deselectAllYears = () => {
        setSelectedYears([]);
    };

    const toggleYearSort = () => {
        setYearSortDirection((current) => {
            if (current === null || current === "desc") return "asc";
            if (current === "asc") return "desc";
            return "desc";
        });
    };

    const getFieldLabel = (field: string) => {
        const labels: Record<string, string> = {
            // Balance Sheet
            total_assets: "Total de Ativos",
            total_current_assets: "Total de Ativos Circulantes",
            cash_and_short_term_investments: "Caixa e Aplicações de Curto Prazo",
            cash_and_equivalents: "Caixa e Equivalentes",
            short_term_investments: "Aplicações de Curto Prazo",
            net_receivables: "Contas a Receber",
            inventory: "Estoque",
            other_current_assets: "Outros Ativos Circulantes",
            non_current_assets_total: "Total de Ativos Não Circulantes",
            property_plant_and_equipment_net: "Imobilizado",
            long_term_investments: "Aplicações de Longo Prazo",
            intangible_assets: "Ativos Intangíveis",
            total_current_liabilities: "Total de Passivos Circulantes",
            accounts_payable: "Contas a Pagar",
            short_term_debt: "Dívida de Curto Prazo",
            current_deferred_revenue: "Receita Diferida Circulante",
            other_current_liab: "Outros Passivos Circulantes",
            non_current_liabilities_total: "Total de Passivos Não Circulantes",
            long_term_debt: "Dívida de Longo Prazo",
            deferred_long_term_liab: "Passivo Diferido de Longo Prazo",
            non_current_liabilities_other: "Outros Passivos Não Circulantes",
            total_liab: "Total de Passivos",
            total_stockholder_equity: "Patrimônio Líquido",
            common_stock: "Ações Ordinárias",
            retained_earnings: "Lucros Retidos",
            accumulated_other_comprehensive_income: "Outros Resultados Abrangentes Acumulados",
            treasury_stock: "Ações em Tesouraria",

            // Income Statement
            total_revenue: "Receita Total",
            cost_of_revenue: "Custo da Receita",
            gross_profit: "Lucro Bruto",
            total_operating_expenses: "Total de Despesas Operacionais",
            research_development: "Pesquisa e Desenvolvimento",
            selling_general_administrative: "Vendas e Administrativo",
            selling_and_marketing_expenses: "Despesas de Vendas e Marketing",
            depreciation_and_amortization: "Depreciação e Amortização",
            reconciled_depreciation: "Depreciação Reconciliada",
            other_operating_expenses: "Outras Despesas Operacionais",
            operating_income: "Lucro Operacional",
            ebit: "EBIT",
            ebitda: "EBITDA",
            interest_income: "Receita de Juros",
            interest_expense: "Despesa de Juros",
            net_interest_income: "Receita Líquida de Juros",
            non_operating_income_net_other: "Outras Receitas Não Operacionais",
            total_other_income_expense_net: "Total de Outras Receitas/Despesas",
            income_before_tax: "Lucro Antes dos Impostos",
            tax_provision: "Provisão para Impostos",
            income_tax_expense: "Despesa de Imposto de Renda",
            net_income: "Lucro Líquido",
            net_income_from_continuing_ops: "Lucro Líquido de Operações Continuadas",
            net_income_applicable_to_common_shares: "Lucro Líquido Aplicável às Ações Ordinárias",
            eps_diluted_current: "LPA Diluído Atual",
            eps_diluted_last_date: "LPA Diluído Anterior",
            discontinued_operations: "Operações Descontinuadas",
            minority_interest: "Participação Minoritária",
            extraordinary_items: "Itens Extraordinários",
            non_recurring: "Itens Não Recorrentes",
            other_items: "Outros Itens",
            effect_of_accounting_charges: "Efeito de Ajustes Contábeis",

            // Cash Flow - update the existing labels and add new ones
            operating_cash_flow: "Fluxo de Caixa Operacional", // keep existing
            total_cash_from_operating_activities: "Total de Fluxo de Caixa das Atividades Operacionais",
            depreciation: "Depreciação",
            depreciation_amortization: "Depreciação e Amortização", // keep existing
            stock_based_compensation: "Remuneração Baseada em Ações",
            other_non_cash_items: "Outros Itens Não Monetários",
            change_in_working_capital: "Variação do Capital de Giro",
            change_to_inventory: "Variação de Estoque",
            change_to_account_receivables: "Variação de Contas a Receber",
            change_receivables: "Variação de Recebíveis",
            change_to_liabilities: "Variação de Passivos",
            change_to_operating_activities: "Variação de Atividades Operacionais",
            cash_flows_other_operating: "Outros Fluxos de Caixa Operacionais",
            total_cash_flows_from_investing_activities: "Total de Fluxo de Caixa das Atividades de Investimento",
            capital_expenditures: "Investimentos em Capex",
            investments: "Investimentos",
            other_cash_flows_from_investing_activities: "Outros Fluxos de Caixa de Investimento",
            total_cash_from_financing_activities: "Total de Fluxo de Caixa das Atividades de Financiamento",
            dividends_paid: "Dividendos Pagos",
            sale_purchase_of_stock: "Compra/Venda de Ações",
            issuance_of_capital_stock: "Emissão de Capital",
            net_borrowings: "Empréstimos Líquidos",
            other_cash_flows_from_financing_activities: "Outros Fluxos de Caixa de Financiamento",
            change_in_cash: "Variação de Caixa",
            begin_period_cash_flow: "Caixa Início do Período",
            end_period_cash_flow: "Caixa Final do Período",
            exchange_rate_changes: "Variação Cambial",
            cash_and_cash_equivalents_changes: "Variação de Caixa e Equivalentes",
            free_cash_flow: "Fluxo de Caixa Livre",
        };

        return labels[field] || field;
    };

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString(languages[lang], {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
        });
    };

    const calculateGrowthPercentage = (currentValue: number, previousValue: number) => {
        if (previousValue === 0 || previousValue === null || previousValue === undefined) {
            return null;
        }
        return ((currentValue - previousValue) / previousValue) * 100;
    };

    const getPreviousYearData = (currentDate: string, data: any[], field: string) => {
        const currentYear = new Date(currentDate).getFullYear();
        const previousYear = currentYear - 1;

        const previousYearData = data.find((item) => {
            if (!item.document_date) return false;
            const itemYear = new Date(item.document_date).getFullYear();
            return itemYear === previousYear;
        });

        return previousYearData ? previousYearData[field] : null;
    };

    const renderGrowthComparison = (field: string, currentDate: string, currentValue: number, data: any[], groupKey: string) => {
        // Add the new fields to the comparison list
        const fieldsWithComparison = [
            "total_revenue",
            "gross_profit",
            "operating_income",
            "ebit",
            "ebitda",
            "net_income",
            "eps_diluted_current",
            "research_development",
            "selling_general_administrative",
        ];

        if (!fieldsWithComparison.includes(field)) return null;

        const comparisonKey = `${groupKey}-${field}-${currentDate}`;
        const isExpanded = expandedComparisons[comparisonKey];
        const previousValue = getPreviousYearData(currentDate, data, field);
        const growthPercentage = previousValue !== null ? calculateGrowthPercentage(currentValue, previousValue) : null;

        if (growthPercentage === null) return null;

        const isPositive = growthPercentage > 0;
        const isNegative = growthPercentage < 0;
        const isNeutral = growthPercentage === 0;

        return (
            <div className="mt-2">
                <button onClick={() => toggleComparison(comparisonKey)} className="flex items-center gap-2 text-xs text-gray-600 hover:text-gray-800 transition-colors">
                    {isExpanded ? <ChevronDown className="w-5 h-5" /> : <ChevronRight className="w-3 h-3" />}
                    <span className="text-[1.2rem]">{t("history_table.line_comparison")}</span>
                </button>

                {isExpanded && (
                    <div className="mt-2 p-3 bg-gray-50 rounded-lg border-l-4 border-blue-400">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                                {isPositive && <TrendingUp className="w-4 h-4 text-green-500" />}
                                {isNegative && <TrendingDown className="w-4 h-4 text-red-500" />}
                                {isNeutral && <Minus className="w-4 h-4 text-gray-500" />}
                                <span className="text-[1.2rem] font-medium text-gray-700">{t("details.results")}:</span>
                            </div>
                            <span className={`text-xl font-bold ${isPositive ? "text-green-600" : isNegative ? "text-red-600" : "text-gray-600"}`}>
                                {growthPercentage > 0 ? "+" : ""}
                                {growthPercentage.toFixed(2)}%
                            </span>
                        </div>
                        <div className="mt-3 text-xs text-gray-600">
                            <div>
                                {t("history_table.previous_year")}: {formatNumber(previousValue, languages[lang])}
                            </div>
                            <div className="mt-2">
                                {t("history_table.current_year")}: {formatNumber(currentValue, languages[lang])}
                            </div>
                            <div className="mt-2">
                                {t("history_table.difference")}: {formatNumber(currentValue - previousValue, languages[lang])}
                            </div>
                        </div>
                    </div>
                )}
            </div>
        );
    };

    const renderMobileCards = (groupKey: string, group: DocumentGroup) => {
        const filteredData = getFilteredData(group.data);
        const availableFields = group.fields.filter((field) => filteredData.some((item) => item[field] !== undefined && item[field] !== null));

        if (availableFields.length === 0) return null;

        const sortedFields = getSortedFields(availableFields, groupKey, filteredData);

        return (
            <div key={groupKey} className="mb-8">
                <button onClick={() => toggleSection(groupKey)} className={`${styles.categoryButton} ${expandedSections[groupKey] ? styles.categoryButtonActive : ""}`}>
                    <span className="text-2xl">{group.icon}</span>
                    <h2 className="text-lg font-bold text-left flex-1 uppercase">{t(group.title)}</h2>
                    {expandedSections[groupKey] ? <ChevronDown className="w-5 h-5" /> : <ChevronRight className="w-5 h-5" />}
                </button>

                {expandedSections[groupKey] && (
                    <div className="space-y-4 animate-in slide-in-from-top-2 duration-300">
                        {sortedDates.map((year) => {
                            const yearData = filteredData.find((item) => {
                                return item.document_date === year;
                            });
                            if (!yearData) return null;

                            return (
                                <div key={year} className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
                                    <div className="bg-[rgb(var(--color-sf-primary))] px-4 py-3 text-[rgb(var(--color-sf-on-primary))]">
                                        <div className="flex items-center justify-between">
                                            <h3 className="font-bold text-lg">{formatDate(year)}</h3>
                                            <span className="text-sm opacity-90">{group.icon}</span>
                                        </div>
                                    </div>

                                    <div className="p-4">
                                        <div className="space-y-3">
                                            {sortedFields.map((field) => {
                                                const value = yearData[field] ?? null;

                                                return (
                                                    <div key={`${field}-${year}`}>
                                                        <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                                                            <div className="flex items-center gap-2 flex-1">
                                                                <span className="text-[1.2rem] font-medium text-gray-800 leading-tight">{t(`details.${field}`)}</span>
                                                            </div>
                                                            <span className="text-sm font-bold text-gray-900 ml-2 text-right">
                                                                {value === null || value === undefined ? "N/A" : formatNumber(value, languages[lang])}
                                                            </span>
                                                        </div>
                                                        {value !== null && value !== undefined && renderGrowthComparison(field, year, value, filteredData, groupKey)}
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                )}
            </div>
        );
    };

    const renderDesktopTable = (groupKey: string, group: DocumentGroup) => {
        const filteredData = getFilteredData(group.data);
        const availableFields = group.fields.filter((field) => filteredData.some((item) => item[field] !== undefined && item[field] !== null));

        if (availableFields.length === 0) return null;

        const sortedFields = getSortedFields(availableFields, groupKey, filteredData);

        return (
            <div key={groupKey} className="mb-8">
                <button onClick={() => toggleSection(groupKey)} className={`${styles.categoryButton} ${expandedSections[groupKey] ? styles.categoryButtonActive : ""}`}>
                    <span className="text-2xl">{group.icon}</span>
                    <h2 className="text-xl font-bold text-left flex-1 uppercase">{t(group.title)}</h2>
                    {expandedSections[groupKey] ? <ChevronDown className="w-5 h-5" /> : <ChevronRight className="w-5 h-5" />}
                </button>

                {expandedSections[groupKey] && (
                    <div className={`${styles.tableContainer} animate-in slide-in-from-top-2 duration-300`}>
                        <table className={styles.table}>
                            <thead>
                                <tr className={styles.tableHeader}>
                                    <th className={`${styles.headerCell} text-gray-800`}>{t("details.name")}</th>
                                    {sortedDates.map((year) => (
                                        <th key={year} className={`${styles.headerCell} cursor-pointer hover:bg-gray-50 text-gray-800`} onClick={() => handleSort(year)}>
                                            <div className="flex items-center gap-1">
                                                {formatDate(year)}
                                                {sortConfig?.year === year && (sortConfig.direction === "asc" ? <ArrowUp className="w-4 h-4" /> : <ArrowDown className="w-4 h-4" />)}
                                            </div>
                                        </th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody>
                                {sortedFields.map((field, index) => (
                                    <React.Fragment key={field}>
                                        <tr className={index % 2 === 0 ? styles.rowEven : styles.rowOdd}>
                                            <td className={`${styles.indicator} text-gray-800`}>
                                                <div className="flex items-center gap-2">{t(`details.${field}`)}</div>
                                            </td>
                                            {sortedDates.map((year) => {
                                                const item = filteredData.find((h) => {
                                                    return h.document_date === year;
                                                });
                                                const value = item?.[field] ?? null;

                                                return (
                                                    <td key={`${field}-${year}`} className={`${styles.valueCell} text-gray-700`}>
                                                        {value === null || value === undefined ? "N/A" : formatNumber(value, languages[lang])}
                                                    </td>
                                                );
                                            })}
                                        </tr>

                                        {/* Add comparison rows for specific fields */}
                                        {[
                                            "total_revenue",
                                            "gross_profit",
                                            "operating_income",
                                            "ebit",
                                            "ebitda",
                                            "net_income",
                                            "eps_diluted_current",
                                            "research_development",
                                            "selling_general_administrative",
                                        ].includes(field) && (
                                            <tr className={index % 2 === 0 ? styles.rowEven : styles.rowOdd}>
                                                <td className={`${styles.indicator} text-gray-800`}>
                                                    <button
                                                        onClick={() => toggleComparison(`${groupKey}-${field}-comparison`)}
                                                        className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-800 transition-colors">
                                                        {expandedComparisons[`${groupKey}-${field}-comparison`] ? <ChevronDown className="w-8 h-8" /> : <ChevronRight className="w-6 h-6" />}
                                                        <span className="text-[1.3rem]">{t("history_table.line_comparison")}</span>
                                                    </button>
                                                </td>
                                                {sortedDates.map((year) => {
                                                    const item = filteredData.find((h) => h.document_date === year);
                                                    const currentValue = item?.[field] ?? null;
                                                    const previousValue = getPreviousYearData(year, filteredData, field);
                                                    const growthPercentage = previousValue !== null && currentValue !== null ? calculateGrowthPercentage(currentValue, previousValue) : null;

                                                    if (!expandedComparisons[`${groupKey}-${field}-comparison`]) {
                                                        return <td key={`${field}-growth-${year}`} className={`${styles.valueCell} text-gray-700`}></td>;
                                                    }

                                                    const isPositive = growthPercentage !== null && growthPercentage > 0;
                                                    const isNegative = growthPercentage !== null && growthPercentage < 0;
                                                    const isNeutral = growthPercentage !== null && growthPercentage === 0;

                                                    return (
                                                        <td key={`${field}-growth-${year}`} className={`${styles.valueCell} text-gray-700`}>
                                                            {growthPercentage !== null ? (
                                                                <div className="flex items-center gap-1">
                                                                    {isPositive && <TrendingUp className="w-6 h-6 text-green-500" />}
                                                                    {isNegative && <TrendingDown className="w-6 h-6 text-red-500" />}
                                                                    {isNeutral && <Minus className="w-6 h-6 text-gray-500" />}
                                                                    <span className={`text-[1.4rem] font-medium ${isPositive ? "text-green-600" : isNegative ? "text-red-600" : "text-gray-600"}`}>
                                                                        {growthPercentage > 0 ? "+" : ""}
                                                                        {growthPercentage.toFixed(2)}%
                                                                    </span>
                                                                </div>
                                                            ) : (
                                                                <span className="text-gray-400 text-[1.4rem]">N/A</span>
                                                            )}
                                                        </td>
                                                    );
                                                })}
                                            </tr>
                                        )}
                                    </React.Fragment>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>
        );
    };

    return (
        <div className="space-y-6">
            {allDates.length > 0 ? (
                <div className="space-y-4">
                    <div className="flex flex-col gap-4 mb-6">
                        <div className="flex flex-wrap gap-2">
                            <div className="relative">
                                <button onClick={() => setShowYearFilter(!showYearFilter)} className={`${styles.filterButton} ${showYearFilter ? styles.filterButtonActive : ""}`}>
                                    <Filter className="w-5 h-5" />
                                    <span>{t("history_table.buttons.filter_years")}</span>
                                </button>

                                {showYearFilter && (
                                    <div className="absolute left-[6px] top-full mt-2 bg-white border border-gray-200 rounded-lg shadow-xl z-50 p-4 min-w-[280px]">
                                        <div className="flex items-center justify-between mb-4">
                                            <h4 className="text-sm font-semibold text-gray-800">{t("history_table.buttons.select_years")}</h4>
                                            <div className="flex gap-2">
                                                <button
                                                    onClick={selectAllYears}
                                                    className="text-xs bg-[rgba(var(--color-sf-primary),0.1)] text-[rgb(var(--color-sf-primary))] hover:bg-[rgba(var(--color-sf-primary),0.2)] px-2 py-1 rounded font-medium transition-colors">
                                                    {t("history_table.buttons.all")}
                                                </button>
                                                <button onClick={deselectAllYears} className="text-xs bg-gray-50 text-gray-600 hover:bg-gray-100 px-2 py-1 rounded font-medium transition-colors">
                                                    {t("history_table.buttons.clear")}
                                                </button>
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-3 gap-2 max-h-[200px] overflow-y-auto">
                                            {allDates.map((year) => (
                                                <label key={year} className={`${styles.yearOption} ${selectedYears.includes(year) ? styles.yearOptionSelected : ""}`}>
                                                    <input type="checkbox" checked={selectedYears.includes(year)} onChange={() => toggleYearSelection(year)} className="sr-only" />
                                                    <span className="text-sm font-bold select-none">{formatDate(year)}</span>
                                                    {selectedYears.includes(year) && <div className={styles.yearDot}></div>}
                                                </label>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>

                            <button onClick={toggleYearSort} className={`${styles.sortButton} ${yearSortDirection ? styles.sortButtonActive : ""}`}>
                                <Calendar className="w-5 h-5" />
                                <span className="hidden sm:inline">
                                    {yearSortDirection === "asc" ? t("history_table.buttons.ascending") : yearSortDirection === "desc" ? t("history_table.buttons.descending") : "Ordenar"}
                                </span>
                                {yearSortDirection && <span className="sm:hidden">{yearSortDirection === "asc" ? <ArrowUp className="w-4 h-4" /> : <ArrowDown className="w-4 h-4" />}</span>}
                            </button>

                            {!isMobile && (
                                <button
                                    onClick={() => {
                                        setSortConfig(null);
                                        setYearSortDirection("desc");
                                        setSelectedYears([...allDates]);
                                    }}
                                    className={styles.resetButton}>
                                    <span className="flex items-center gap-2">
                                        <SortAsc className="w-5 h-5" />
                                        <span className="hidden sm:inline">{t("history_table.buttons.reset")}</span>
                                    </span>
                                </button>
                            )}
                        </div>

                        <div className="flex flex-wrap gap-2">
                            <button onClick={() => setExpandedSections((prev) => Object.keys(prev).reduce((acc, key) => ({...acc, [key]: true}), {}))} className={styles.expandButton}>
                                {t("history_table.buttons.expand")}
                            </button>

                            <button onClick={() => setExpandedSections((prev) => Object.keys(prev).reduce((acc, key) => ({...acc, [key]: false}), {}))} className={styles.collapseButton}>
                                {t("history_table.buttons.collapse")}
                            </button>
                        </div>
                    </div>

                    {Object.entries(documentGroups).map(([groupKey, group]) => (isMobile ? renderMobileCards(groupKey, group) : renderDesktopTable(groupKey, group)))}
                </div>
            ) : (
                <NoData />
            )}
        </div>
    );
}

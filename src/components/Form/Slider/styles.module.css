.container {
    padding: 2rem;
    background-color: #f5f6fa;
    min-height: 100vh;
  }
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
  }
  
  .title {
    font-family: --var(--inter);
    font-weight: 400;
    font-size: 15px;
    line-height: 100%;
    letter-spacing: 0%;
    color: #718ebf;
  }
  
  .headerButtons {
    display: flex;
    gap: 1rem;
  }
  
  .filterButton {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1.5rem;
    border-radius: 0.5rem;
    border: 1px solid #8a4fff;
    background-color: white;
    color: #8a4fff;
    font-weight: 500;
  }
  
  .indicatorsButton {
    padding: 0.5rem 1.5rem;
    border-radius: 0.5rem;
    border: none;
    background-color: #1a0b3d;
    color: white;
    font-weight: 500;
  }
  
  .tabContainer {
    display: flex;
    margin-bottom: 2rem;
    border-radius: 2rem;
    overflow: hidden;
    width: fit-content;
    background-color: #f0f0f4;
  }
  
  .tab {
    padding: 0.75rem 2rem;
    background-color: transparent;
    border: none;
    cursor: pointer;
    font-weight: 500;
    border-radius: 2rem;
  }
  
  .tabActive {
    background-color: #28d0c8;
    color: white;
  }
  
  .searchFields {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .searchField {
    background-color: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
  }
  
  .searchFieldLabel {
    font-weight: 500;
    margin-bottom: 0.75rem;
    display: block;
  }
  
  .searchInput {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #e0e0e0;
    border-radius: 0.5rem;
    background-color: white;
    position: relative;
  }
  
  .searchInputWrapper {
    position: relative;
  }
  
  .searchIcon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
  }
  
  .filterGrid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .filterCard {
    background-color: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    transition: all 0.2s;
  }
  
  .filterCardDisabled {
    opacity: 0.5;
  }
  
  .filterCardDisabled .sliderThumb {
    cursor: not-allowed;
  }
  
  .filterCardDisabled .input {
    background-color: #f5f5f5;
    cursor: not-allowed;
    margin-top: 0.5rem;
  }
  
  .filterCardHeader {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    justify-content: space-between;
  }
  
  .filterIcon {
    width: 2rem;
    height: 2rem;
    background-color: #fff8e1;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffc107;
    margin-right: 0.75rem;
  }
  
  .filterTitle {
    font-weight: 500;
    color: #333;
    flex: 1;
  }
  
  .filterControls {
    display: flex;
    gap: 0.5rem;
  }
  
  .filterControl {
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }
  
  .tooltip {
    position: absolute;
    right: 0;
    top: 100%;
    margin-top: 0.5rem;
    background-color: #4a4a4a;
    color: white;
    padding: 1rem;
    border-radius: 0.25rem;
    width: 18rem;
    z-index: 10;
    font-size: 0.875rem;
    line-height: 1.5;
    font-weight: normal;
    text-align: left;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s, visibility 0.2s;
  }
  
  .tooltipVisible {
    opacity: 1;
    visibility: visible;
  }
  
  .tooltip::before {
    content: "";
    position: absolute;
    top: -0.5rem;
    right: 0.5rem;
    width: 1rem;
    height: 1rem;
    background-color: #4a4a4a;
    transform: rotate(45deg);
  }
  
  .sliderContainer {
    position: relative;
    margin-bottom: 1rem;
  }
  
  .sliderTrack {
    width: 100%;
    height: 8.5px;
    background-color: #d9f1ee;
    border-radius: 4.25px;
    position: relative;
    margin: 1rem 0;
  }
  
  .sliderFill {
    position: absolute;
    height: 100%;
    background-color: #28d0c8;
    border-radius: 4.25px;
  }
  
  .sliderThumb {
    width: 25px;
    height: 25px;
    background-color: #28d0c8;
    border: 4.19px solid #ffffff;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    cursor: pointer;
    box-shadow: 0px 4.19px 4.19px -2.1px rgba(24, 39, 75, 0.08), 0px 2.1px 4.19px -2.1px rgba(24, 39, 75, 0.12);
  }
  
  .sliderThumb:focus-visible {
    outline: 2px solid #2d1b69;
    outline-offset: 2px;
  }
  
  .inputGroup {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
  }
  
  .input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #e0e0e0;
    border-radius: 0.25rem;
    text-align: center;
  }
  
  .input:focus {
    border-color: #28d0c8;
    outline: none;
  }
  
  .input:disabled {
    background-color: #f5f5f5;
    color: #999;
  }
  
  .actionButtons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
  }
  
  .saveButton {
    padding: 0.75rem 2rem;
    background-color: #28d0c8;
    color: white;
    border: none;
    border-radius: 2rem;
    font-weight: 500;
    cursor: pointer;
    min-width: 10rem;
  }
  
  .searchButton {
    padding: 0.75rem 2rem;
    background-color: white;
    color: #28d0c8;
    border: 1px solid #28d0c8;
    border-radius: 2rem;
    font-weight: 500;
    cursor: pointer;
    min-width: 10rem;
  }
  
  @media (max-width: 1024px) {
    .filterGrid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  @media (max-width: 768px) {
    .searchFields {
      grid-template-columns: 1fr;
    }
  
    .filterGrid {
      grid-template-columns: 1fr;
    }
  
    .actionButtons {
      flex-direction: column;
    }
  }
  
  
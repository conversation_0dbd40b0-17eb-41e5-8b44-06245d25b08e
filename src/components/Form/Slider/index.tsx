import {Folder, HelpOutline, Visibility, VisibilityOff} from "@mui/icons-material";
import type React from "react";
import {useEffect, useRef, useState} from "react";

import {useTranslation} from "react-i18next";
import styles from "./styles.module.css";

interface FilterCardProps {
    title: string;
    indicator: string;
    min: number;
    max: number;
    step?: number;
    id: string;
    helpText?: string;
    values: [number, number];
    visible: boolean;
    onChange: (type: string, value: number | boolean, indicator: string) => void;
}

export function RangeSlider({title, min, max, indicator, step = 1, values, id, helpText, visible, onChange}: FilterCardProps) {
    const [isDragging, setIsDragging] = useState<"min" | "max" | null>(null);
    const [showTooltip, setShowTooltip] = useState(false);
    const [tooltipClickToggled, setTooltipClickToggled] = useState(false);

    const trackRef = useRef<HTMLDivElement>(null);
    const sliderContainerRef = useRef<HTMLDivElement>(null);
    const minThumbRef = useRef<HTMLDivElement>(null);
    const maxThumbRef = useRef<HTMLDivElement>(null);
    const [minValue, maxValue] = values;

    const {t} = useTranslation();

    const handleMinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (!visible) return;
        const value = Math.min(Number(e.target.value), maxValue - step);

        if (value > min) {
            onChange("min", value, indicator);
        } else {
            onChange("min", min, indicator);
        }
    };

    const handleMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (!visible) return;
        const value = Math.max(Number(e.target.value), minValue + step);
        if (value < max) {
            onChange("max", value, indicator);
        } else {
            onChange("max", max, indicator);
        }
    };

    const getPercentage = (value: number) => {
        return ((value - min) / (max - min)) * 100;
    };

    const toggleVisibility = () => {
        onChange("visible", !visible, indicator);
        // If we're disabling the slider and currently dragging, stop dragging
        if (visible && isDragging) {
            setIsDragging(null);
        }
    };

    const calculateValueFromPosition = (clientX: number) => {
        if (!trackRef.current) return 0;

        const rect = trackRef.current.getBoundingClientRect();
        const percentage = Math.max(0, Math.min(100, ((clientX - rect.left) / rect.width) * 100));
        const value = min + ((max - min) * percentage) / 100;
        return Math.round(value / step) * step;
    };

    // Handle both mouse and touch events with a unified approach
    const handleStart = (thumb: "min" | "max") => (e: React.MouseEvent | React.TouchEvent) => {
        if (!visible) return;

        // Prevent default to avoid scrolling on mobile
        e.preventDefault();

        // Set which thumb is being dragged
        setIsDragging(thumb);

        // For touch events, capture the initial position
        if ("touches" in e) {
            const touch = e.touches[0];
            const value = calculateValueFromPosition(touch.clientX);

            if (thumb === "min") {
                onChange("min", maxValue - step, indicator);
            } else {
                onChange("max", minValue + step, indicator);
            }
        }
    };

    const handleMove = (e: MouseEvent | TouchEvent) => {
        if (!isDragging || !trackRef.current || !visible) return;

        // Prevent default to stop scrolling
        e.preventDefault();

        let clientX: number;

        // Handle both mouse and touch events
        if ("touches" in e) {
            clientX = e.touches[0].clientX;
        } else {
            clientX = e.clientX;
        }

        const value = calculateValueFromPosition(clientX);

        if (isDragging === "min") {
            onChange("min", Math.min(value, maxValue - step), indicator);
        } else {
            onChange("max", Math.max(value, minValue + step), indicator);
        }
    };

    const handleEnd = () => {
        setIsDragging(null);
    };

    const handleTooltipClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setTooltipClickToggled(!tooltipClickToggled);
        setShowTooltip(!tooltipClickToggled);
    };

    // Set up event listeners
    useEffect(() => {
        const moveHandler = (e: MouseEvent | TouchEvent) => handleMove(e);
        const endHandler = () => handleEnd();

        if (isDragging) {
            // Add event listeners to window
            window.addEventListener("mousemove", moveHandler as any);
            window.addEventListener("mouseup", endHandler);
            window.addEventListener("touchmove", moveHandler as any, {passive: false});
            window.addEventListener("touchend", endHandler);
            window.addEventListener("touchcancel", endHandler);
        }

        return () => {
            // Clean up event listeners
            window.removeEventListener("mousemove", moveHandler as any);
            window.removeEventListener("mouseup", endHandler);
            window.removeEventListener("touchmove", moveHandler as any);
            window.removeEventListener("touchend", endHandler);
            window.removeEventListener("touchcancel", endHandler);
        };
    }, [isDragging, minValue, maxValue]);

    // Prevent scrolling on the slider container
    useEffect(() => {
        const sliderElement = sliderContainerRef.current;

        if (!sliderElement) return;

        const preventScroll = (e: TouchEvent) => {
            e.preventDefault();
        };

        // Add event listener directly to the slider container
        sliderElement.addEventListener("touchmove", preventScroll, {passive: false});

        return () => {
            sliderElement.removeEventListener("touchmove", preventScroll);
        };
    }, []);

    useEffect(() => {
        const handleClickOutside = () => {
            if (tooltipClickToggled) {
                setTooltipClickToggled(false);
                setShowTooltip(false);
            }
        };

        document.addEventListener("click", handleClickOutside);
        return () => {
            document.removeEventListener("click", handleClickOutside);
        };
    }, [tooltipClickToggled]);

    return (
        <div className={`${styles.filterCard} ${!visible ? styles.filterCardDisabled : ""} mt-2`}>
            <div className={styles.header}>
                <div className="flex items-center">
                    <div className={styles.filterIcon} aria-hidden="true">
                        <Folder sx={{fontSize: 18}} />
                    </div>
                    <h3 className={styles.title} id={`${id}-label`}>
                        {title}
                    </h3>
                </div>
                <div className={styles.filterControls}>
                    <button type="button" className={styles.filterControl} onClick={toggleVisibility} aria-label={visible ? `Hide ${title} filter` : `Show ${title} filter`}>
                        {visible ? <Visibility sx={{fontSize: 18}} /> : <VisibilityOff sx={{fontSize: 18}} />}
                    </button>
                    <div
                        className={styles.filterControl}
                        onMouseEnter={() => !tooltipClickToggled && setShowTooltip(true)}
                        onMouseLeave={() => !tooltipClickToggled && setShowTooltip(false)}
                        onClick={handleTooltipClick}
                        aria-label={`Information about ${title}`}
                        role="tooltip"
                        aria-describedby={`${id}-tooltip`}>
                        <HelpOutline sx={{fontSize: 16}} />
                        <div className={`${styles.tooltip} ${showTooltip ? styles.tooltipVisible : ""}`} id={`${id}-tooltip`}>
                            <strong>{title}</strong> - {t(`${helpText}`) || `${title} mede um aspecto importante do desempenho financeiro da empresa. É útil para comparar empresas do mesmo setor.`}
                        </div>
                    </div>
                </div>
            </div>

            <div ref={sliderContainerRef} className={styles.sliderContainer} role="group" aria-labelledby={`${id}-label`} aria-disabled={!visible}>
                <div className={styles.sliderTrack} ref={trackRef} onTouchStart={(e) => e.stopPropagation()}>
                    <div
                        className={styles.sliderFill}
                        style={{
                            left: `${getPercentage(minValue)}%`,
                            width: `${getPercentage(maxValue) - getPercentage(minValue)}%`,
                        }}
                    />
                    <div
                        ref={minThumbRef}
                        className={styles.sliderThumb}
                        style={{left: `${getPercentage(minValue)}%`}}
                        role="slider"
                        aria-valuemin={min}
                        aria-valuemax={max}
                        aria-valuenow={minValue}
                        tabIndex={visible ? 0 : -1}
                        aria-label={`Minimum ${title}`}
                        aria-disabled={!visible}
                        onMouseDown={handleStart("min")}
                        onTouchStart={handleStart("min")}
                    />

                    <div
                        ref={maxThumbRef}
                        className={styles.sliderThumb}
                        style={{left: `${getPercentage(maxValue)}%`}}
                        role="slider"
                        aria-valuemin={min}
                        aria-valuemax={max}
                        aria-valuenow={maxValue}
                        tabIndex={visible ? 0 : -1}
                        aria-label={`Maximum ${title}`}
                        aria-disabled={!visible}
                        onMouseDown={handleStart("max")}
                        onTouchStart={handleStart("max")}
                    />
                </div>
            </div>

            <div className={styles.inputGroup}>
                <input type="number" className={styles.input} value={minValue} onChange={handleMinChange} min={min} max={max} step={step} disabled={!visible} aria-label={`Minimum ${title} value`} />
                <input type="number" className={styles.input} value={maxValue} onChange={handleMaxChange} min={min} max={max} step={step} disabled={!visible} aria-label={`Maximum ${title} value`} />
            </div>
        </div>
    );
}

import {CssTextField} from "@/app/updateMe/components/registerStyle";
import {useSearchTicker} from "@/context/stocks/SearchTickersContext";
import {BodyModal, CloseButton, TextModalContainer, TitleModal} from "@/styles/components/SearchModal";
import {Cell} from "@/styles/components/Ui";
import {TickerResponse} from "@/types/api/TickerResponse";
import {CloseRounded} from "@mui/icons-material";
import {Modal} from "@mui/material";
import {useRouter} from "next/navigation";
import {useCallback, useEffect, useState} from "react";
import {useTranslation} from "react-i18next";

type Props = {
    // handleSearch: (params: string) => void,
    isOpen: boolean;
    handleOpen: () => void;
};

type Tickers = {
    attributes: {
        country_code: string;
        createdAt: string | null;
        exchange_code: string | null;
        is_enable: number;
        name: string | null;
        primary_ticker_eodhd: string | null;
        symbol_code: string | null;
    };
    id: number;
};

export default function SearchModal({isOpen, handleOpen}: Props) {
    const [value, setValue] = useState("");
    const [results, setResults] = useState<TickerResponse[]>([]);
    const {t} = useTranslation();
    const router = useRouter();

    const {tickers} = useSearchTicker();

    const handleChange = (text: string) => {
        setValue(text);
    };

    const handleRedirect = (symbol_code: string) => {
        handleOpen();
        router.push(`/stocks/${symbol_code}`);
    };

    const searchItems = useCallback(
        (value: string) => {
            if (tickers && tickers.length > 0 && value !== "") {
                const filteredItems = tickers.filter((ticker: TickerResponse) => {
                    if (ticker?.name && ticker?.name.toLowerCase().includes(value.toLowerCase())) {
                        return ticker;
                    }

                    if (ticker?.symbol_code && ticker?.symbol_code.toLowerCase().includes(value.toLowerCase())) {
                        return ticker;
                    }
                });

                setResults(filteredItems);
            }
        },
        [tickers],
    );

    useEffect(() => {
        searchItems(value);
    }, [searchItems, value]);

    return (
        <Modal open={isOpen} onClose={handleOpen}>
            <TextModalContainer>
                <TitleModal>
                    <CssTextField onChange={(e) => handleChange(e.target.value)} value={value} placeholder={t("search.placeholder")} sx={{".MuiInputBase-input": {fontSize: 18}, width: "100%"}} />
                </TitleModal>
                <CloseButton onClick={handleOpen}>
                    <CloseRounded />
                </CloseButton>
                <BodyModal>
                    <ul>
                        {results &&
                            results.length > 0 &&
                            results?.map((item, key) => (
                                <li key={key} className="flex flex-row h-[30px]">
                                    <Cell className="w-[30%]" onClick={() => handleRedirect(item?.primary_ticker_eodhd || "")}>
                                        {item?.symbol_code || ""}
                                    </Cell>
                                    <div className="w-[70%] sm:w-[100%]" style={{fontSize: 10}}>
                                        {item?.name || ""}
                                    </div>
                                </li>
                            ))}
                    </ul>
                </BodyModal>
            </TextModalContainer>
        </Modal>
    );
}

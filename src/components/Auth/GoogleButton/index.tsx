"use client";

import Loading from "@/components/Loading";
import {Google} from "@mui/icons-material";
import {signIn} from "next-auth/react";
import {useRouter, useSearchParams} from "next/navigation";
import {useState} from "react";
import {useTranslation} from "react-i18next";
import {toast} from "react-toastify";

export default function Signin() {
    const {t} = useTranslation();
    const [loading, setLoading] = useState(false);
    const router = useRouter();
    const errorMessage = (message) =>
        toast.error(message, {
            position: "top-right",
        });

    const searchParams = useSearchParams();

    const callback = searchParams?.get("callback");

    async function handleLogin() {
        try {
            setLoading(true);
            const res = await signIn("google");

            // if (res?.error) {
            //     errorMessage(res?.error);
            // } else {
            router.push(callback || "/dashboard/wallet");
            // }
        } catch (e) {
        } finally {
            setLoading(false);
        }
    }

    return (
        <>
            <div className="mb-[20px]">
                <h3 className="text-5xl font-bold">{t("authflow.socialoptions")}</h3>
            </div>
            {loading ? (
                <Loading />
            ) : (
                <button
                    className="
            bg-google border 
            border-none
            rounded-[30px] text-zinc-700
            w-[120px]
            h-[52px]
            flex items-center justify-around
            px-3
            "
                    onClick={handleLogin}>
                    <Google htmlColor="white" fontSize="large" /> <span className="text-white text-2xl"> GOOGLE</span>
                </button>
            )}
        </>
    );
}

import {cn} from "@/utils/functions";
import * as React from "react";
import styles from "./styles.module.css";

export interface LabelProps extends React.LabelHTMLAttributes<HTMLLabelElement> {
    htmlFor?: string;
}

const Label = React.forwardRef<HTMLLabelElement, LabelProps>(({className, htmlFor, ...props}, ref) => {
    return <label ref={ref} htmlFor={htmlFor} className={cn(styles.label, className)} {...props} />;
});
Label.displayName = "Label";

export {Label};

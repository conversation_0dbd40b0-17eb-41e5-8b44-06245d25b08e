"use client";
import {Label} from "@/components/UI/Label";
import {Switch} from "@/components/UI/switch";
import {cn} from "@/lib/utils";

interface ToggleSwitchProps {
    label: string;
    checked: boolean;
    onCheckedChange: (checked: boolean) => void;
    className?: string;
}

export const ToggleSwitch = ({label, checked, onCheckedChange, className}: ToggleSwitchProps) => {
    return (
        <div className={cn("flex items-center space-x-2", className)}>
            <Switch id={`toggle-${label.toLowerCase().replace(/\s+/g, "-")}`} checked={checked} onChange={(e) => onCheckedChange((e.target as HTMLInputElement).checked)} />
            <Label htmlFor={`toggle-${label.toLowerCase().replace(/\s+/g, "-")}`} className="text-xs sm:text-sm cursor-pointer">
                {label}
            </Label>
        </div>
    );
};

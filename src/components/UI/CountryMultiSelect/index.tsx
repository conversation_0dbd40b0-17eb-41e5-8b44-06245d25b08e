"use client";

import {Badge} from "@/components/UI/badge";
import {But<PERSON>} from "@/components/UI/button";
import {Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList} from "@/components/UI/command";
import {Popover, PopoverContent, PopoverTrigger} from "@/components/UI/popover";
import {cn} from "@/lib/utils";
import {LocationResponse} from "@/types/api/LocationResponse";
import {Check, ChevronsUpDown, X} from "lucide-react";
import {useState} from "react";
import {useTranslation} from "react-i18next";

type Props = {
    countries: LocationResponse[];
    selectedCountries: string[];
    setSelectedCountries: (country) => void;
};

export default function CountryMultiSelect({countries, selectedCountries, setSelectedCountries}: Props) {
    const {t} = useTranslation();
    const [open, setOpen] = useState(false);
    const [loading, setLoading] = useState(false);

    const handleSelectCountry = (countryCode: string) => {
        const index = selectedCountries.findIndex((item) => item === countryCode);
        const clone = [...selectedCountries];

        if (index === -1) {
            clone.push(countryCode);

            setSelectedCountries(clone);
        } else {
            const newClone = clone.filter((code) => code !== countryCode);
            setSelectedCountries(newClone);
        }
    };

    const handleRemoveCountry = (countryCode: string) => {
        const newClone = selectedCountries.filter((code) => code !== countryCode);
        setSelectedCountries(newClone);
    };

    const clearAll = () => {
        setSelectedCountries([]);
    };

    if (loading) {
        return (
            <div className="w-full max-w-md">
                <div className="flex items-center justify-center p-4 border rounded-md">
                    <div className="text-sm text-muted-foreground">{t("loading")}</div>
                </div>
            </div>
        );
    }
    return (
        <div className="w-full py-5 space-y-4">
            <div className="space-y-2 gap-2">
                <Popover open={open} onOpenChange={setOpen}>
                    <PopoverTrigger asChild>
                        <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={open}
                            className="w-[100%] font-[300] text-[13px] justify-between bg-[rgb(var(--color-sf-button-gray))] px-[20px] py-[22px] border-none rounded-[13px]">
                            {selectedCountries.length === 0 ? t("blog.all") : `${selectedCountries.length} ${t("location.selected_countries")}`}
                            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0">
                        <Command>
                            <CommandInput placeholder={t("location.selected_countries")} className="text-[15px] my-2" />
                            <CommandList>
                                <CommandEmpty>{t("noCountryFound")}</CommandEmpty>
                                <CommandGroup>
                                    {countries.map((country) => (
                                        <CommandItem className="text-[15px]" key={country.country_code} value={country.country_code} onSelect={() => handleSelectCountry(country.country_code)}>
                                            <Check className={cn("mr-2 h-4 w-4", selectedCountries?.includes(country.country_code) ? "opacity-100" : "opacity-0")} />
                                            {t(`countries.${country.country_code.toLowerCase()}`) || country.country_code}
                                        </CommandItem>
                                    ))}
                                </CommandGroup>
                            </CommandList>
                        </Command>
                    </PopoverContent>
                </Popover>
            </div>

            {selectedCountries.length > 0 && (
                <div className="space-y-2">
                    <div className="flex items-center justify-between">
                        <span className="font-medium text-[15px]">{t("location.selected_countries")}</span>
                        <Button variant="ghost" size="xl" onClick={clearAll} className="h-auto p-1 text-[12px] text-primaryShadow">
                            {t("history_table.buttons.clear")}
                        </Button>
                    </div>
                    <div className="flex flex-wrap gap-2">
                        {selectedCountries?.map((countryCode) => (
                            <Badge key={countryCode} variant="secondary" className="flex items-center gap-1 text-[15px] bg-none border-[2px] rounded-[10px] border-primaryShadow text-primaryShadow">
                                {t(`countries.${countryCode.toLowerCase()}`) || countryCode}
                                <X className="h-3 w-3 cursor-pointer hover:text-destructive" onClick={() => handleRemoveCountry(countryCode)} />
                            </Badge>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
}

.input {
    display: flex;
    height: 2.5rem;
    width: 100%;
    border-radius: 0.375rem;
    border: 1px solid rgb(var(--input));
    background-color: rgb(var(--background));
    padding: 2.2rem 0.75rem;
    font-size: 1.2rem;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow,
      transform;
    transition-duration: 150ms;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .input:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px rgb(var(--background)), 0 0 0 4px rgb(var(--ring));
    border-color: rgb(var(--ring));
  }
  
  .input:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
  
  .input::placeholder {
    color: rgb(var(--muted-foreground));
    font-size: 1.2rem;
  }
  
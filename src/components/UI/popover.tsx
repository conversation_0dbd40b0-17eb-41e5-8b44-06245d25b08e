"use client";

import {cn} from "@/lib/utils";
import React from "react";

interface PopoverProps {
    children: React.ReactNode;
    open?: boolean;
    onOpenChange?: (open: boolean) => void;
}

export const Popover = ({children, open, onOpenChange}: PopoverProps) => {
    const [isOpen, setIsOpen] = React.useState(open || false);

    React.useEffect(() => {
        if (open !== undefined) {
            setIsOpen(open);
        }
    }, [open]);

    const handleOpenChange = (newOpen: boolean) => {
        setIsOpen(newOpen);
        onOpenChange?.(newOpen);
    };

    return (
        <div className="relative">
            {React.Children.map(children, (child) => {
                if (React.isValidElement(child)) {
                    return React.cloneElement(child as React.ReactElement<any>, {
                        isOpen,
                        onOpenChange: handleOpenChange,
                    });
                }
                return child;
            })}
        </div>
    );
};

interface PopoverTriggerProps {
    children: React.ReactNode;
    asChild?: boolean;
    isOpen?: boolean;
    onOpenChange?: (open: boolean) => void;
}

export const PopoverTrigger = ({children, asChild, isOpen, onOpenChange}: PopoverTriggerProps) => {
    const handleClick = () => {
        onOpenChange?.(!isOpen);
    };

    if (asChild && React.isValidElement(children)) {
        return React.cloneElement(children as React.ReactElement, {
            onClick: handleClick,
            "aria-expanded": isOpen,
        });
    }

    return (
        <button onClick={handleClick} aria-expanded={isOpen}>
            {children}
        </button>
    );
};

interface PopoverContentProps extends React.HTMLAttributes<HTMLDivElement> {
    children: React.ReactNode;
    align?: "start" | "center" | "end";
    isOpen?: boolean;
}

export const PopoverContent = ({children, className, align = "center", isOpen, ...props}: PopoverContentProps) => {
    if (!isOpen) return null;

    return (
        <div
            className={cn(
                "absolute z-50 w-72 rounded-md border bg-popover p-2 shadow-md outline-none animate-in data-[side=bottom]:slide-in-from-top-2 data-[side=top]:slide-in-from-bottom-2",
                {
                    "left-0": align === "start",
                    "left-1/2 -translate-x-1/2": align === "center",
                    "right-0": align === "end",
                },
                className,
            )}
            {...props}>
            {children}
        </div>
    );
};

"use client";

import type React from "react";

import {cn} from "@/lib/utils";
import {X} from "lucide-react";
import {useEffect, useRef} from "react";

interface PopupProps {
    isOpen: boolean;
    onClose: () => void;
    content: React.ReactNode;
    position: {x: number; y: number};
    className?: string;
    containerRelative?: boolean;
}

export function Popup({isOpen, onClose, content, position, className, containerRelative = false}: PopupProps) {
    const popupRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (popupRef.current && !popupRef.current.contains(event.target as Node)) {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener("mousedown", handleClickOutside);
        }

        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [isOpen, onClose]);

    if (!isOpen) return null;

    // Adjust position to ensure popup stays within viewport
    const adjustedPosition = {
        x: Math.min(position.x, window.innerWidth - 250),
        y: Math.min(position.y, window.innerHeight - 200),
    };

    return (
        <div
            ref={popupRef}
            className={cn("z-50 bg-background rounded-md shadow-lg p-4 border border-border min-w-[220px] max-w-[300px]", className)}
            style={{
                position: containerRelative ? "absolute" : "fixed",
                left: `${position.x}px`,
                top: `${position.y}px`,
            }}>
            <button onClick={onClose} className="absolute top-2 right-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200" aria-label="Close popup">
                <X size={16} />
            </button>
            {content}
        </div>
    );
}

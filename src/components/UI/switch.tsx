import {cn} from "@/lib/utils";
import React from "react";

interface SwitchProps extends React.InputHTMLAttributes<HTMLInputElement> {
    label?: string;
}

export const Switch = React.forwardRef<HTMLInputElement, SwitchProps>(({className, label, ...props}, ref) => {
    return (
        <div className="flex items-center space-x-2">
            <label className="relative inline-flex items-center cursor-pointer justify-center">
                <input type="checkbox" ref={ref} className="sr-only peer" {...props} />
                <div
                    className={cn(
                        "w-14 h-8 bg-primaryShadow peer-focus:outline-none peer-focus:ring-8 peer-focus:ring-primaryShadow/20 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[3px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-6 after:w-6 after:transition-all dark:border-gray-600 peer-checked:bg-primaryShadow",
                        className,
                    )}></div>
                {label && <span className="ml-3 text-sm font-medium text-foreground">{label}</span>}
            </label>
        </div>
    );
});

Switch.displayName = "Switch";

"use client";

import type React from "react";

import {ChevronDown} from "lucide-react";
import {useEffect, useRef, useState} from "react";
import {useTranslation} from "react-i18next";
import styles from "./accordion.module.css";

export interface AccordionItem {
    id: string;
    title: string;
    content: string | React.ReactNode;
}

interface AccordionProps {
    items: AccordionItem[];
    defaultOpenId?: string;
    className?: string;
    variant?: "default" | "colored";
    headerStyle?: any;
    headerColor?: string;
    contentColor?: string;
}

export default function Accordion({items, defaultOpenId, className = "", variant = "default", headerColor = "#29D0C8", contentColor = "#E6ECF5", headerStyle: hStyle = {}}: AccordionProps) {
    const [openItemId, setOpenItemId] = useState<string | null>(defaultOpenId || null);
    const contentRefs = useRef<{[key: string]: HTMLDivElement | null}>({});

    const toggleItem = (id: string) => {
        setOpenItemId(openItemId === id ? null : id);
    };

    // Add animation effect for smooth height transition
    useEffect(() => {
        items.forEach((item) => {
            const contentEl = contentRefs.current[item.id];
            if (!contentEl) return;

            if (openItemId === item.id) {
                const height = contentEl.scrollHeight;
                contentEl.style.maxHeight = `${height}px`;
            } else {
                contentEl.style.maxHeight = "0px";
            }
        });
    }, [openItemId, items]);

    const {t} = useTranslation();

    return (
        <div className={`${styles.accordion} ${className}`}>
            {items.map((item) => {
                const isOpen = openItemId === item.id;

                // Dynamic styles based on variant and open state
                const itemStyle = isOpen && variant === "colored" ? {} : {};

                const headerStyle = isOpen && variant === "colored" ? {backgroundColor: headerColor, color: "white", ...hStyle} : {...hStyle};

                const iconContainerStyle = isOpen && variant === "colored" ? {backgroundColor: "white"} : {};

                const iconStyle = isOpen && variant === "colored" ? {color: headerColor} : {};

                const contentStyle = isOpen && variant === "colored" ? {backgroundColor: contentColor} : {};

                return (
                    <div key={item.id} className={`${styles.accordionItem} ${isOpen ? styles.accordionItemOpen : ""}`} data-state={isOpen ? "open" : "closed"} style={itemStyle}>
                        <button
                            className={styles.accordionTrigger}
                            onClick={() => toggleItem(item.id)}
                            aria-expanded={isOpen}
                            aria-controls={`content-${item.id}`}
                            id={`trigger-${item.id}`}
                            style={headerStyle}>
                            <span className={styles.accordionTitle}>{t(item.title)}</span>
                            <span className={`${styles.accordionIcon} ${isOpen ? styles.iconRotated : ""}`} style={iconContainerStyle}>
                                <ChevronDown className={styles.icon} style={iconStyle} />
                            </span>
                        </button>
                        <div
                            id={`content-${item.id}`}
                            className={styles.accordionContent}
                            role="region"
                            aria-labelledby={`trigger-${item.id}`}
                            ref={(el) => {
                                contentRefs.current[item.id] = el;
                            }}
                            style={isOpen && variant === "colored" ? contentStyle : {}}>
                            <div className={styles.accordionContentInner}>{t(`${item.content}`)}</div>
                        </div>
                    </div>
                );
            })}
        </div>
    );
}

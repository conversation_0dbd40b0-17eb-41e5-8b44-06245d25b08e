.accordion {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  
}

.accordionItem {
  background-color: white;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  border-top-left-radius: 2.5rem;
  border-bottom-right-radius: 2.5rem;
}

.accordionItemOpen {
  border-color: #e5e7eb;
}

.accordionTrigger {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem;
  background: white;
  border: none;
  text-align: left;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  cursor: pointer;
  transition: all 0.3s ease;
}

.accordionTitle {
  flex: 1;
}

.accordionIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: #f3f4f6;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.icon {
  color: #6b7280;
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
}

.accordionItemOpen .icon {
  transform: rotate(180deg);
}

.accordionContent {
  overflow: hidden;
  max-height: 0;
  transition: max-height 0.3s ease-in-out;
  background-color: white;
  
}

.accordionContentInner {
  padding: 2rem  1.25rem;
  color: #4b5563;
  line-height: 1.6;
}

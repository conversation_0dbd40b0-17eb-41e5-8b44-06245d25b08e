"use client";

import Link from "next/link";
import {usePathname} from "next/navigation";
import {Calendar, Database, Home, List, Settings, FileText} from "lucide-react";
import {cn} from "@/lib/client-utils";
import styles from "./dashboard-sidebar.module.css";

export function DashboardSidebar() {
    const pathname = usePathname();

    const routes = [
        {
            name: "Dashboard",
            href: "/",
            icon: Home,
        },
        {
            name: "Jobs",
            href: "/jobs",
            icon: List,
        },
        {
            name: "Logs",
            href: "/logs",
            icon: FileText,
        },
        {
            name: "Settings",
            href: "/settings",
            icon: Settings,
        },
        {
            name: "Schedules",
            href: "/schedules",
            icon: Calendar,
        },
        {
            name: "Database",
            href: "/database",
            icon: Database,
        },
    ];

    return (
        <div className={styles.sidebar}>
            <div className={styles.header}>
                <h1 className={styles.title}>Agenda Dashboard</h1>
            </div>
            <nav className={styles.nav}>
                {routes.map((route) => (
                    <Link key={route.href} href={route.href} className={cn(styles.navLink, pathname === route.href && styles.activeNavLink)}>
                        <route.icon className={styles.navIcon} />
                        {route.name}
                    </Link>
                ))}
            </nav>
        </div>
    );
}

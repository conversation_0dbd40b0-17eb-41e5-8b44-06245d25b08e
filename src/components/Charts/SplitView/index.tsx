import {useTranslation} from "react-i18next";
import styles from "./styles.module.css";

enum ChartView {
    Combined = "combined",
    Splitted = "splitted",
}

interface SplitViewProps {
    combinedViewAction: (params: any) => void;
    splittedViewAction: (params: any) => void;
    chartSelected: ChartView;
}

export default function SplitView({combinedViewAction, splittedViewAction, chartSelected}: SplitViewProps) {
    const {t} = useTranslation();

    return (
        <div className={`flex flex-row ${styles.buttonsContainer}`}>
            <button onClick={combinedViewAction} className={`${styles.splitviewButton} ${chartSelected === ChartView.Combined && styles.actived} `}>
                {t("chart.combined")}
            </button>
            <button onClick={splittedViewAction} className={`${styles.splitviewButton} ${chartSelected === ChartView.Splitted && styles.actived} `}>
                {t("chart.splitted")}
            </button>
        </div>
    );
}

"use client";

import type React from "react";

import {ChartConfig} from "@/components/Charts/chart-config";
import {ChartLegend} from "@/components/Charts/financial-dashboard-legend";

import {ChartContainer, ChartTooltip, ChartTooltipContent} from "@/components/UI/chart";
import {Popup} from "@/components/UI/Popup";

import styles from "@/components/Charts/chart.module.css";

import {ToggleSwitch} from "@/components/UI/ToogleSwitch";
import {StatisticsHistoryResponse} from "@/types/api/StatisticsHistoryResponse";
import {useEffect, useRef, useState} from "react";
import {useTranslation} from "react-i18next";
import {CartesianGrid, Line, LineChart, XAxis, YAxis} from "recharts";
import SplitView from "../SplitView";

interface ValuationChartProps {
    data: Array<StatisticsHistoryResponse>;
    colors: {
        price_sales_ratio: string;
        pe: string;
        price_to_book: string;
    };
}

enum ChartView {
    Combined = "combined",
    Splitted = "splitted",
}

export function ValuationChart({data, colors}: ValuationChartProps) {
    const chartRef = useRef<HTMLDivElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    // State for tracking visible series
    const [visibleSeries, setVisibleSeries] = useState<string[]>(["price_sales_ratio", "pe", "price_to_book"]);

    // State for grid lines visibility and appearance
    const [showGrid, setShowGrid] = useState(true);
    const [gridOpacity, setGridOpacity] = useState(0.6);
    const [gridDasharray, setGridDasharray] = useState("3 3");
    const [gridStrokeDasharray, setGridStrokeDasharray] = useState("3 3");

    // State for view mode (combined or split)
    const [viewMode, setViewMode] = useState<ChartView>(ChartView.Combined);

    // State for chart dimensions
    const [chartWidth, setChartWidth] = useState(0);
    const [chartHeight, setChartHeight] = useState(0);

    // Popup state
    const [popup, setPopup] = useState<{
        isOpen: boolean;
        content: React.ReactNode;
        position: {x: number; y: number};
    }>({
        isOpen: false,
        content: null,
        position: {x: 0, y: 0},
    });

    // Window width for responsive adjustments
    const [windowWidth, setWindowWidth] = useState(typeof window !== "undefined" ? window.innerWidth : 0);

    const {t} = useTranslation();

    // Update the useEffect for chart width calculation to fill the entire container
    useEffect(() => {
        const handleResize = () => {
            setWindowWidth(window.innerWidth);

            // Update chart width based on container size
            if (containerRef.current) {
                const containerWidth = containerRef.current.getBoundingClientRect().width;

                // Calculate appropriate margins based on screen size
                let leftAxisWidth = 45;
                let rightAxisWidth = 60; // Increased right margin space

                if (windowWidth < 480) {
                    leftAxisWidth = 40;
                    rightAxisWidth = 60; // Increased for mobile
                } else if (windowWidth < 768) {
                    leftAxisWidth = 45;
                    rightAxisWidth = 55;
                }

                // Calculate chart width with appropriate margins
                const calculatedWidth = Math.max(containerWidth - leftAxisWidth - rightAxisWidth, 0);
                setChartWidth(calculatedWidth);

                // Set chart height based on container and screen size
                setChartHeight(windowWidth < 480 ? 250 : windowWidth < 768 ? 300 : 350);
            }
        };

        if (typeof window !== "undefined") {
            window.addEventListener("resize", handleResize);
            // Initial calculation
            handleResize();
            return () => window.removeEventListener("resize", handleResize);
        }
    }, [windowWidth]);

    // Toggle visibility of a series
    const toggleSeries = (seriesId: string) => {
        if (visibleSeries.includes(seriesId)) {
            // Don't allow removing the last visible series
            if (visibleSeries.length > 1) {
                setVisibleSeries(visibleSeries.filter((id) => id !== seriesId));
            }
        } else {
            setVisibleSeries([...visibleSeries, seriesId]);
        }
    };

    // Calculate position relative to chart container
    const calculatePopupPosition = (x: number, y: number) => {
        if (!chartRef?.current) return {x, y};

        const chartRect = chartRef.current.getBoundingClientRect();

        // Calculate position relative to chart container
        return {
            x: Math.min(Math.max(x - chartRect.left, 10), chartRect.width - 230),
            y: Math.min(Math.max(y - chartRect.top, 10), chartRect.height - 150),
        };
    };

    // Handle chart click
    const handleChartClick = (data: any) => {
        if (!data || !data.activePayload || !data.activePayload[0]) return;

        const chartData = data.activePayload[0].payload;
        if (!chartRef?.current) return;

        // Calculate position relative to chart container
        const position = calculatePopupPosition(data.chartX, data.chartY);

        const content = (
            <div className="space-y-2">
                <div className="font-semibold text-base border-b pb-1">Year: {chartData.year}</div>
                <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                    <div className="text-sm font-medium">Price to Sales:</div>
                    <div className="text-sm">{chartData.price_sales_ratio}</div>
                    <div className="text-sm font-medium">PE Ratio:</div>
                    <div className="text-sm">{chartData.pe}</div>
                    <div className="text-sm font-medium">Price to Book:</div>
                    <div className="text-sm">{chartData.price_to_book}</div>
                </div>
            </div>
        );

        setPopup({
            isOpen: true,
            content,
            position,
        });
    };

    // Close popup
    const closePopup = () => {
        setPopup((prev) => ({...prev, isOpen: false}));
    };

    // Get responsive chart margins
    const getChartMargins = () => {
        if (windowWidth < 480) {
            return {top: 10, right: 10, bottom: 5, left: -5}; // Increased right margin for mobile
        } else if (windowWidth < 768) {
            return {top: 15, right: 15, bottom: 5, left: 0}; // Increased right margin for tablet
        } else {
            return {top: 15, right: 10, bottom: 5, left: 0}; // Increased right margin for desktop
        }
    };

    // Get split view chart margins (smaller than combined view)
    const getSplitChartMargins = () => {
        if (windowWidth < 480) {
            return {top: 5, right: 10, bottom: 5, left: 0};
        } else {
            return {top: 10, right: 10, bottom: 5, left: 0};
        }
    };

    // Render a single chart for the split view
    const renderSplitChart = (dataKey: string, label: string, color: string) => {
        if (!visibleSeries.includes(dataKey)) return null;

        return (
            <div className="space-y-1 w-full">
                <div className="text-xs font-medium text-center w-full">{label}</div>
                <div className="relative w-full">
                    <ChartContainer
                        config={{
                            [dataKey]: {
                                label,
                                color: `hsl(${color})`,
                            },
                        }}
                        className="h-[150px] sm:h-[170px] md:h-[180px]">
                        <LineChart data={data} margin={getSplitChartMargins()} width={chartWidth} height={150}>
                            {showGrid && (
                                <CartesianGrid
                                    strokeDasharray={gridDasharray}
                                    opacity={gridOpacity}
                                    vertical={true}
                                    horizontal={true}
                                    stroke="currentColor"
                                    className="text-gray-300 dark:text-gray-400"
                                />
                            )}
                            <XAxis
                                dataKey="date"
                                tickLine={true}
                                axisLine={true}
                                tick={{fontSize: windowWidth < 480 ? 9 : 10}}
                                height={20}
                                minTickGap={windowWidth < 480 ? 15 : 10}
                                interval={"preserveStartEnd"}
                            />
                            <YAxis
                                width={windowWidth < 480 ? 35 : 40}
                                tickCount={windowWidth < 480 ? 3 : 4}
                                axisLine={true}
                                tickLine={true}
                                tick={(props) => {
                                    const {x, y, payload} = props;

                                    const value = payload?.value ?? "";

                                    return (
                                        <text x={x} y={y} dy={4} textAnchor="end" fill="currentColor" fontSize={windowWidth < 480 ? 9 : 10} style={{userSelect: "none"}}>
                                            {value}
                                        </text>
                                    ) as React.ReactElement<SVGElement>;
                                }}
                            />
                            <ChartTooltip
                                content={<ChartTooltipContent />}
                                contentStyle={{
                                    backgroundColor: "var(--background)",
                                    borderColor: "var(--border)",
                                    color: "var(--foreground)",
                                }}
                            />
                            <Line type="monotone" dataKey={dataKey} stroke={`hsl(${color})`} strokeWidth={2} dot={{r: windowWidth < 480 ? 2 : 3}} isAnimationActive={false} />
                        </LineChart>
                    </ChartContainer>
                </div>
                <ChartLegend items={[{id: dataKey, color: `hsl(${color})`, label: label}]} activeItems={visibleSeries} onToggle={toggleSeries} className="mt-2" />
            </div>
        );
    };

    return (
        <div>
            <div className="overflow-hidden mt-10">
                <div className="pb-2">
                    <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-2">
                        <h2 className={`${styles.chartTitle}`}>{t("stock_charts.profitability_data")}</h2>
                    </div>
                    <div className="flex justify-between items-center">
                        <SplitView combinedViewAction={() => setViewMode(ChartView.Combined)} splittedViewAction={() => setViewMode(ChartView.Splitted)} chartSelected={viewMode} />
                        <div className="flex items-center  w-[150px] justify-end">
                            <ToggleSwitch label={t("chart.grid")} checked={showGrid} onCheckedChange={setShowGrid} className="pr-4" />
                            <ChartConfig
                                showGrid={showGrid}
                                setShowGrid={setShowGrid}
                                gridOpacity={gridOpacity}
                                setGridOpacity={setGridOpacity}
                                gridStrokeDasharray={gridDasharray}
                                setGridStrokeDasharray={setGridDasharray}
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div className="relative p-0 pb-2">
                <div className="mt-0 w-full">
                    <div ref={containerRef} className="relative w-full">
                        {viewMode === ChartView.Combined && (
                            <div ref={chartRef} className={styles.container}>
                                <ChartContainer
                                    config={{
                                        price_sales_ratio: {
                                            label: t("indicator.price_sales_ratio"),
                                            color: `hsl(${colors.price_sales_ratio})`, // Use dynamic color
                                        },
                                        pe: {
                                            label: t("indicator.pe"),
                                            color: `hsl(${colors.pe})`, // Use dynamic color
                                        },
                                        price_to_book: {
                                            label: t("indicator.price_to_book"),
                                            color: `hsl(${colors.price_to_book})`, // Use dynamic color
                                        },
                                    }}>
                                    <LineChart data={data} margin={getChartMargins()} onClick={handleChartClick} width={chartWidth} height={chartHeight}>
                                        {showGrid && (
                                            <CartesianGrid
                                                strokeDasharray={gridDasharray}
                                                opacity={gridOpacity}
                                                vertical={true}
                                                horizontal={true}
                                                stroke="currentColor"
                                                className="text-gray-300 dark:text-gray-400"
                                            />
                                        )}
                                        <XAxis
                                            dataKey="date"
                                            tickLine={true}
                                            axisLine={true}
                                            tick={{fontSize: windowWidth < 480 ? 10 : 12}}
                                            height={30}
                                            minTickGap={windowWidth < 480 ? 10 : 0}
                                            interval={"preserveStartEnd"}
                                        />
                                        <YAxis
                                            width={windowWidth < 480 ? 40 : 45}
                                            tickCount={windowWidth < 480 ? 4 : 5}
                                            axisLine={true}
                                            tickLine={true}
                                            tick={(props) => {
                                                const {x, y, payload} = props;
                                                if (!payload)
                                                    return (
                                                        <g>
                                                            <text x={x} y={y} dy={4} textAnchor="end" fill="currentColor" fontSize={windowWidth < 480 ? 10 : 12} style={{userSelect: "none"}}></text>
                                                        </g>
                                                    );

                                                const value = payload.value;

                                                return (
                                                    <g>
                                                        <text x={x} y={y} dy={4} textAnchor="end" fill="currentColor" fontSize={windowWidth < 480 ? 10 : 12} style={{userSelect: "none"}}>
                                                            {value}
                                                        </text>
                                                    </g>
                                                );
                                            }}
                                        />
                                        <ChartTooltip
                                            content={<ChartTooltipContent />}
                                            contentStyle={{
                                                backgroundColor: "var(--background)",
                                                borderColor: "var(--border)",
                                                color: "var(--foreground)",
                                            }}
                                        />
                                        {visibleSeries.includes("price_sales_ratio") && (
                                            <Line
                                                type="monotone"
                                                dataKey="price_sales_ratio"
                                                stroke="var(--color-price_sales_ratio)"
                                                strokeWidth={2}
                                                dot={{r: windowWidth < 480 ? 3 : 4}}
                                                isAnimationActive={false}
                                            />
                                        )}
                                        {visibleSeries.includes("pe") && (
                                            <Line
                                                type="monotone"
                                                dataKey="pe"
                                                stroke="var(--color-pe)"
                                                strokeWidth={windowWidth < 480 ? 2 : 3}
                                                dot={{r: windowWidth < 480 ? 3 : 4}}
                                                isAnimationActive={false}
                                            />
                                        )}
                                        {visibleSeries.includes("price_to_book") && (
                                            <Line
                                                type="monotone"
                                                dataKey="price_to_book"
                                                stroke="var(--color-price_to_book)"
                                                strokeWidth={2}
                                                dot={{r: windowWidth < 480 ? 3 : 4}}
                                                isAnimationActive={false}
                                            />
                                        )}
                                    </LineChart>
                                </ChartContainer>
                                {popup.isOpen && <Popup isOpen={popup.isOpen} onClose={closePopup} content={popup.content} position={popup.position} containerRelative={true} />}
                                <ChartLegend
                                    items={[
                                        {id: "price_sales_ratio", color: `hsl(${colors.price_sales_ratio})`, label: "Price to Sales Ratio"},
                                        {id: "pe", color: `hsl(${colors.pe})`, label: "PE Ratio"},
                                        {id: "price_to_book", color: `hsl(${colors.price_to_book})`, label: "Price to Book Ratio"},
                                    ]}
                                    activeItems={visibleSeries}
                                    onToggle={toggleSeries}
                                    className="mt-2"
                                />
                            </div>
                        )}
                    </div>
                </div>
                {viewMode === ChartView.Splitted && (
                    <div className="mt-0 w-full">
                        <div className="grid grid-cols-1 gap-4 px-4 w-full">
                            <div ref={chartRef} className={styles.container}>
                                {renderSplitChart("price_sales_ratio", "Price to Sales Ratio", colors.price_sales_ratio)}
                                {renderSplitChart("pe", "PE Ratio", colors.pe)}
                                {renderSplitChart("price_to_book", "Price to Book Ratio", colors.price_to_book)}
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}

"use client";

import type React from "react";

import {ChartConfig} from "@/components/Charts/chart-config";
import {ChartLegend} from "@/components/Charts/financial-dashboard-legend";

import {ChartContainer, ChartTooltip, ChartTooltipContent} from "@/components/UI/chart";
import {Popup} from "@/components/UI/Popup";

import styles from "@/components/Charts/chart.module.css";
import {ToggleSwitch} from "@/components/UI/ToogleSwitch";
import {StatisticsHistoryResponse} from "@/types/api/StatisticsHistoryResponse";
import {useEffect, useRef, useState} from "react";
import {useTranslation} from "react-i18next";
import {CartesianGrid, Line, LineChart, XAxis, YAxis} from "recharts";
import SplitView from "../SplitView";

interface ProfitabilityChartProps {
    data: Array<StatisticsHistoryResponse>;
    colors: {
        netMargin: string;
        operatingMargin: string;
        eps: string;
    };
}

enum ChartView {
    Combined = "combined",
    Splitted = "splitted",
}

interface SingleItemLegendProps {
    color: string;
    label: string;
    active: boolean;
    onClick: () => void;
}

function SingleItemLegend({color, label, active, onClick}: SingleItemLegendProps) {
    return (
        <button onClick={onClick} className="flex items-center text-sm space-x-2 hover:opacity-75 transition-opacity">
            <div className="w-3 h-3 rounded-full" style={{backgroundColor: color, opacity: active ? 1 : 0.3}} />
            <span>{label}</span>
        </button>
    );
}

export function ProfitabilityChart({data, colors}: ProfitabilityChartProps) {
    const chartRef = useRef<HTMLDivElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    const {t} = useTranslation();

    const [chartView, setChartView] = useState<ChartView>(ChartView.Combined);

    // State for tracking visible series
    const [visibleSeries, setVisibleSeries] = useState<string[]>(["net_margin", "operation_margin", "eps_current"]);

    // State for grid lines visibility and appearance
    const [showGrid, setShowGrid] = useState(true);
    const [gridOpacity, setGridOpacity] = useState(0.6);
    const [gridDasharray, setGridDasharray] = useState("3 3");

    // State for chart dimensions
    const [chartWidth, setChartWidth] = useState(0);
    const [chartHeight, setChartHeight] = useState(0);

    // Popup state
    const [popup, setPopup] = useState<{
        isOpen: boolean;
        content: React.ReactNode;
        position: {x: number; y: number};
    }>({
        isOpen: false,
        content: null,
        position: {x: 0, y: 0},
    });

    // Window width for responsive adjustments
    const [windowWidth, setWindowWidth] = useState(typeof window !== "undefined" ? window.innerWidth : 0);

    // Update the useEffect for chart width calculation to fill the entire container
    useEffect(() => {
        const handleResize = () => {
            setWindowWidth(window.innerWidth);

            // Update chart width based on container size
            if (containerRef.current) {
                const containerWidth = containerRef.current.getBoundingClientRect().width;

                // Calculate appropriate margins based on screen size
                let leftAxisWidth = 40;
                let rightAxisWidth = 70;

                if (windowWidth < 480) {
                    leftAxisWidth = 25;
                    rightAxisWidth = 35;
                } else if (windowWidth < 768) {
                    leftAxisWidth = 35;
                    rightAxisWidth = 55;
                }

                // Calculate chart width with appropriate margins
                // For mobile, ensure we don't exceed the container width
                if (windowWidth < 480) {
                    // For mobile, use almost the full container width
                    setChartWidth(Math.max(containerWidth - 5, 0));
                } else {
                    setChartWidth(Math.max(containerWidth - leftAxisWidth - rightAxisWidth, 0));
                }

                // Set chart height based on container and screen size
                setChartHeight(windowWidth < 480 ? 250 : windowWidth < 768 ? 300 : 350);
            }
        };

        if (typeof window !== "undefined") {
            window.addEventListener("resize", handleResize);
            // Initial calculation
            handleResize();
            return () => window.removeEventListener("resize", handleResize);
        }
    }, [windowWidth]);

    // Toggle visibility of a series
    const toggleSeries = (seriesId: string) => {
        if (visibleSeries.includes(seriesId)) {
            // Don't allow removing the last visible series
            if (visibleSeries.length > 1) {
                setVisibleSeries(visibleSeries.filter((id) => id !== seriesId));
            }
        } else {
            setVisibleSeries([...visibleSeries, seriesId]);
        }
    };

    // Calculate position relative to chart container
    const calculatePopupPosition = (x: number, y: number) => {
        if (!chartRef?.current) return {x, y};

        const chartRect = chartRef.current.getBoundingClientRect();

        // Calculate position relative to chart container
        return {
            x: Math.min(Math.max(x - chartRect.left, 10), chartRect.width - 230),
            y: Math.min(Math.max(y - chartRect.top, 10), chartRect.height - 150),
        };
    };

    // Handle chart click
    const handleChartClick = (data: any) => {
        if (!data || !data.activePayload || !data.activePayload[0]) return;

        const chartData = data.activePayload[0].payload;
        if (!chartRef?.current) return;

        // Calculate position relative to chart container
        const position = calculatePopupPosition(data.chartX, data.chartY);

        const content = (
            <div className="space-y-2">
                <div className="font-semibold text-base border-b pb-1">Year: {chartData.year}</div>
                <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                    <div className="text-sm font-medium">Net Margin:</div>
                    <div className="text-sm">{chartData.net_margin}%</div>
                    <div className="text-sm font-medium">Operating Margin:</div>
                    <div className="text-sm">{chartData.operation_margin}%</div>
                    <div className="text-sm font-medium">EPS:</div>
                    <div className="text-sm">${chartData.eps_current}</div>
                </div>
            </div>
        );

        setPopup({
            isOpen: true,
            content,
            position,
        });
    };

    // Close popup
    const closePopup = () => {
        setPopup((prev) => ({...prev, isOpen: false}));
    };

    // Format dollar values for Y-axis
    const formatDollarValue = (value: number) => {
        if (value === 0) return "$0";
        return `$${value.toFixed(2)}`;
    };

    // Format percentage values for Y-axis
    const formatPercentValue = (value: number) => {
        return `${value}%`;
    };

    // Get responsive chart margins
    const getChartMargins = () => {
        if (windowWidth < 480) {
            return {top: 0, right: 15, bottom: 0, left: 20}; // No margins for mobile
        } else if (windowWidth < 768) {
            return {top: 10, right: 15, bottom: 5, left: 0};
        } else {
            return {top: 15, right: 20, bottom: 5, left: 0};
        }
    };

    const getChartMarginsSplited = () => {
        if (windowWidth < 480) {
            return {top: 5, right: 5, bottom: 5, left: 15};
        } else if (windowWidth < 1024) {
            return {top: 5, right: 5, bottom: 10, left: 5};
        } else {
            return {top: 20, right: 20, bottom: 10, left: 8};
        }
    };

    return (
        <div className="overflow-hidden mt-10">
            <div className="pb-2">
                <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-2">
                    <h2 className={`${styles.chartTitle}`}>{t("stock_charts.profitability_data")}</h2>
                </div>
                <div className="flex justify-between items-center">
                    <SplitView combinedViewAction={() => setChartView(ChartView.Combined)} splittedViewAction={() => setChartView(ChartView.Splitted)} chartSelected={chartView} />
                    <div className="flex items-center  w-[150px] justify-end">
                        <ToggleSwitch label={t("chart.grid")} checked={showGrid} onCheckedChange={setShowGrid} className="pr-4" />
                        <ChartConfig
                            showGrid={showGrid}
                            setShowGrid={setShowGrid}
                            gridOpacity={gridOpacity}
                            setGridOpacity={setGridOpacity}
                            gridStrokeDasharray={gridDasharray}
                            setGridStrokeDasharray={setGridDasharray}
                        />
                    </div>
                </div>
            </div>
            <div className="relative p-0 pb-2 overflow-hidden">
                <div ref={containerRef} className="relative w-full overflow-hidden">
                    {/* tabs */}
                    <div ref={chartRef} className={styles.container}>
                        {chartView === ChartView.Combined ? (
                            <ChartContainer
                                config={{
                                    netMargin: {
                                        label: "Net Margin (%)",
                                        color: `hsl(${colors.netMargin})`, // Use dynamic color
                                    },
                                    operatingMargin: {
                                        label: "Operating Margin (%)",
                                        color: `hsl(${colors.operatingMargin})`, // Use dynamic color
                                    },
                                    eps: {
                                        label: "EPS ($)",
                                        color: `hsl(${colors.eps})`, // Use dynamic color
                                    },
                                }}>
                                <LineChart
                                    data={data}
                                    margin={getChartMargins()}
                                    onClick={handleChartClick}
                                    width={windowWidth < 480 ? (containerRef.current?.clientWidth ?? 300) - 5 : chartWidth}
                                    height={chartHeight}
                                    className="w-full">
                                    {showGrid && (
                                        <CartesianGrid
                                            strokeDasharray={gridDasharray}
                                            opacity={gridOpacity}
                                            vertical={true}
                                            horizontal={true}
                                            stroke="currentColor"
                                            className="text-gray-300 dark:text-gray-400"
                                        />
                                    )}
                                    <XAxis
                                        dataKey="date"
                                        tickLine={true}
                                        axisLine={true}
                                        tick={{fontSize: windowWidth < 480 ? 8 : 12}}
                                        height={windowWidth < 480 ? 20 : 30}
                                        minTickGap={windowWidth < 480 ? 20 : 10}
                                        interval={"preserveStartEnd"}
                                    />
                                    <YAxis
                                        yAxisId="left"
                                        orientation="left"
                                        width={windowWidth < 480 ? 25 : 40}
                                        tickCount={windowWidth < 480 ? 3 : 5}
                                        axisLine={true}
                                        tickLine={true}
                                        tick={(props) => {
                                            const {x, y, payload} = props;

                                            if (!payload) {
                                                return (
                                                    <text x={x} y={y} dy={4} textAnchor="end" fill="currentColor" fontSize={windowWidth < 480 ? 8 : 12} style={{userSelect: "none"}}>
                                                        {/* Empty text to ensure a valid ReactElement */}
                                                    </text>
                                                );
                                            }

                                            const value = payload.value;
                                            const formattedValue = formatPercentValue(value);

                                            return (
                                                <text x={x} y={y} dy={4} textAnchor="end" fill="currentColor" fontSize={windowWidth < 480 ? 8 : 12} style={{userSelect: "none"}}>
                                                    {formattedValue}
                                                </text>
                                            );
                                        }}
                                    />
                                    <YAxis
                                        yAxisId="right"
                                        orientation="right"
                                        width={windowWidth < 480 ? 35 : 70}
                                        tickCount={windowWidth < 480 ? 3 : 5}
                                        domain={[0, "auto"]}
                                        axisLine={true}
                                        tickLine={true}
                                        tick={(props) => {
                                            const {x, y, payload} = props;
                                            if (!payload) {
                                                return (
                                                    <text x={x} y={y} dy={4} textAnchor="end" fill="currentColor" fontSize={windowWidth < 480 ? 8 : 12} style={{userSelect: "none"}}>
                                                        {/* Empty text to ensure a valid ReactElement */}
                                                    </text>
                                                );
                                            }

                                            const value = payload.value;
                                            const formattedValue = formatDollarValue(value);

                                            // Smaller offset for mobile
                                            const offset = windowWidth < 480 ? 2 : 25;

                                            return (
                                                <g>
                                                    <text x={x + offset} y={y} dy={4} textAnchor="start" fill="currentColor" fontSize={windowWidth < 480 ? 8 : 12} style={{userSelect: "none"}}>
                                                        {formattedValue}
                                                    </text>
                                                </g>
                                            );
                                        }}
                                    />
                                    <ChartTooltip
                                        content={<ChartTooltipContent />}
                                        contentStyle={{
                                            backgroundColor: "var(--background)",
                                            borderColor: "var(--border)",
                                            color: "var(--foreground)",
                                        }}
                                    />
                                    {visibleSeries.includes("net_margin") && (
                                        <Line
                                            yAxisId="left"
                                            type="monotone"
                                            dataKey="net_margin"
                                            stroke="var(--color-netMargin)"
                                            strokeWidth={windowWidth < 480 ? 1 : 2}
                                            dot={{r: windowWidth < 480 ? 1.5 : 4}}
                                            isAnimationActive={false}
                                        />
                                    )}
                                    {visibleSeries.includes("operation_margin") && (
                                        <Line
                                            yAxisId="left"
                                            type="monotone"
                                            dataKey="operation_margin"
                                            stroke="var(--color-operatingMargin)"
                                            strokeWidth={windowWidth < 480 ? 1 : 2}
                                            dot={{r: windowWidth < 480 ? 1.5 : 4}}
                                            isAnimationActive={false}
                                        />
                                    )}
                                    {visibleSeries.includes("eps_current") && (
                                        <Line
                                            yAxisId="right"
                                            type="monotone"
                                            dataKey="eps_current"
                                            stroke="var(--color-eps)"
                                            strokeWidth={windowWidth < 480 ? 1 : 3}
                                            dot={{r: windowWidth < 480 ? 1.5 : 4}}
                                            isAnimationActive={false}
                                        />
                                    )}
                                </LineChart>
                            </ChartContainer>
                        ) : (
                            <div className="grid grid-cols-1 gap-4 px-4 w-full">
                                {/* Net Margin Chart */}
                                <div className="space-y-1 w-full">
                                    <ChartContainer
                                        config={{
                                            netMargin: {
                                                label: t("indicator.net_margin"),
                                                color: `hsl(${colors.netMargin})`,
                                            },
                                        }}
                                        className="h-[150px] sm:h-[170px] md:h-[180px] w-full">
                                        <LineChart
                                            data={data}
                                            margin={getChartMarginsSplited()}
                                            width={windowWidth < 480 ? (containerRef.current?.clientWidth ?? 300) - 5 : chartWidth}
                                            height={windowWidth < 480 ? 120 : 150}>
                                            {showGrid && (
                                                <CartesianGrid
                                                    strokeDasharray={gridDasharray}
                                                    opacity={gridOpacity}
                                                    vertical={true}
                                                    horizontal={true}
                                                    stroke="currentColor"
                                                    className="text-gray-300 dark:text-gray-400"
                                                />
                                            )}
                                            <XAxis
                                                dataKey="date"
                                                tickLine={true}
                                                axisLine={true}
                                                tick={{fontSize: windowWidth < 480 ? 8 : 10}}
                                                height={windowWidth < 480 ? 20 : 25}
                                                interval={"preserveStartEnd"}
                                            />
                                            <YAxis tickFormatter={(value) => `${value}%`} width={windowWidth < 480 ? 25 : 40} tick={{fontSize: windowWidth < 480 ? 8 : 10}} />
                                            <ChartTooltip
                                                content={<ChartTooltipContent />}
                                                contentStyle={{
                                                    backgroundColor: "var(--background)",
                                                    borderColor: "var(--border)",
                                                    color: "var(--foreground)",
                                                }}
                                            />
                                            <Line
                                                type="monotone"
                                                dataKey="net_margin"
                                                stroke="var(--color-netMargin)"
                                                strokeWidth={windowWidth < 480 ? 1 : 2}
                                                dot={{r: windowWidth < 480 ? 2 : 4}}
                                                isAnimationActive={false}
                                            />
                                        </LineChart>
                                    </ChartContainer>
                                    <ChartLegend
                                        items={[{id: "net_margin", color: `hsl(${colors.netMargin})`, label: t("indicator.net_margin")}]}
                                        activeItems={visibleSeries}
                                        onToggle={toggleSeries}
                                    />
                                </div>

                                {/* Operating Margin Chart */}
                                <div className="space-y-1">
                                    <ChartContainer
                                        config={{
                                            operatingMargin: {
                                                label: t("indicator.operation_margin"),
                                                color: `hsl(${colors.operatingMargin})`,
                                            },
                                        }}
                                        className="h-[150px] sm:h-[170px] md:h-[180px] w-full">
                                        <LineChart
                                            data={data}
                                            margin={getChartMarginsSplited()}
                                            width={windowWidth < 480 ? (containerRef.current?.clientWidth ?? 300) - 5 : chartWidth}
                                            height={windowWidth < 480 ? 120 : 150}>
                                            {showGrid && (
                                                <CartesianGrid
                                                    strokeDasharray={gridDasharray}
                                                    opacity={gridOpacity}
                                                    vertical={true}
                                                    horizontal={true}
                                                    stroke="currentColor"
                                                    className="text-gray-300 dark:text-gray-400"
                                                />
                                            )}
                                            <XAxis
                                                dataKey="date"
                                                tickLine={true}
                                                axisLine={true}
                                                tick={{fontSize: windowWidth < 480 ? 8 : 10}}
                                                height={windowWidth < 480 ? 20 : 25}
                                                interval={"preserveStartEnd"}
                                            />
                                            <YAxis tickFormatter={(value) => `${value}%`} width={windowWidth < 480 ? 25 : 40} tick={{fontSize: windowWidth < 480 ? 8 : 10}} />
                                            <ChartTooltip
                                                content={<ChartTooltipContent />}
                                                contentStyle={{
                                                    backgroundColor: "var(--background)",
                                                    borderColor: "var(--border)",
                                                    color: "var(--foreground)",
                                                }}
                                            />
                                            <Line
                                                type="monotone"
                                                dataKey="operation_margin"
                                                stroke="var(--color-operatingMargin)"
                                                strokeWidth={windowWidth < 480 ? 1 : 2}
                                                dot={{r: windowWidth < 480 ? 2 : 4}}
                                                isAnimationActive={false}
                                            />
                                        </LineChart>
                                    </ChartContainer>
                                    <ChartLegend
                                        items={[{id: "operation_margin", color: `hsl(${colors.operatingMargin})`, label: t("indicator.operation_margin")}]}
                                        activeItems={visibleSeries}
                                        onToggle={toggleSeries}
                                    />
                                </div>

                                {/* EPS Chart */}
                                <div className="space-y-1">
                                    <ChartContainer
                                        config={{
                                            eps: {
                                                label: t("indicator.eps_current"),
                                                color: `hsl(${colors.eps})`,
                                            },
                                        }}
                                        className="h-[150px] sm:h-[170px] md:h-[180px] w-full">
                                        <LineChart
                                            data={data}
                                            margin={getChartMarginsSplited()}
                                            width={windowWidth < 480 ? (containerRef.current?.clientWidth ?? 300) - 5 : chartWidth}
                                            height={windowWidth < 480 ? 120 : 150}>
                                            {showGrid && (
                                                <CartesianGrid
                                                    strokeDasharray={gridDasharray}
                                                    opacity={gridOpacity}
                                                    vertical={true}
                                                    horizontal={true}
                                                    stroke="currentColor"
                                                    className="text-gray-300 dark:text-gray-400"
                                                />
                                            )}
                                            <XAxis
                                                dataKey="date"
                                                tickLine={true}
                                                axisLine={true}
                                                tick={{fontSize: windowWidth < 480 ? 8 : 10}}
                                                height={windowWidth < 480 ? 20 : 25}
                                                interval={"preserveStartEnd"}
                                            />
                                            <YAxis tickFormatter={formatDollarValue} width={windowWidth < 480 ? 30 : 45} tick={{fontSize: windowWidth < 480 ? 8 : 10}} />
                                            <ChartTooltip
                                                content={<ChartTooltipContent />}
                                                contentStyle={{
                                                    backgroundColor: "var(--background)",
                                                    borderColor: "var(--border)",
                                                    color: "var(--foreground)",
                                                }}
                                            />
                                            <Line
                                                type="monotone"
                                                dataKey="eps_current"
                                                stroke="var(--color-eps)"
                                                strokeWidth={windowWidth < 480 ? 1 : 2}
                                                dot={{r: windowWidth < 480 ? 2 : 4}}
                                                isAnimationActive={false}
                                            />
                                        </LineChart>
                                    </ChartContainer>
                                    <ChartLegend items={[{id: "eps_current", color: `hsl(${colors.eps})`, label: t("indicator.eps_current")}]} activeItems={visibleSeries} onToggle={toggleSeries} />
                                </div>
                            </div>
                        )}
                        {popup.isOpen && <Popup isOpen={popup.isOpen} onClose={closePopup} content={popup.content} position={popup.position} containerRelative={true} />}
                        {chartView === ChartView.Combined && (
                            <ChartLegend
                                items={[
                                    {id: "net_margin", color: `hsl(${colors.netMargin})`, label: t("indicator.net_margin")},
                                    {id: "operation_margin", color: `hsl(${colors.operatingMargin})`, label: t("indicator.operation_margin")},
                                    {id: "eps_current", color: `hsl(${colors.eps})`, label: t("indicator.eps_current")},
                                ]}
                                activeItems={visibleSeries}
                                onToggle={toggleSeries}
                            />
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}

"use client";

import type React from "react";

import {ChartConfig} from "@/components/Charts/chart-config";
import styles from "@/components/Charts/chart.module.css";
import {ChartLegend} from "@/components/Charts/financial-dashboard-legend";
import {ChartContainer, ChartTooltip, ChartTooltipContent} from "@/components/UI/chart";
import {Popup} from "@/components/UI/Popup";
import {ToggleSwitch} from "@/components/UI/ToogleSwitch";
import {useEffect, useRef, useState} from "react";
import {useTranslation} from "react-i18next";
import {Bar, BarChart, CartesianGrid, ComposedChart, Line, LineChart, XAxis, YAxis} from "recharts";
import SplitView from "../SplitView";

interface DividendChartProps {
    data: Array<{
        year: string;
        dividendYield?: number;
        dividendsPaid?: number;
        payoutRatio?: number;
    }>;
    colors: {
        dividendYield: string;
        dividendsPaid: string;
        payoutRatio: string;
    };
}

enum ChartView {
    Combined = "combined",
    Splitted = "splitted",
}

export function DividendChart({data, colors}: DividendChartProps) {
    const chartRef = useRef<HTMLDivElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);
    const [chartView, setChartView] = useState<ChartView>(ChartView.Combined);

    // State for tracking visible series
    const [visibleSeries, setVisibleSeries] = useState<string[]>(["dividendYield", "dividendsPaid", "payoutRatio"]);

    // State for grid lines visibility and appearance
    const [showGrid, setShowGrid] = useState(true);
    const [gridOpacity, setGridOpacity] = useState(0.6);
    const [gridDasharray, setGridDasharray] = useState("3 3");
    const {t} = useTranslation();

    // State for chart dimensions
    const [chartWidth, setChartWidth] = useState(0);

    // Popup state
    const [popup, setPopup] = useState<{
        isOpen: boolean;
        content: React.ReactNode;
        position: {x: number; y: number};
    }>({
        isOpen: false,
        content: null,
        position: {x: 0, y: 0},
    });

    // Window width for responsive adjustments
    const [windowWidth, setWindowWidth] = useState(typeof window !== "undefined" ? window.innerWidth : 0);

    // Update the useEffect for chart width calculation to better handle medium screens
    useEffect(() => {
        const handleResize = () => {
            setWindowWidth(window.innerWidth);

            // Update chart width based on container size
            if (containerRef.current) {
                const containerWidth = containerRef.current.getBoundingClientRect().width;
                // Subtract padding and axis width to prevent overflow
                // Increase right axis width to prevent cutting off labels
                const leftAxisWidth = windowWidth < 480 ? 30 : windowWidth < 1024 ? 45 : 50;
                const rightAxisWidth = windowWidth < 480 ? 45 : windowWidth < 1024 ? 65 : 70;
                const calculatedWidth = containerWidth;
                setChartWidth(containerWidth);
            }
        };

        if (typeof window !== "undefined") {
            window.addEventListener("resize", handleResize);
            // Initial calculation
            handleResize();
            return () => window.removeEventListener("resize", handleResize);
        }
    }, [windowWidth]);

    // Toggle visibility of a series
    const toggleSeries = (seriesId: string) => {
        if (visibleSeries.includes(seriesId)) {
            // Don't allow removing the last visible series
            if (visibleSeries.length > 1) {
                setVisibleSeries(visibleSeries.filter((id) => id !== seriesId));
            }
        } else {
            setVisibleSeries([...visibleSeries, seriesId]);
        }
    };

    const getXAxisInterval = () => {
        if (windowWidth < 480) {
            return data.length > 3 ? Math.ceil(data.length / 2) : "preserveStartEnd";
        } else if (windowWidth < 768) {
            return data.length > 7 ? Math.ceil(data.length / 5) : 0;
        } else {
            return data.length > 10 ? Math.ceil(data.length / 8) : 0;
        }
    };

    const formatDollarValue = (value: number) => {
        if (value === 0) return "$0";
        return `$${value.toFixed(2)}`;
    };

    // Calculate position relative to chart container
    const calculatePopupPosition = (x: number, y: number) => {
        if (!chartRef?.current) return {x, y};

        const chartRect = chartRef.current.getBoundingClientRect();

        // Calculate position relative to chart container
        return {
            x: Math.min(Math.max(x - chartRect.left, 10), chartRect.width - 230),
            y: Math.min(Math.max(y - chartRect.top, 10), chartRect.height - 150),
        };
    };

    // Handle chart click
    const handleChartClick = (data: any) => {
        if (!data || !data.activePayload || !data.activePayload[0]) return;

        const chartData = data.activePayload[0].payload;
        if (!chartRef?.current) return;

        // Calculate position relative to chart container
        const position = calculatePopupPosition(data.chartX, data.chartY);

        const content = (
            <div className="space-y-2">
                <div className="font-semibold text-base border-b pb-1">Year: {chartData.year}</div>
                <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                    <div className="text-sm font-medium">{t("indicator.dividend_yield")}:</div>
                    <div className="text-sm">{chartData.dividendYield}%</div>
                    <div className="text-sm font-medium">{t("addasset.dividends_paid")}:</div>
                    <div className="text-sm">${chartData.dividendsPaid}</div>
                    <div className="text-sm font-medium">{t("indicator.payout_ratio")}</div>
                    <div className="text-sm">{(chartData.payoutRatio * 100).toFixed(1)}%</div>
                </div>
            </div>
        );

        setPopup({
            isOpen: true,
            content,
            position,
        });
    };

    // Close popup
    const closePopup = () => {
        setPopup((prev) => ({...prev, isOpen: false}));
    };

    // Update the getChartMargins function to better handle medium screens
    const getChartMargins = () => {
        if (windowWidth < 480) {
            return {top: 5, right: 5, bottom: 5, left: 22};
        } else if (windowWidth < 1024) {
            return {top: 5, right: 5, bottom: 10, left: 5};
        } else {
            return {top: 20, right: -10, bottom: 10, left: 5};
        }
    };

    const getChartMarginsSplited = () => {
        if (windowWidth < 480) {
            return {top: 5, right: 5, bottom: 5, left: 5};
        } else if (windowWidth < 1024) {
            return {top: 5, right: 5, bottom: 10, left: 5};
        } else {
            return {top: 20, right: 20, bottom: 10, left: 8};
        }
    };

    // Calculate bar size based on screen width and number of data points
    const getBarSize = () => {
        const dataLength = data.length;
        if (windowWidth < 480) {
            return Math.min(12, (chartWidth - 50) / dataLength);
        } else if (windowWidth < 768) {
            return Math.min(15, (chartWidth - 60) / dataLength);
        } else {
            return Math.min(20, (chartWidth - 80) / dataLength);
        }
    };

    return (
        <div className="mt-10 w-full">
            <div className="w-full pb-2">
                <div className="flex flex-row justify-between items-center">
                    <h2 className={`${styles.chartTitle}`}>{t("stock_charts.dividends")}</h2>
                </div>
                <div className="flex justify-between items-center">
                    <SplitView combinedViewAction={() => setChartView(ChartView.Combined)} splittedViewAction={() => setChartView(ChartView.Splitted)} chartSelected={chartView} />
                    <div className="flex items-center  w-[150px] justify-end">
                        <ToggleSwitch label={t("chart.grid")} checked={showGrid} onCheckedChange={setShowGrid} className="pr-4" />
                        <ChartConfig
                            showGrid={showGrid}
                            setShowGrid={setShowGrid}
                            gridOpacity={gridOpacity}
                            setGridOpacity={setGridOpacity}
                            gridStrokeDasharray={gridDasharray}
                            setGridStrokeDasharray={setGridDasharray}
                        />
                    </div>
                </div>
            </div>
            <div className="w-full">
                <div ref={containerRef} className="w-full">
                    {chartView === ChartView.Combined && (
                        <div ref={chartRef} className={styles.container}>
                            <ChartContainer
                                config={{
                                    dividendYield: {
                                        label: `${t("indicator.dividend_yield")}: `,
                                        color: `hsl(${colors.dividendYield})`, // Use dynamic color
                                    },
                                    dividendsPaid: {
                                        label: `${t("addasset.dividends_paid")}: `,
                                        color: `hsl(${colors.dividendsPaid})`, // Use dynamic color
                                    },
                                    payoutRatio: {
                                        label: `${t("indicator.payout_ratio")}: `,
                                        color: `hsl(${colors.payoutRatio})`, // Use dynamic color
                                    },
                                }}>
                                <ComposedChart data={data} margin={getChartMargins()} onClick={handleChartClick} barSize={getBarSize()} maxBarSize={20}>
                                    {showGrid && (
                                        <CartesianGrid
                                            strokeDasharray={gridDasharray}
                                            opacity={gridOpacity}
                                            vertical={true}
                                            horizontal={true}
                                            stroke="currentColor"
                                            className="text-gray-300 dark:text-gray-400"
                                        />
                                    )}
                                    <XAxis dataKey="year" tickLine={true} axisLine={true} tick={{fontSize: windowWidth < 480 ? 10 : 12}} height={30} interval={"preserveStartEnd"} />
                                    <YAxis
                                        yAxisId="left"
                                        orientation="left"
                                        width={windowWidth < 480 ? 30 : windowWidth < 768 ? 40 : 50}
                                        tick={{fontSize: windowWidth < 480 ? 10 : 12}}
                                        tickLine={true}
                                        axisLine={true}
                                        tickFormatter={(value) => `${value}%`}
                                        tickCount={windowWidth < 480 ? 4 : 5}
                                        dx={windowWidth < 480 ? -5 : 0}
                                    />
                                    {/* Update the YAxis width for the right axis */}
                                    <YAxis
                                        yAxisId="right"
                                        orientation="right"
                                        width={windowWidth < 480 ? 45 : windowWidth < 1024 ? 65 : 70}
                                        tick={{fontSize: windowWidth < 480 ? 10 : 12}}
                                        tickLine={true}
                                        axisLine={true}
                                        tickFormatter={(value) => `$${value}`}
                                        tickCount={windowWidth < 480 ? 4 : 5}
                                        dx={windowWidth < 480 ? 5 : windowWidth < 1024 ? 10 : 0}
                                    />
                                    <ChartTooltip
                                        content={<ChartTooltipContent />}
                                        contentStyle={{
                                            backgroundColor: "var(--background)",
                                            borderColor: "var(--border)",
                                            color: "var(--foreground)",
                                        }}
                                    />
                                    {visibleSeries.includes("dividendsPaid") && (
                                        <Bar yAxisId="right" dataKey="dividendsPaid" fill="var(--color-dividendsPaid)" radius={[4, 4, 0, 0]} isAnimationActive={false} />
                                    )}
                                    {visibleSeries.includes("dividendYield") && (
                                        <Line
                                            yAxisId="left"
                                            type="monotone"
                                            dataKey="dividendYield"
                                            stroke="var(--color-dividendYield)"
                                            strokeWidth={windowWidth < 480 ? 2 : 3}
                                            dot={{r: windowWidth < 480 ? 3 : 5}}
                                            isAnimationActive={false}
                                        />
                                    )}
                                    {visibleSeries.includes("payoutRatio") && (
                                        <Line
                                            yAxisId="left"
                                            type="monotone"
                                            dataKey="payoutRatio"
                                            stroke="var(--color-payoutRatio)"
                                            strokeWidth={windowWidth < 480 ? 2 : 3}
                                            dot={{r: windowWidth < 480 ? 3 : 5}}
                                            isAnimationActive={false}
                                        />
                                    )}
                                </ComposedChart>
                            </ChartContainer>
                            <ChartLegend
                                items={[
                                    {id: "dividendsPaid", color: `hsl(${colors.dividendsPaid})`, label: t("indicator.dividends_paid")},
                                    {id: "dividendYield", color: `hsl(${colors.dividendYield})`, label: `${t("indicator.dividend_yield")}`},
                                    {id: "payoutRatio", color: `hsl(${colors.payoutRatio})`, label: `${t("indicator.payout_ratio")}`},
                                ]}
                                activeItems={visibleSeries}
                                onToggle={toggleSeries}
                            />
                        </div>
                    )}

                    {chartView === ChartView.Splitted && (
                        <div ref={chartRef} className={styles.container}>
                            {/* Dividend Yield Chart */}
                            <ChartContainer
                                config={{
                                    dividendYield: {
                                        label: t("indicator.dividend_yield"),
                                        color: `hsl(${colors.dividendYield})`, // Use dynamic color
                                    },
                                }}
                                className="h-[150px] sm:h-[170px] md:h-[180px] w-full">
                                <LineChart
                                    data={data}
                                    margin={getChartMarginsSplited()}
                                    width={windowWidth < 480 ? (containerRef.current?.clientWidth ?? 300) - 5 : chartWidth}
                                    height={windowWidth < 480 ? 120 : 180}>
                                    {showGrid && (
                                        <CartesianGrid
                                            strokeDasharray={gridDasharray}
                                            opacity={gridOpacity}
                                            vertical={true}
                                            horizontal={true}
                                            stroke="currentColor"
                                            className="text-gray-300 dark:text-gray-400"
                                        />
                                    )}
                                    <XAxis
                                        dataKey="year"
                                        tickLine={true}
                                        axisLine={true}
                                        tick={{fontSize: windowWidth < 480 ? 8 : 10}}
                                        height={windowWidth < 480 ? 20 : 25}
                                        interval={"preserveStartEnd"}
                                    />
                                    <YAxis domain={[0, 5]} tickFormatter={(value) => `${value}%`} width={windowWidth < 480 ? 25 : 40} tick={{fontSize: windowWidth < 480 ? 8 : 10}} />
                                    <ChartTooltip
                                        content={<ChartTooltipContent />}
                                        contentStyle={{
                                            backgroundColor: "var(--background)",
                                            borderColor: "var(--border)",
                                            color: "var(--foreground)",
                                        }}
                                    />
                                    <Line
                                        type="monotone"
                                        dataKey="dividendYield"
                                        stroke="var(--color-dividendYield)"
                                        strokeWidth={windowWidth < 480 ? 1 : 2}
                                        dot={{r: windowWidth < 480 ? 2 : 4}}
                                        isAnimationActive={false}
                                    />
                                </LineChart>
                            </ChartContainer>
                            <ChartLegend
                                items={[{id: "dividendYield", color: `hsl(${colors.dividendYield})`, label: `${t("indicator.dividend_yield")}`}]}
                                activeItems={visibleSeries}
                                onToggle={toggleSeries}
                            />

                            {/* Payout Ratio Chart */}
                            <ChartContainer
                                config={{
                                    payoutRatio: {
                                        label: t("indicator.payout_ratio"),
                                        color: `hsl(${colors.payoutRatio})`, // Use dynamic color
                                    },
                                }}
                                className="h-[150px] sm:h-[170px] md:h-[180px] w-full">
                                <LineChart
                                    data={data}
                                    margin={getChartMarginsSplited()}
                                    width={windowWidth < 480 ? (containerRef.current?.clientWidth ?? 300) - 5 : chartWidth}
                                    height={windowWidth < 480 ? 120 : 150}>
                                    {showGrid && (
                                        <CartesianGrid
                                            strokeDasharray={gridDasharray}
                                            opacity={gridOpacity}
                                            vertical={true}
                                            horizontal={true}
                                            stroke="currentColor"
                                            className="text-gray-300 dark:text-gray-400"
                                        />
                                    )}
                                    <XAxis
                                        dataKey="year"
                                        tickLine={true}
                                        axisLine={true}
                                        tick={{fontSize: windowWidth < 480 ? 8 : 10}}
                                        height={windowWidth < 480 ? 20 : 25}
                                        interval={"preserveStartEnd"}
                                    />
                                    <YAxis domain={[0, 120]} tickFormatter={(value) => `${value}%`} width={windowWidth < 480 ? 25 : 40} tick={{fontSize: windowWidth < 480 ? 8 : 10}} />
                                    <ChartTooltip
                                        content={<ChartTooltipContent />}
                                        contentStyle={{
                                            backgroundColor: "var(--background)",
                                            borderColor: "var(--border)",
                                            color: "var(--foreground)",
                                        }}
                                    />
                                    <Line
                                        type="monotone"
                                        dataKey="payoutRatio"
                                        stroke="var(--color-payoutRatio)"
                                        strokeWidth={windowWidth < 480 ? 1 : 2}
                                        dot={{r: windowWidth < 480 ? 2 : 4}}
                                        isAnimationActive={false}
                                    />
                                </LineChart>
                            </ChartContainer>
                            <ChartLegend
                                items={[{id: "payoutRatio", color: `hsl(${colors.payoutRatio})`, label: `${t("indicator.payout_ratio")}`}]}
                                activeItems={visibleSeries}
                                onToggle={toggleSeries}
                            />

                            {/* Dividends Paid Chart */}
                            <ChartContainer
                                config={{
                                    dividendsPaid: {
                                        label: t("addasset.dividens_paid"),
                                        color: `hsl(${colors.dividendsPaid})`, // Use dynamic color
                                    },
                                }}
                                className="h-[150px] sm:h-[170px] md:h-[180px] w-full">
                                <BarChart
                                    data={data}
                                    margin={getChartMarginsSplited()}
                                    barSize={getBarSize()}
                                    maxBarSize={20}
                                    width={windowWidth < 480 ? (containerRef.current?.clientWidth ?? 300) - 5 : chartWidth}
                                    height={windowWidth < 480 ? 120 : 150}>
                                    {showGrid && (
                                        <CartesianGrid
                                            strokeDasharray={gridDasharray}
                                            opacity={gridOpacity}
                                            vertical={true}
                                            horizontal={true}
                                            stroke="currentColor"
                                            className="text-gray-300 dark:text-gray-400"
                                        />
                                    )}
                                    <XAxis
                                        dataKey="year"
                                        tickLine={true}
                                        axisLine={true}
                                        tick={{fontSize: windowWidth < 480 ? 8 : 10}}
                                        height={windowWidth < 480 ? 20 : 25}
                                        interval={"preserveStartEnd"}
                                    />
                                    <YAxis tickFormatter={formatDollarValue} width={windowWidth < 480 ? 30 : 45} tick={{fontSize: windowWidth < 480 ? 8 : 10}} />
                                    <ChartTooltip
                                        content={<ChartTooltipContent />}
                                        contentStyle={{
                                            backgroundColor: "var(--background)",
                                            borderColor: "var(--border)",
                                            color: "var(--foreground)",
                                        }}
                                    />
                                    <Bar dataKey="dividendsPaid" fill="var(--color-dividendsPaid)" radius={[4, 4, 0, 0]} isAnimationActive={false} />
                                </BarChart>
                            </ChartContainer>
                            <ChartLegend
                                items={[{id: "dividendsPaid", color: `hsl(${colors.dividendsPaid})`, label: t("details.dividends_paid")}]}
                                activeItems={visibleSeries}
                                onToggle={toggleSeries}
                            />
                        </div>
                    )}
                </div>
                {popup.isOpen && <Popup isOpen={popup.isOpen} onClose={closePopup} content={popup.content} position={popup.position} containerRelative={true} />}
            </div>
        </div>
    );
}

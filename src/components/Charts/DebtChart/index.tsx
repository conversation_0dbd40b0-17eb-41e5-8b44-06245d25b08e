"use client";

import type React from "react";

import {ChartConfig} from "@/components/Charts/chart-config";
import styles from "@/components/Charts/chart.module.css";
import {ChartLegend} from "@/components/Charts/financial-dashboard-legend";
import {ChartContainer, ChartTooltip, ChartTooltipContent} from "@/components/UI/chart";
import {Popup} from "@/components/UI/Popup";
import {ToggleSwitch} from "@/components/UI/ToogleSwitch";
import {AssetsHistory, StatisticsHistoryResponse} from "@/types/api/StatisticsHistoryResponse";
import {useEffect, useRef, useState} from "react";
import {useTranslation} from "react-i18next";
import {Bar, BarChart, CartesianGrid, XAxis, YAxis} from "recharts";
import SplitView from "../SplitView";

interface DebtChartProps {
    data: Array<StatisticsHistoryResponse>;
    assets: AssetsHistory;
    colors: {
        ebitda: string;
        net_debt: string;
        total_debt: string;
        assets: string;
    };
}

enum ChartView {
    Combined = "combined",
    Splitted = "splitted",
}

export function DebtChart({data, colors, assets}: DebtChartProps) {
    const chartRef = useRef<HTMLDivElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    // State for tracking visible series
    const [visibleSeries, setVisibleSeries] = useState<string[]>(["ebitda", "net_debt", "total_debt", "assets"]);

    // State for grid lines visibility and appearance
    const [showGrid, setShowGrid] = useState(true);
    const [gridOpacity, setGridOpacity] = useState(0.6);
    const [gridDasharray, setGridDasharray] = useState("3 3");

    const [chartView, setChartView] = useState<ChartView>(ChartView.Combined);

    const results =
        data.map((item) => {
            const assets_index = assets.findIndex((asset) => asset.date === item.date);
            const asset = assets_index > -1 ? assets[assets_index] : null;
            return {
                net_debt: item?.net_debt,
                ebitda: item?.ebitda,
                total_debt: asset?.total_debt,
                assets: asset?.total_assets,
                date: item?.date,
            };
        }) || [];

    // State for chart dimensions
    const [chartWidth, setChartWidth] = useState(0);
    const [chartHeight, setChartHeight] = useState(0);

    const {t} = useTranslation();

    // Popup state
    const [popup, setPopup] = useState<{
        isOpen: boolean;
        content: React.ReactNode;
        position: {x: number; y: number};
    }>({
        isOpen: false,
        content: null,
        position: {x: 0, y: 0},
    });

    // Window width for responsive adjustments
    const [windowWidth, setWindowWidth] = useState(typeof window !== "undefined" ? window.innerWidth : 0);

    // Update the useEffect for chart width calculation to better handle medium screens
    useEffect(() => {
        const handleResize = () => {
            setWindowWidth(window.innerWidth);

            // Update chart width based on container size
            if (containerRef.current) {
                const containerWidth = containerRef.current.getBoundingClientRect().width;

                // Calculate appropriate margins based on screen size
                let leftAxisWidth = 60;
                let rightAxisWidth = 60; // Increased right margin space

                if (windowWidth < 480) {
                    leftAxisWidth = 50;
                    rightAxisWidth = 60; // Increased right margin for mobile
                } else if (windowWidth < 768) {
                    leftAxisWidth = 55;
                    rightAxisWidth = 55;
                }

                // Calculate chart width with appropriate margins
                const calculatedWidth = Math.max(containerWidth - leftAxisWidth - rightAxisWidth, 0);
                setChartWidth(calculatedWidth);

                // Set chart height based on container and screen size
                setChartHeight(windowWidth < 480 ? 250 : windowWidth < 768 ? 300 : 350);
            }
        };

        if (typeof window !== "undefined") {
            window.addEventListener("resize", handleResize);
            // Initial calculation
            handleResize();
            return () => window.removeEventListener("resize", handleResize);
        }
    }, [windowWidth]);

    // Format large numbers
    const formatLargeNumber = (value: number) => {
        if (value >= 1000000000) {
            return `${(value / 1000000000).toFixed(1)}B`;
        } else if (value >= 1000000) {
            return `${(value / 1000000).toFixed(1)}M`;
        }
        return value.toString();
    };

    // Toggle visibility of a series
    const toggleSeries = (seriesId: string) => {
        if (visibleSeries.includes(seriesId)) {
            // Don't allow removing the last visible series
            if (visibleSeries.length > 1) {
                setVisibleSeries(visibleSeries.filter((id) => id !== seriesId));
            }
        } else {
            setVisibleSeries([...visibleSeries, seriesId]);
        }
    };

    // Calculate position relative to chart container
    const calculatePopupPosition = (x: number, y: number) => {
        if (!chartRef?.current) return {x, y};

        const chartRect = chartRef.current.getBoundingClientRect();

        // Calculate position relative to chart container
        return {
            x: Math.min(Math.max(x - chartRect.left, 10), chartRect.width - 230),
            y: Math.min(Math.max(y - chartRect.top, 10), chartRect.height - 150),
        };
    };

    // Handle chart click
    const handleChartClick = (data: any) => {
        if (!data || !data.activePayload || !data.activePayload[0]) return;

        const chartData = data.activePayload[0].payload;
        if (!chartRef?.current) return;

        // Calculate position relative to chart container
        const position = calculatePopupPosition(data.chartX, data.chartY);

        const content = (
            <div className="space-y-2">
                <div className="font-semibold text-base border-b pb-1">Year: {chartData.date}</div>
                <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                    <div className="text-sm font-medium">EBITDA:</div>
                    <div className="text-sm">${formatLargeNumber(chartData.ebitda)}</div>
                    <div className="text-sm font-medium">Net Debt:</div>
                    <div className="text-sm">${formatLargeNumber(chartData.net_debt)}</div>
                    <div className="text-sm font-medium">Total Debt:</div>
                    <div className="text-sm">${formatLargeNumber(chartData.total_debt)}</div>
                    <div className="text-sm font-medium">Assets:</div>
                    <div className="text-sm">${formatLargeNumber(chartData.assets)}</div>
                </div>
            </div>
        );

        setPopup({
            isOpen: true,
            content,
            position,
        });
    };

    // Close popup
    const closePopup = () => {
        setPopup((prev) => ({...prev, isOpen: false}));
    };

    // Get responsive chart margins
    const getChartMargins = () => {
        if (windowWidth < 480) {
            return {top: 5, right: 5, bottom: 5, left: 22};
        } else if (windowWidth < 1024) {
            return {top: 5, right: 5, bottom: 10, left: 15};
        } else {
            return {top: 20, right: 15, bottom: 10, left: 15};
        }
    };
    const getChartMarginsSplited = () => {
        if (windowWidth < 480) {
            return {top: 5, right: 5, bottom: 5, left: 40};
        } else if (windowWidth < 1024) {
            return {top: 5, right: 5, bottom: 10, left: 40};
        } else {
            return {top: 20, right: 20, bottom: 10, left: 60};
        }
    };

    // Calculate bar size based on screen width and number of data points
    const getBarSize = () => {
        const dataLength = data.length;
        if (windowWidth < 480) {
            return Math.min(10, (chartWidth - 50) / dataLength / 4); // Reduced bar size for mobile
        } else if (windowWidth < 768) {
            return Math.min(15, (chartWidth - 60) / dataLength / 4);
        } else {
            return Math.min(20, (chartWidth - 80) / dataLength / 4);
        }
    };

    return (
        <div className="mt-10 w-full">
            <div className="w-full pb-2">
                <div className="flex flex-row justify-between items-center">
                    <h2 className={`${styles.chartTitle}`}>{t("stock_charts.debt_indicators")}</h2>
                </div>
                <div className="flex justify-between items-center">
                    <SplitView combinedViewAction={() => setChartView(ChartView.Combined)} splittedViewAction={() => setChartView(ChartView.Splitted)} chartSelected={chartView} />
                    <div className="flex items-center  w-[150px] justify-end">
                        <ToggleSwitch label={t("chart.grid")} checked={showGrid} onCheckedChange={setShowGrid} className="pr-4" />
                        <ChartConfig
                            showGrid={showGrid}
                            setShowGrid={setShowGrid}
                            gridOpacity={gridOpacity}
                            setGridOpacity={setGridOpacity}
                            gridStrokeDasharray={gridDasharray}
                            setGridStrokeDasharray={setGridDasharray}
                        />
                    </div>
                </div>
            </div>
            <div className="w-full">
                <div ref={containerRef} className="w-full">
                    {chartView === ChartView.Combined && (
                        <div ref={chartRef} className={` ${styles.container}`}>
                            <ChartContainer
                                config={{
                                    ebitda: {
                                        label: t("indicator.ebitda"),
                                        color: `hsl(${colors.ebitda})`, // Use dynamic color
                                    },
                                    net_debt: {
                                        label: t("indicator.net_debt"),
                                        color: `hsl(${colors.net_debt})`, // Use dynamic color
                                    },
                                    total_debt: {
                                        label: t("indicator.total_debt"),
                                        color: `hsl(${colors.total_debt})`, // Use dynamic color
                                    },
                                    assets: {
                                        label: t("indicator.total_assets"),
                                        color: `hsl(${colors.assets})`, // Use dynamic color
                                    },
                                }}>
                                <BarChart data={results} margin={getChartMargins()} onClick={handleChartClick} barSize={getBarSize()} maxBarSize={20} height={chartHeight}>
                                    {showGrid && (
                                        <CartesianGrid
                                            strokeDasharray={gridDasharray}
                                            opacity={gridOpacity}
                                            vertical={true}
                                            horizontal={true}
                                            stroke="currentColor"
                                            className="text-gray-300 dark:text-gray-400"
                                        />
                                    )}
                                    <XAxis
                                        dataKey="date"
                                        tickLine={true}
                                        axisLine={true}
                                        tick={{fontSize: windowWidth < 480 ? 10 : 12}}
                                        height={30}
                                        minTickGap={windowWidth < 480 ? 10 : 0}
                                        interval={"preserveStartEnd"}
                                    />
                                    <YAxis
                                        width={windowWidth < 480 ? 50 : 60} // Increased width
                                        tickCount={windowWidth < 480 ? 4 : 5}
                                        axisLine={true}
                                        tickLine={true}
                                        tick={(props) => {
                                            const {x, y, payload} = props;

                                            if (payload) {
                                                const value = payload.value;
                                                const formattedValue = formatLargeNumber(value);

                                                return (
                                                    <text x={x} y={y} dy={4} textAnchor="end" fill="currentColor" fontSize={windowWidth < 480 ? 10 : 12} style={{userSelect: "none"}}>
                                                        {formattedValue}
                                                    </text>
                                                );
                                            }

                                            return <></>; // Return an empty React fragment instead of null
                                        }}
                                    />
                                    <ChartTooltip
                                        content={<ChartTooltipContent />}
                                        contentStyle={{
                                            backgroundColor: "var(--background)",
                                            borderColor: "var(--border)",
                                            color: "var(--foreground)",
                                        }}
                                    />
                                    {visibleSeries.includes("ebitda") && <Bar dataKey="ebitda" fill="var(--color-ebitda)" radius={[4, 4, 0, 0]} isAnimationActive={false} />}
                                    {visibleSeries.includes("net_debt") && <Bar dataKey="net_debt" fill="var(--color-net_debt)" radius={[4, 4, 0, 0]} isAnimationActive={false} />}
                                    {visibleSeries.includes("total_debt") && <Bar dataKey="total_debt" fill="var(--color-total_debt)" radius={[4, 4, 0, 0]} isAnimationActive={false} />}
                                    {visibleSeries.includes("assets") && <Bar dataKey="assets" fill="var(--color-assets)" radius={[4, 4, 0, 0]} isAnimationActive={false} />}
                                </BarChart>
                            </ChartContainer>
                            {popup.isOpen && <Popup isOpen={popup.isOpen} onClose={closePopup} content={popup.content} position={popup.position} containerRelative={true} />}
                            <ChartLegend
                                items={[
                                    {id: "ebitda", color: `hsl(${colors.ebitda})`, label: t("indicator.ebitda")},
                                    {id: "net_debt", color: `hsl(${colors.net_debt})`, label: t("indicator.net_debt")},
                                    {id: "total_debt", color: `hsl(${colors.total_debt})`, label: t("indicator.total_debt")},
                                    {id: "assets", color: `hsl(${colors.assets})`, label: t("indicator.total_assets")},
                                ]}
                                activeItems={visibleSeries}
                                onToggle={toggleSeries}
                                className="mt-2"
                            />
                        </div>
                    )}

                    {chartView === ChartView.Splitted && (
                        <div className="grid grid-cols-1 gap-4 px-4 w-full">
                            <div ref={chartRef} className={` ${styles.container}`}>
                                {/* EBITDA Chart */}
                                {visibleSeries.includes("ebitda") && (
                                    <div className="space-y-1 w-full">
                                        <ChartContainer
                                            config={{
                                                ebitda: {
                                                    label: t("indicator.ebitda"),
                                                    color: `hsl(${colors.ebitda})`,
                                                },
                                            }}
                                            className="h-[150px] sm:h-[170px] md:h-[180px] w-full">
                                            <BarChart
                                                data={results}
                                                margin={getChartMarginsSplited()}
                                                width={windowWidth < 480 ? (containerRef.current?.clientWidth ?? 300) - 5 : chartWidth}
                                                height={windowWidth < 480 ? 120 : 150}>
                                                {showGrid && (
                                                    <CartesianGrid
                                                        strokeDasharray={gridDasharray}
                                                        opacity={gridOpacity}
                                                        vertical={true}
                                                        horizontal={true}
                                                        stroke="currentColor"
                                                        className="text-gray-300 dark:text-gray-400"
                                                    />
                                                )}
                                                <XAxis
                                                    dataKey="date"
                                                    tickLine={true}
                                                    axisLine={true}
                                                    tick={{fontSize: windowWidth < 480 ? 8 : 10}}
                                                    height={windowWidth < 480 ? 20 : 25}
                                                    interval={"preserveStartEnd"}
                                                />
                                                <YAxis
                                                    width={windowWidth < 480 ? 40 : 50}
                                                    tickCount={4}
                                                    axisLine={true}
                                                    tickLine={true}
                                                    tick={{fontSize: windowWidth < 480 ? 8 : 10}}
                                                    tickFormatter={formatLargeNumber}
                                                />
                                                <ChartTooltip content={<ChartTooltipContent />} />
                                                <Bar dataKey="ebitda" fill="var(--color-ebitda)" radius={[4, 4, 0, 0]} isAnimationActive={false} />
                                            </BarChart>
                                        </ChartContainer>
                                    </div>
                                )}
                                <ChartLegend
                                    items={[{id: "ebitda", color: `hsl(${colors.ebitda})`, label: t("indicator.ebitda")}]}
                                    activeItems={visibleSeries}
                                    onToggle={toggleSeries}
                                    className="mt-2"
                                />

                                {/* Net Debt Chart */}
                                {visibleSeries.includes("net_debt") && (
                                    <div className="space-y-1 w-full">
                                        <ChartContainer
                                            config={{
                                                net_debt: {
                                                    label: "Net Debt",
                                                    color: `hsl(${colors.net_debt})`,
                                                },
                                            }}
                                            className="h-[150px] sm:h-[170px] md:h-[180px] w-full">
                                            <BarChart
                                                data={results}
                                                margin={getChartMarginsSplited()}
                                                width={windowWidth < 480 ? (containerRef.current?.clientWidth ?? 300) - 5 : chartWidth}
                                                height={windowWidth < 480 ? 120 : 150}>
                                                {showGrid && (
                                                    <CartesianGrid
                                                        strokeDasharray={gridDasharray}
                                                        opacity={gridOpacity}
                                                        vertical={true}
                                                        horizontal={true}
                                                        stroke="currentColor"
                                                        className="text-gray-300 dark:text-gray-400"
                                                    />
                                                )}
                                                <XAxis
                                                    dataKey="date"
                                                    tickLine={true}
                                                    axisLine={true}
                                                    tick={{fontSize: windowWidth < 480 ? 8 : 10}}
                                                    height={windowWidth < 480 ? 20 : 25}
                                                    interval={"preserveStartEnd"}
                                                />
                                                <YAxis
                                                    width={windowWidth < 480 ? 40 : 50}
                                                    tickCount={4}
                                                    axisLine={true}
                                                    tickLine={true}
                                                    tick={{fontSize: windowWidth < 480 ? 8 : 10}}
                                                    tickFormatter={formatLargeNumber}
                                                />
                                                <ChartTooltip content={<ChartTooltipContent />} />
                                                <Bar dataKey="net_debt" fill="var(--color-net_debt)" radius={[4, 4, 0, 0]} isAnimationActive={false} />
                                            </BarChart>
                                        </ChartContainer>
                                    </div>
                                )}
                                <ChartLegend
                                    items={[{id: "net_debt", color: `hsl(${colors.net_debt})`, label: t("indicator.net_debt")}]}
                                    activeItems={visibleSeries}
                                    onToggle={toggleSeries}
                                    className="mt-2"
                                />

                                {/* Total Debt Chart */}
                                {visibleSeries.includes("total_debt") && (
                                    <div className="space-y-1 w-full">
                                        <ChartContainer
                                            config={{
                                                total_debt: {
                                                    label: t("indicator.total_debt"),
                                                    color: `hsl(${colors.total_debt})`,
                                                },
                                            }}
                                            className="h-[150px] sm:h-[170px] md:h-[180px] w-full">
                                            <BarChart
                                                data={results}
                                                margin={getChartMarginsSplited()}
                                                width={windowWidth < 480 ? (containerRef.current?.clientWidth ?? 300) - 5 : chartWidth}
                                                height={windowWidth < 480 ? 120 : 150}>
                                                {showGrid && (
                                                    <CartesianGrid
                                                        strokeDasharray={gridDasharray}
                                                        opacity={gridOpacity}
                                                        vertical={true}
                                                        horizontal={true}
                                                        stroke="currentColor"
                                                        className="text-gray-300 dark:text-gray-400"
                                                    />
                                                )}
                                                <XAxis
                                                    dataKey="date"
                                                    tickLine={true}
                                                    axisLine={true}
                                                    tick={{fontSize: windowWidth < 480 ? 8 : 10}}
                                                    height={windowWidth < 480 ? 20 : 25}
                                                    interval={"preserveStartEnd"}
                                                />
                                                <YAxis
                                                    width={windowWidth < 480 ? 40 : 50}
                                                    tickCount={4}
                                                    axisLine={true}
                                                    tickLine={true}
                                                    tick={{fontSize: windowWidth < 480 ? 8 : 10}}
                                                    tickFormatter={formatLargeNumber}
                                                />
                                                <ChartTooltip content={<ChartTooltipContent />} />
                                                <Bar dataKey="total_debt" fill="var(--color-total_debt)" radius={[4, 4, 0, 0]} isAnimationActive={false} />
                                            </BarChart>
                                        </ChartContainer>
                                    </div>
                                )}
                                <ChartLegend
                                    items={[{id: "total_debt", color: `hsl(${colors.total_debt})`, label: t("indicator.total_debt")}]}
                                    activeItems={visibleSeries}
                                    onToggle={toggleSeries}
                                    className="mt-2"
                                />

                                {/* Assets Chart */}
                                {visibleSeries.includes("assets") && (
                                    <div className="space-y-1 w-full">
                                        <ChartContainer
                                            config={{
                                                assets: {
                                                    label: t("indicato.total_assets"),
                                                    color: `hsl(${colors.assets})`,
                                                },
                                            }}
                                            className="h-[150px] sm:h-[170px] md:h-[180px] w-full">
                                            <BarChart
                                                data={results}
                                                margin={getChartMarginsSplited()}
                                                width={windowWidth < 480 ? (containerRef.current?.clientWidth ?? 300) - 5 : chartWidth}
                                                height={windowWidth < 480 ? 120 : 150}>
                                                {showGrid && (
                                                    <CartesianGrid
                                                        strokeDasharray={gridDasharray}
                                                        opacity={gridOpacity}
                                                        vertical={true}
                                                        horizontal={true}
                                                        stroke="currentColor"
                                                        className="text-gray-300 dark:text-gray-400"
                                                    />
                                                )}
                                                <XAxis
                                                    dataKey="date"
                                                    tickLine={true}
                                                    axisLine={true}
                                                    tick={{fontSize: windowWidth < 480 ? 8 : 10}}
                                                    height={windowWidth < 480 ? 20 : 25}
                                                    interval={"preserveStartEnd"}
                                                />
                                                <YAxis
                                                    width={windowWidth < 480 ? 40 : 50}
                                                    tickCount={4}
                                                    axisLine={true}
                                                    tickLine={true}
                                                    tick={{fontSize: windowWidth < 480 ? 8 : 10}}
                                                    tickFormatter={formatLargeNumber}
                                                />
                                                <ChartTooltip content={<ChartTooltipContent />} />
                                                <Bar dataKey="assets" fill="var(--color-assets)" radius={[4, 4, 0, 0]} isAnimationActive={false} />
                                            </BarChart>
                                        </ChartContainer>
                                    </div>
                                )}
                                <ChartLegend
                                    items={[{id: "assets", color: `hsl(${colors.assets})`, label: t("indicator.total_assets")}]}
                                    activeItems={visibleSeries}
                                    onToggle={toggleSeries}
                                    className="mt-2"
                                />
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}

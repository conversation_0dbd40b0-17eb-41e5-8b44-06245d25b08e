"use client";

import {cn} from "@/lib/utils";
import styles from "./legend.module.css";

interface LegendItemProps {
    color: string;
    label: string;
    active: boolean;
    onClick: () => void;
    className?: string;
}

export function LegendItem({color, label, active, onClick, className}: LegendItemProps) {
    return (
        <div className={cn(styles.legendItem, active ? styles.active : styles.inactive, className)} onClick={onClick}>
            <div className={styles.legendColor} style={{backgroundColor: color}} />
            <span className={styles.legendLabel}>{label}</span>
        </div>
    );
}

interface ChartLegendProps {
    items: {
        id: string;
        color: string;
        label: string;
    }[];
    activeItems: string[];
    onToggle: (id: string) => void;
    className?: string;
}

export function ChartLegend({items, activeItems, onToggle, className}: ChartLegendProps) {
    return (
        <div className={cn(styles.legendContainer, className)}>
            {items.map((item) => (
                <LegendItem key={item.id} color={item.color} label={item.label} active={activeItems.includes(item.id)} onClick={() => onToggle(item.id)} />
            ))}
        </div>
    );
}

.legendContainer {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: center;
    margin-top: 0.5rem;
  }
  
  .legendItem {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition-property: opacity;
    transition-duration: 200ms;
    transition-timing-function: ease;
    user-select: none;
  }
  
  .legendItem.active {
    opacity: 1;
  }
  
  .legendItem.inactive {
    opacity: 0.4;
  }
  
  .legendColor {
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 0.125rem;
  }
  
  .legendLabel {
    font-size: 1rem;
  }
  
  @media (min-width: 640px) {
    .legendLabel {
      font-size: 1.4rem;
    }
  }
  
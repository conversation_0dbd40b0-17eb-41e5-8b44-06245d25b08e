"use client";

import {But<PERSON>} from "@/components/UI/button";
import {Popover, PopoverContent, PopoverTrigger} from "@/components/UI/popover";
import {Slider} from "@/components/UI/slider";
import {ToggleSwitch} from "@/components/UI/ToogleSwitch";
import {Settings} from "@mui/icons-material";
import {useState} from "react";
import {useTranslation} from "react-i18next";
import {Label} from "../UI/Label/index";

interface ChartConfigProps {
    showGrid: boolean;
    setShowGrid: (value: boolean) => void;
    gridOpacity: number;
    setGridOpacity: (value: number) => void;
    gridStrokeDasharray: string;
    setGridStrokeDasharray: (value: string) => void;
}

export function ChartConfig({showGrid, setShowGrid, gridOpacity, setGridOpacity, gridStrokeDasharray, setGridStrokeDasharray}: ChartConfigProps) {
    const [open, setOpen] = useState(false);
    const {t} = useTranslation();

    const handleOpacityChange = (value: number[]) => {
        setGridOpacity(value[0]);
    };

    const handleDasharrayChange = (option: string) => {
        setGridStrokeDasharray(option);
        setOpen(false);
    };

    return (
        <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
                <Button variant="outline" size="icon" className="h-[40px] w-[40px]">
                    <Settings sx={{fontSize: 20}} fontSize="large" className="h-10 w-10" />
                    <span className="sr-only">Open chart settings</span>
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80" align="end">
                <div className="grid gap-4">
                    <div className="space-y-2">
                        <h4 className="font-medium leading-none text-[1.8rem]">{t("chart.grid_settings")}</h4>
                        <p className="text-sm text-muted-foreground">{t("chart.grid_settings_info")}</p>
                    </div>
                    <div className="grid gap-2">
                        <div className="flex items-center justify-between">
                            <ToggleSwitch label={t("chart.show_grid")} checked={showGrid} onCheckedChange={setShowGrid} />
                        </div>

                        <div className="space-y-1 pt-2">
                            <Label htmlFor="opacity">
                                {t("chart.grid_opacity")}: {gridOpacity.toFixed(1)}
                            </Label>
                            <Slider id="opacity" min={0.1} max={1.0} step={0.1} defaultValue={[gridOpacity]} onValueChange={handleOpacityChange} disabled={!showGrid} />
                        </div>

                        <div className="space-y-1 pt-2">
                            <Label>{t("chart.grid_line_style")}</Label>
                            <div className="grid grid-cols-3 gap-2 pt-1">
                                <Button variant={gridStrokeDasharray === "0" ? "default" : "outline"} size="sm" onClick={() => handleDasharrayChange("0")} disabled={!showGrid}>
                                    {t("chart.solid")}
                                </Button>
                                <Button variant={gridStrokeDasharray === "3 3" ? "default" : "outline"} size="sm" onClick={() => handleDasharrayChange("3 3")} disabled={!showGrid}>
                                    {t("chart.dashed")}
                                </Button>
                                <Button variant={gridStrokeDasharray === "1 5" ? "default" : "outline"} size="sm" onClick={() => handleDasharrayChange("1 5")} disabled={!showGrid}>
                                    {t("chart.dotted")}
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </PopoverContent>
        </Popover>
    );
}

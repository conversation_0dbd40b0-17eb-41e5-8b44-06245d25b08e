.main-header {
    height: 118px;
    width: 100%;
}

.navbar {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    
}

.navbar-link{
    color: black;
    font-size: 14px;
    font-weight: 500;
    margin: 0 10px;
    font-family: var(--poppins);
}

.search {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgb(var(--color-sf-black));
    padding: 0.5rem 1.5rem;
    border-radius: 1rem;
}

.login-container {
    margin: 0 10px;
}

.login {
    border: 2px solid rgb(var(--color-sf-primary));
    color: rgb(var(--color-sf-primary));
    padding: 0.5rem 1.5rem;
    border-radius: 1rem;
}

.mobile-logo-header {
    margin: 0 30px 15px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-items: center;
    position: relative;
}

.mobile-logo-header  img {
    margin: 0 auto;
}

.close-menu {
    position: absolute;
    top: 1px;
    right: -5%;
}

.mobile-menu-list {
    padding: 15px 30px;
}

.mobile-menu-list li {
    padding: 10px 0;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.5;
}
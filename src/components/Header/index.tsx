import {Touch} from "@/styles/components/Ui";
import {UserResponse} from "@/types/api/UserResponse";
import {Close} from "@mui/icons-material";
import MenuIcon from "@mui/icons-material/Menu";
import {Drawer} from "@mui/material";
import {Search} from "lucide-react";
import {signIn, signOut, useSession} from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import {useRouter} from "next/navigation";
import {useState} from "react";
import {useTranslation} from "react-i18next";
import {LanguageButton} from "../LanguageButton";
import SearchModal from "../SearchModal";
import styles from "./styles.module.css";

enum HeaderType {
    link = "link",
    action = "action",
}

type HeaderItem = {
    label: string;
    action: string | (() => void);
    is_enabled: boolean;
    type: HeaderType;
};

type PropsMenu = {
    isOpened?: boolean;
    user: UserResponse | null;
    isSearching: boolean;
    is_authenicated: boolean;
    setIsSearching: (params: boolean) => void;
    setIsOpened?: (value: boolean) => void;
};

export function HeaderMobile({isOpened, setIsOpened, user}: PropsMenu) {
    return (
        <div className="min-[639px]:hidden   h-full mx-5">
            <div className="flex items-center justify-between h-full w-100">
                <div className="flex items-center  h-full">
                    {setIsOpened && (
                        <Touch className="touch" onClick={() => setIsOpened(!isOpened)}>
                            <MenuIcon sx={{fontSize: 24, color: "#666"}} />
                        </Touch>
                    )}
                </div>
                <div>
                    <Image src={"/imgs/tiba_logo.svg"} alt="Tiba logo" width={120} height={14} />
                </div>
                <LanguageButton light={false} />
            </div>
        </div>
    );
}

function HeaderDesktop({user, is_authenicated, isSearching, setIsSearching}: PropsMenu) {
    const router = useRouter();

    const {t} = useTranslation();

    return (
        <div className="max-sm:hidden h-full mx-10">
            <div className="grid md:grid-cols-3 h-full">
                <div className="flex justify-between h-full items-center col-span-2">
                    <Link href={"/"}>
                        <Image src={"/imgs/tiba_logo.svg"} alt="Tiba logo" width={146} height={40} />
                    </Link>
                    <div>
                        <ul className={`flex space-x-4 ${styles.navbar}`}>
                            <li>
                                <Link className={`${styles["navbar-link"]}`} href={"/"}>
                                    {t("menu.home")}
                                </Link>
                            </li>
                            <li>
                                <Link className={`${styles["navbar-link"]}`} href={"/dashboard/wallet"}>
                                    {t("menu.wallet")}
                                </Link>
                            </li>
                            <li>
                                <Link className={`${styles["navbar-link"]}`} href={"/dashboard/watchlist"}>
                                    {t("menu.watchlist")}
                                </Link>
                            </li>
                            <li>
                                <Link className={`${styles["navbar-link"]}`} href={"/stocks"}>
                                    {t("menu.stocks")}
                                </Link>
                            </li>
                            <li>
                                <button className={`${styles["navbar-link"]} ${styles.search}`} onClick={() => setIsSearching(!isSearching)}>
                                    <Search size={17} className="mr-2" /> {t("menu.search")}
                                </button>
                            </li>
                            {is_authenicated && (
                                <li onClick={() => signOut({redirect: false})}>
                                    <span className={`${styles["navbar-link"]} cursor-pointer`}>{t("menu.logout")}</span>
                                </li>
                            )}
                        </ul>
                    </div>
                </div>
                <div className={`flex items-center justify-center h-full w-full`}>
                    <div className="mr-10">
                        <LanguageButton light={false} />
                    </div>
                    {is_authenicated ? (
                        <div className="cursor-pointer h-full flex items-center justify-center" onClick={() => router.push("/dashboard/user")}>
                            <div className="ml-10">
                                <Image src={"/imgs/user_icon.png"} alt="User icon" width={35} height={35} />
                            </div>
                            <span className="ml-8 text-[17px]">{t("greetings.hello")},</span>
                            <span className="font-bold ml-1 [17px]">{user?.firstname}</span>
                        </div>
                    ) : (
                        <button className={styles.login} onClick={() => signIn()}>
                            {t("menu.login")}
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
}

export function Header() {
    const {t} = useTranslation();
    const [isOpened, setIsOpened] = useState(false);

    const {data: session}: any = useSession();
    const is_authenicated = session ? true : false;
    const [isSearching, setIsSearching] = useState(false);

    const router = useRouter();

    const user: UserResponse | null = is_authenicated ? session?.user : null;

    return (
        <header className={`${styles["main-header"]} bg-white w-full`}>
            <SearchModal isOpen={isSearching} handleOpen={() => setIsSearching(false)} />
            <Drawer anchor="left" open={isOpened} onClose={() => setIsOpened(false)}>
                <div className="w-[90vw] h-full pt-5">
                    <div className={`${styles["mobile-logo-header"]}`}>
                        <Image src={"/imgs/tiba_logo.svg"} alt="Tiba logo" width={110} height={30} />
                        <div className={`${styles["close-menu"]}`} onClick={() => setIsOpened(false)}>
                            <Close sx={{fontSize: "26px"}} />
                        </div>
                    </div>
                    <ul className={`${styles["mobile-menu-list"]}`}>
                        <li>
                            <Link className={`${styles["navbar-link"]}`} href={"/"}>
                                {t("menu.home")}
                            </Link>
                        </li>
                        <li>
                            <Link className={`${styles["navbar-link"]}`} href={"/dashboard/wallet"}>
                                {t("menu.wallet")}
                            </Link>
                        </li>
                        <li>
                            <Link className={`${styles["navbar-link"]}`} href={"/dashboard/watchlist"}>
                                {t("menu.watchlist")}
                            </Link>
                        </li>
                        <li>
                            <Link className={`${styles["navbar-link"]}`} href={"/stocks"}>
                                {t("menu.stocks")}
                            </Link>
                        </li>
                        <li>
                            <button
                                className={`${styles["navbar-link"]} ${styles.search}`}
                                onClick={() => {
                                    setIsOpened(false);
                                    setIsSearching(!isSearching);
                                }}>
                                <Search size={17} className="mr-2" /> {t("menu.search")}
                            </button>
                        </li>
                        {is_authenicated && (
                            <li onClick={() => signOut({redirect: false})}>
                                <span className={`${styles["navbar-link"]} cursor-pointer`}>{t("menu.logout")}</span>
                            </li>
                        )}
                        {!is_authenicated && (
                            <li onClick={() => signIn()} className={styles["login-container"]}>
                                <span className={`${styles.login} cursor-pointer`}>{t("menu.login")}</span>
                            </li>
                        )}
                    </ul>
                </div>
            </Drawer>
            <HeaderDesktop user={user} is_authenicated={is_authenicated} isSearching={isSearching} setIsSearching={setIsSearching} />

            <HeaderMobile isOpened={isOpened} setIsOpened={setIsOpened} is_authenicated={is_authenicated} user={user} isSearching={isSearching} setIsSearching={setIsSearching} />
        </header>
    );
}

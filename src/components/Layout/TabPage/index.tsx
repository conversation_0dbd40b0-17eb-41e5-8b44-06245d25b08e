import {ReactNode} from "react";
import styles from "./styles.module.css";

interface Props {
    label: string;
    children?: ReactNode;
    isActived: boolean;
    action: (params?: any) => void;
}

export default function TabPage({label, children, isActived = false, action}: Props) {
    return (
        <button onClick={action} className={`${styles.button} ${isActived && styles.is_actived}`}>
            {children && children} {label}
        </button>
    );
}

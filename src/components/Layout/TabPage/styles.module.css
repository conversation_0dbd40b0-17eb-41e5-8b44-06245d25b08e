.button {
    background-color: var(--color-sf-light-purple);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem 1rem;
    color: rgba(26, 12, 65, 1);
    font-size: 16px;
    font-weight: 500;
    border-radius: 8px;
    transition: 300ms;
}

.button:hover {
    transition: 300ms;
    background: var(--color-sf-dark-purple);
    color: rgb(var(--color-sf-white));
}

.is_actived {
    transition: 300ms;
    background: var(--color-sf-dark-purple);
    color: rgb(var(--color-sf-white));
}
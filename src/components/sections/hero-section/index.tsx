"use client";

import {<PERSON><PERSON><PERSON>, ChevronLeft, ChevronRight} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import {useEffect, useRef, useState} from "react";
import {useTranslation} from "react-i18next";
import AnimatedBackground from "./animated-background";
import styles from "./hero-section.module.css";

// Default placeholder for images
const DEFAULT_PLACEHOLDER = "/imgs/hero-slider/financial-chart.png";

// Update the slides array with refined positioning and sizing
const slides = [
    {
        id: "slide1",
        title: "landingpage.slider_one.title",
        subtitle: "landingpage.slider_one.subtitle",
        description: "landingpage.slider_one.description",
        ctaText: "landingpage.slider_one.ctaText",
        ctaLink: "#about",
        layout: "grid",
        widgets: [
            {
                id: "chart",
                type: "chart",
                image: "/imgs/hero-slider/revslider_2_img6.jpg",
                alt: "Weekly performance chart",
                position: "top-left",
                delay: 0.8,
            },
            {
                id: "businessman",
                type: "person",
                image: "/imgs/hero-slider/businessman-tablet.jpg",
                alt: "Businessman with tablet",
                position: "top-right",
                delay: 1.0,
            },
            {
                id: "woman",
                type: "person",
                image: "/imgs/hero-slider/woman-blue-dress.jpg",
                alt: "Woman with smartphone",
                position: "bottom-left",
                delay: 1.2,
            },
            {
                id: "ethereum",
                type: "crypto",
                image: "/imgs/hero-slider/revslider_2_img10.jpg",
                alt: "Ethereum price",
                position: "bottom-left-overlay",
                delay: 1.4,
            },
            {
                id: "analysis",
                type: "chart",
                image: "/imgs/hero-slider/revslider_2_img9.jpg",
                alt: "Financial analysis",
                position: "bottom-right",
                delay: 1.6,
            },
        ],
    },
    {
        id: "slide2",
        title: "landingpage.slider_two.title",
        subtitle: "landingpage.slider_two.subtitle",
        description: "landingpage.slider_two.description",
        ctaText: "landingpage.slider_two.ctaText",
        ctaLink: "#plans",
        layout: "overlay",
        mainImage: "/imgs/hero-slider/woman-with-phone.jpg",
        widgets: [
            {
                id: "lifetime-income",
                type: "stat-card",
                image: "/imgs/hero-slider/revslider_2_img3.jpg",
                alt: "Lifetime Income",
                position: "left-top",
                delay: 0.8,
                content: {
                    icon: "money",
                    title: "Lifetime Income",
                    value: "$40,728",
                    color: "pink",
                },
            },
            {
                id: "lifetime-outcome",
                type: "stat-card",
                image: "/imgs/hero-slider/revslider_2_img5.jpg",
                alt: "Lifetime Outcome",
                position: "left-middle",
                delay: 1.1,
                content: {
                    icon: "money-out",
                    title: "Lifetime Outcome",
                    value: "$30,239",
                    color: "pink",
                },
            },
            {
                id: "bonus-income",
                type: "stat-card",
                alt: "Bonus Income",
                position: "left-bottom",
                delay: 1.4,
                content: {
                    icon: "stars",
                    title: "Bonus Income",
                    value: "$2,490",
                    color: "purple",
                },
            },
            {
                id: "financial-chart",
                type: "chart",
                image: "/imgs/hero-slider/financial-chart.png",
                alt: "Financial chart",
                position: "right-bottom",
                delay: 1.7,
            },
        ],
    },
];

export default function HeroSection() {
    const {t} = useTranslation();
    const [currentSlide, setCurrentSlide] = useState(0);
    const [isAutoPlaying, setIsAutoPlaying] = useState(true);
    const [isAnimating, setIsAnimating] = useState(false);
    const [animationKey, setAnimationKey] = useState(0); // Used to force animation restart
    const carouselRef = useRef<HTMLDivElement>(null);

    // Auto-advance slides
    useEffect(() => {
        if (!isAutoPlaying) return;

        const interval = setInterval(() => {
            if (!isAnimating) {
                goToNextSlide();
            }
        }, 7000); // Longer interval to allow all animations to complete

        return () => clearInterval(interval);
    }, [isAutoPlaying, isAnimating]);

    // Pause auto-play on hover
    const handleMouseEnter = () => setIsAutoPlaying(false);
    const handleMouseLeave = () => setIsAutoPlaying(true);

    const goToSlide = (index: number) => {
        if (isAnimating || index === currentSlide) return;

        setIsAnimating(true);
        setCurrentSlide(index);
        setAnimationKey((prev) => prev + 1); // Force animation restart

        // Resume auto-play after user interaction
        setIsAutoPlaying(false);
        setTimeout(() => {
            setIsAnimating(false);
            setIsAutoPlaying(true);
        }, 700); // Match this with the CSS transition duration
    };

    const goToPrevSlide = () => {
        if (isAnimating) return;
        const newIndex = (currentSlide - 1 + slides.length) % slides.length;
        goToSlide(newIndex);
    };

    const goToNextSlide = () => {
        if (isAnimating) return;
        const newIndex = (currentSlide + 1) % slides.length;
        goToSlide(newIndex);
    };

    // Ensure image source is never empty
    const getImageSrc = (src: string | undefined): string => {
        if (!src || typeof src !== "string" || src.trim() === "") {
            return DEFAULT_PLACEHOLDER;
        }
        return src;
    };

    // Render stat card with icon and content
    const renderStatCard = (widget: any) => {
        let iconContent;

        if (widget.content.icon === "stars") {
            iconContent = (
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={styles.statIcon}>
                    <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
                </svg>
            );
        } else if (widget.content.icon === "money") {
            iconContent = (
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={styles.statIcon}>
                    <rect x="2" y="6" width="20" height="12" rx="2" />
                    <path d="M12 12a2 2 0 1 0 0-4 2 2 0 0 0 0 4z" />
                    <path d="M6 12h.01M18 12h.01" />
                </svg>
            );
        } else if (widget.content.icon === "money-out") {
            iconContent = (
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={styles.statIcon}>
                    <path d="M2 17a5 5 0 0 0 10 0c0-2.76-2.5-5-5-3-2.5-2-5 .24-5 3Z" />
                    <path d="M12 17a5 5 0 0 0 10 0c0-2.76-2.5-5-5-3-2.5-2-5 .24-5 3Z" />
                    <path d="M7 14c3.22-2.91 4.29-8.75 5-12 1.66 2.38 4.94 9 5 12" />
                    <path d="M22 9c-4.29 0-7.14-2.33-10-7 5.71 0 10 4.67 10 7Z" />
                </svg>
            );
        }

        return (
            <div className={`${styles.statCard} ${styles[widget.content.color]}`}>
                <div className={styles.statIconContainer}>{iconContent}</div>
                <div className={styles.statContent}>
                    <p className={styles.statTitle}>{widget.content.title}</p>
                    <p className={styles.statAmount}>{widget.content.value}</p>
                </div>
            </div>
        );
    };

    return (
        <section className={styles.section} onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave} id="home">
            {/* Animated background */}
            <AnimatedBackground />

            {/* Carousel container */}
            <div className={styles.carouselContainer}>
                {/* Carousel slides */}
                <div ref={carouselRef} className={styles.carouselTrack} style={{transform: `translateX(-${currentSlide * 100}%)`}}>
                    {slides.map((slide, index) => (
                        <div key={slide.id} className={`${styles.carouselSlide} ${index === currentSlide ? styles.activeSlide : ""}`}>
                            <div className={styles.container}>
                                {/* Left content */}
                                <div className={styles.leftContent}>
                                    <div className={`${styles.contentWrapper} ${index === currentSlide ? styles.animate : ""}`} key={`content-${animationKey}-${index}`}>
                                        <h1 className={styles.heading}>
                                            <div className={`${styles.animatedText} ${styles.animateTitle}`}>{t(`${slide.title}`)}</div>
                                            <div className={`${styles.headingHighlight} ${styles.animatedText} ${styles.animateSubtitle}`}>{t(slide.subtitle)}</div>
                                        </h1>

                                        <p className={`${styles.subheading} ${styles.animatedText} ${styles.animateDescription}`}>{t(slide.description)}</p>

                                        <div className={`${styles.statsContainer} ${styles.animatedText} ${styles.animateStats}`}>
                                            <div className={styles.statValue}>25.9 M+</div>
                                            {/* <p className={styles.statLabel}>{t("hero.invested")}</p> */}
                                        </div>

                                        <Link href={slide.ctaLink} className={`${styles.ctaButton} ${styles.animatedText} ${styles.animateCta}`}>
                                            {t(slide.ctaText)}
                                            <ArrowRight className={styles.ctaIcon} size={18} />
                                        </Link>
                                    </div>
                                </div>

                                {/* Right content - Based on layout type */}
                                <div className={styles.rightContent}>
                                    {slide.layout === "grid" ? (
                                        // Grid layout (Slide 1)
                                        <div className={styles.gridLayout}>
                                            {slide.widgets.map((widget) => {
                                                // Determine the appropriate CSS class based on widget position
                                                const positionClass = styles[widget.position.replace(/-/g, "")];

                                                // Determine animation delay style
                                                const animationDelayStyle = {
                                                    animationDelay: `${widget.delay}s`,
                                                };

                                                return (
                                                    <div key={widget.id} className={`${styles.widget} ${positionClass} ${index === currentSlide ? styles.animate : ""}`} style={animationDelayStyle}>
                                                        <Image
                                                            src={getImageSrc(widget.image) || "/placeholder.svg"}
                                                            alt={widget.alt}
                                                            width={widget.type === "person" ? 400 : 300}
                                                            height={widget.type === "person" ? 400 : 200}
                                                            className={`${styles.widgetImage} ${styles[widget.type]} ${widget.type === "person" && styles.personImage} ${
                                                                widget.type === "chart" && styles.chartImage
                                                            }
                                                            
                                                            `}
                                                            priority={widget.position === "top-left" || widget.position === "top-right"}
                                                        />
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    ) : (
                                        // Overlay layout (Slide 2)
                                        <div className={styles.overlayLayout}>
                                            {/* Main background image */}
                                            <div className={`${styles.mainImageContainer} ${index === currentSlide ? styles.animate : ""}`}>
                                                <Image
                                                    src={getImageSrc(slide.mainImage) || "/placeholder.svg"}
                                                    alt="Woman with phone"
                                                    width={600}
                                                    height={600}
                                                    className={styles.mainImage}
                                                    priority={true}
                                                />
                                            </div>

                                            {/* Overlay widgets */}
                                            {slide.widgets.map((widget) => {
                                                // Determine the appropriate CSS class based on widget position
                                                const positionClass = styles[widget.position.replace(/-/g, "")];

                                                // Determine animation delay style
                                                const animationDelayStyle = {
                                                    animationDelay: `${widget.delay}s`,
                                                };

                                                return (
                                                    <div
                                                        key={widget.id}
                                                        className={`${styles.overlayWidget} ${positionClass} ${index === currentSlide ? styles.animate : ""}`}
                                                        style={animationDelayStyle}>
                                                        {widget.type === "stat-card" ? (
                                                            renderStatCard(widget)
                                                        ) : (
                                                            <Image src={getImageSrc(widget.image) || "/placeholder.svg"} alt={widget.alt} width={300} height={200} className={styles.chartImage} />
                                                        )}
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

                {/* Carousel navigation */}
                <button className={`${styles.carouselButton} ${styles.carouselButtonPrev}`} onClick={goToPrevSlide} aria-label="Previous slide" disabled={isAnimating}>
                    <ChevronLeft size={24} />
                </button>

                <button className={`${styles.carouselButton} ${styles.carouselButtonNext}`} onClick={goToNextSlide} aria-label="Next slide" disabled={isAnimating}>
                    <ChevronRight size={24} />
                </button>
            </div>

            {/* Pagination dots */}
            <div className={styles.pagination}>
                {slides.map((_, index) => (
                    <button
                        key={index}
                        className={`${styles.paginationDot} ${index === currentSlide ? styles.paginationDotActive : ""}`}
                        onClick={() => goToSlide(index)}
                        aria-label={`Go to slide ${index + 1}`}
                        disabled={isAnimating}></button>
                ))}
            </div>
        </section>
    );
}

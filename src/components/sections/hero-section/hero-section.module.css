.section {
  position: relative;
  width: 100%;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    rgba(230, 247, 240, 0.7) 0%,
    rgba(232, 234, 247, 0.7) 50%,
    rgba(217, 229, 247, 0.7) 100%
  );
  border-radius: 0;
  border-top-left-radius: 5rem;
  font-family: var(--dm);
}

/* Carousel styles */
.carouselContainer {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.carouselTrack {
  display: flex;
  transition: transform 0.7s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
}

.carouselSlide {
  flex: 0 0 100%;
  width: 100%;
  opacity: 0.5;
  transition: opacity 0.7s ease;
}

.activeSlide {
  opacity: 1;
}

.carouselButton {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--card-bg);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  box-shadow: var(--card-shadow);
  transition: all 0.2s;
}

.carouselButton:hover {
  background-color: var(--background-color);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.carouselButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.carouselButtonPrev {
  left: 20px;
}

.carouselButtonNext {
  right: 20px;
}

/* Hero content styles */
.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 4rem 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

@media (min-width: 768px) {
  .container {
    padding: 6rem 3rem;
    flex-direction: row;
  }

  .section {
    min-height: 600px;
  }
}

.leftContent {
  margin-bottom: 3rem;
  width: 100%;
  z-index: 1;
}

@media (min-width: 768px) {
  .leftContent {
    margin-bottom: 0;
    width: 40%;
  }
}

.contentWrapper {
  opacity: 0;
}

.contentWrapper.animate {
  animation: fadeIn 0.5s forwards;
}

.heading {
  font-size: 5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

@media (min-width: 768px) {
  .heading {
    font-size: 5rem;
  }
}

.headingHighlight {
  color: var(--text-primary);
  display: block;
}

.animatedText {
  opacity: 0;
  transform: translateY(20px);
}

/* Animation classes for each element */
.animateTitle {
  animation: fadeInUp 0.8s forwards;
  animation-delay: 0.3s;
  font-size: 76px;
  font-weight: 400;
  line-height: 70px;
  margin-bottom: 2rem;
}

.animateSubtitle {
  animation: fadeInUp 0.8s forwards;
  animation-delay: 0.5s;
  font-weight: 600;
  font-size: 76px;
  line-height: 50px;
}

.animateDescription {
  animation: fadeInUp 0.8s forwards;
  animation-delay: 0.7s;
}

.animateStats {
  animation: fadeInUp 0.8s forwards;
  animation-delay: 0.9s;
}

.animateCta {
  animation: fadeInUp 0.8s forwards;
  animation-delay: 1.1s;
}

.subheading {
  font-size: 2rem;
  margin-bottom: 2rem;
}

.statsContainer {
  margin-bottom: 2.5rem;
}

.statValue {
  font-size: 2.25rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.statLabel {
  color: var(--text-secondary);
}

.ctaButton {
  display: inline-flex;
  align-items: center;
  padding: 1.75rem 2.5rem;
  border-radius: 9999px;
  background: linear-gradient(92deg, rgb(29, 51, 93) 0%, rgb(41, 208, 200) 100%);
  color: white;
  font-weight: 500;
  transition: opacity 0.2s;
}

.ctaButton:hover {
  opacity: 0.9;
}

.ctaIcon {
  margin-left: 0.5rem;
}

/* Right content - Common styles */
.rightContent {
  position: relative;
  width: 100%;
  z-index: 0;
}

@media (min-width: 768px) {
  .rightContent {
    width: 50%;
    right: -10%;
  }
}

/* Grid layout (Slide 1) */
.gridLayout {
  position: relative;
  width: 100%;
  height: 500px;
}

/* Widget base styles */
.widget {
  position: absolute;
  opacity: 0;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  transform: translateY(20px);
}

.widget.animate {
  animation: fadeInUp 0.8s forwards;
}

.widgetImage {
  display: block;
  width: 100%;
  height: auto;
}

/* Widget position classes for grid layout */
.topleft {
  top: 0;
  left: 0;
  width: 45%;
  z-index: 1;
}

.topright {
  top: 0;
  right: 0;
  width: 45%;
  z-index: 1;
}

.bottomleft {
  bottom: 0;
  left: 0;
  width: 45%;
  z-index: 1;
}

.bottomleftoverlay {
  bottom: 2%;
  left: -10%;
  width: 35%;
  z-index: 2;
}

.bottomright {
  bottom: 0;
  right: 0;
  width: 45%;
  z-index: 1;
}

/* Widget type-specific styles */
.weeklychart {
  border-radius: 16px;
  background-color: white;
}

.person {
  object-fit: cover;
  border-radius: 16px;
}

.crypto {
  border-radius: 16px;
  background-color: white;
  height: 89px;
  width: auto;
}

.analysis {
  border-radius: 16px;
  background-color: white;
}

/* Overlay layout (Slide 2) */
.overlayLayout {
  position: relative;
  width: 100%;
  height: 550px;
  border-radius: 24px;
  overflow: hidden;
}

.mainImageContainer {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 24px;
  overflow: hidden;
  opacity: 0;
  background-color: #f0f9ff;
}

.mainImageContainer.animate {
  animation: fadeIn 0.8s forwards;
}

.mainImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 24px;
}

/* Overlay widget styles */
.overlayWidget {
  position: absolute;
  opacity: 0;
  transform: translateY(20px);
  z-index: 2;
  transition: transform 0.3s ease;
}

.overlayWidget.animate {
  animation: fadeInUp 0.8s forwards;
}

.overlayWidget:hover {
  transform: translateY(-5px);
}

/* Overlay widget position classes - adjusted to match the website */
.lefttop {
  top: 15%;
  left: 8%;
  width: 240px;
}

.leftmiddle {
  top: 40%;
  left: 8%;
  width: 240px;
}

.leftbottom {
  top: 65%;
  left: 8%;
  width: 240px;
}

.rightbottom {
  bottom: 15%;
  right: 8%;
  width: 280px;
}

/* Stat card styles */
.statCard {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  backdrop-filter: blur(5px);
  background-color: rgba(255, 255, 255, 0.9);
}

.statCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px -10px rgba(0, 0, 0, 0.15);
}

.pink {
  background-color: rgba(255, 255, 255, 0.9);
}

.purple {
  background-color: rgba(255, 255, 255, 0.9);
}

.statIconContainer {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
}

.pink .statIconContainer {
  background-color: #fce7f3;
}

.purple .statIconContainer {
  background-color: #ede9fe;
}

.statIcon {
  width: 24px;
  height: 24px;
}

.pink .statIcon {
  color: #ec4899;
}

.purple .statIcon {
  color: #8b5cf6;
}

.statContent {
  flex-grow: 1;
}

.statTitle {
  color: #6b7280;
  font-size: 14px;
  margin: 0 0 4px 0;
}

.statAmount {
  color: #111827;
  font-size: 20px;
  font-weight: 700;
  margin: 0;
}

/* Chart image */
.chartImage {
  width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  backdrop-filter: blur(5px);
  background-color: rgba(255, 255, 255, 0.9);
}

.chartImage:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px -10px rgba(0, 0, 0, 0.15);
}

/* Pagination dots */
.pagination {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.paginationDot {
  width: 3rem;
  height: 0.5rem;
  border-radius: 9999px;
  background-color: var(--border-color);
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;
}

.paginationDot:disabled {
  cursor: not-allowed;
}

.paginationDotActive {
  background-color: #1e3a8a;
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .gridLayout {
    height: 450px;
  }

  .overlayLayout {
    height: 500px;
  }

  .topleft,
  .topright,
  .bottomleft,
  .bottomright {
    width: 48%;
  }

  .bottomleftoverlay {
    width: 38%;
    bottom: 15%;
  }

  .lefttop {
    top: 15%;
    left: 5%;
    width: 220px;
  }

  .leftmiddle {
    top: 40%;
    left: 5%;
    width: 220px;
  }

  .leftbottom {
    top: 65%;
    left: 5%;
    width: 220px;
  }

  .rightbottom {
    bottom: 15%;
    right: 5%;
    width: 250px;
  }
}

@media (max-width: 768px) {
  .gridLayout {
    height: 600px;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 2rem;
  }

  .overlayLayout {
    height: 600px;
  }

  .widget {
    position: relative;
    width: 100% !important;
    top: auto;
    left: auto;
    right: auto;
    bottom: auto;
  }

  .bottomleftoverlay {
    position: absolute;
    bottom: 20%;
    left: 5%;
    width: 50% !important;
  }

  .lefttop {
    top: 10%;
    left: 5%;
    width: 200px;
  }

  .leftmiddle {
    top: 30%;
    left: 5%;
    width: 200px;
  }

  .leftbottom {
    top: 50%;
    left: 5%;
    width: 200px;
  }

  .rightbottom {
    bottom: 10%;
    right: 5%;
    width: 220px;
  }
}

@media (max-width: 640px) {
  .gridLayout {
    height: 500px;
  }

  .overlayLayout {
    height: 450px;
  }

  .bottomleftoverlay {
    width: 60% !important;
  }

  .lefttop {
    top: 10%;
    left: 5%;
    width: 180px;
  }

  .leftmiddle {
    top: 30%;
    left: 5%;
    width: 180px;
  }

  .leftbottom {
    top: 50%;
    left: 5%;
    width: 180px;
  }

  .rightbottom {
    bottom: 10%;
    right: 5%;
    width: 180px;
  }

  .personImage {
    width: 300px;
    height: auto;
  }

  .chartImage {
    display: none;
  }

  .bottomleftoverlay {
    bottom: 2%;
    left: 50%;
  }

  .rightContent {
    display: none;
  }

  .animateSubtitle {
    font-size: 3.5rem;
  }

/* Animation classes for each element */
  .animateTitle {
    font-size: 5rem;
  }

}

.personImage {
  border-radius: 5rem;
  border-top-left-radius: 2rem;
  border-bottom-right-radius: 2rem;
}

.chartImage {
  border-radius: 5rem;
  border-top-left-radius: 2rem;
  border-bottom-right-radius: 2rem;
  height: 180px;
  object-fit: cover;
}
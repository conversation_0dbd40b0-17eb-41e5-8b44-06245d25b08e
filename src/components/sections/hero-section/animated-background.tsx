"use client";

import {useEffect, useRef} from "react";
import styles from "./animated-background.module.css";

interface AnimatedBackgroundProps {
    className?: string;
}

export default function AnimatedBackground({className}: AnimatedBackgroundProps) {
    const canvasRef = useRef<HTMLCanvasElement>(null);

    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext("2d");
        if (!ctx) return;

        // Set canvas dimensions to match parent container
        const resizeCanvas = () => {
            const parent = canvas.parentElement;
            if (parent) {
                canvas.width = parent.offsetWidth;
                canvas.height = parent.offsetHeight;
            }
        };

        resizeCanvas();
        window.addEventListener("resize", resizeCanvas);

        // Create particles
        const particles: Particle[] = [];
        const particleCount = 80;
        const connectionDistance = 150;
        const mouseRadius = 150;

        // Mouse position
        const mouse = {
            x: null as number | null,
            y: null as number | null,
        };

        class Particle {
            x: number;
            y: number;
            size: number;
            baseSize: number;
            speedX: number;
            speedY: number;
            color: string;
            opacity: number;
            hasConnection: boolean;

            constructor() {
                this.x = Math.random() * canvas!.width;
                this.y = Math.random() * canvas!.height;
                this.baseSize = Math.random() * 3 + 0.5;
                this.size = this.baseSize;
                this.speedX = (Math.random() - 0.5) * 0.5;
                this.speedY = (Math.random() - 0.5) * 0.5;
                this.color = this.getRandomColor();
                this.opacity = Math.random() * 0.5 + 0.2;
                this.hasConnection = false;
            }

            getRandomColor() {
                const colors = [
                    "rgba(32, 201, 151, 0.8)", // Teal
                    "rgba(59, 130, 246, 0.8)", // Blue
                    "rgba(139, 92, 246, 0.8)", // Purple
                ];
                return colors[Math.floor(Math.random() * colors.length)];
            }

            update() {
                this.x += this.speedX;
                this.y += this.speedY;

                // Bounce off edges
                if (canvas && (this.x > canvas.width || this.x < 0)) {
                    this.speedX = -this.speedX;
                }

                if (canvas && (this.y > canvas.height || this.y < 0)) {
                    this.speedY = -this.speedY;
                }

                // Reset hasConnection flag
                this.hasConnection = false;
            }

            draw() {
                if (ctx) {
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                    ctx.fillStyle = this.color;
                    ctx.globalAlpha = this.opacity;
                    ctx.fill();
                    ctx.globalAlpha = 1;
                }
            }
        }

        // Initialize particles
        for (let i = 0; i < particleCount; i++) {
            particles.push(new Particle());
        }

        // Draw gradient background
        const drawBackground = () => {
            // Create gradient
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, "rgba(230, 247, 240, 0.8)"); // Light teal
            gradient.addColorStop(0.5, "rgba(232, 234, 247, 0.8)"); // Light blue
            gradient.addColorStop(1, "rgba(217, 229, 247, 0.8)"); // Light blue

            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        };

        // Draw grid pattern
        const drawGrid = () => {
            const gridSize = 40;
            ctx.beginPath();
            ctx.strokeStyle = "rgba(32, 201, 151, 0.1)";
            ctx.lineWidth = 1;

            // Vertical lines
            for (let x = 0; x <= canvas.width; x += gridSize) {
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
            }

            // Horizontal lines
            for (let y = 0; y <= canvas.height; y += gridSize) {
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
            }

            ctx.stroke();
        };

        // Draw geometric shapes
        const drawShapes = () => {
            // Draw circles
            ctx.beginPath();
            ctx.arc(canvas.width * 0.2, canvas.height * 0.3, 60, 0, Math.PI * 2);
            ctx.strokeStyle = "rgba(32, 201, 151, 0.2)";
            ctx.lineWidth = 2;
            ctx.stroke();

            // Draw triangle
            ctx.beginPath();
            ctx.moveTo(canvas.width * 0.7, canvas.height * 0.2);
            ctx.lineTo(canvas.width * 0.8, canvas.height * 0.4);
            ctx.lineTo(canvas.width * 0.6, canvas.height * 0.4);
            ctx.closePath();
            ctx.strokeStyle = "rgba(59, 130, 246, 0.2)";
            ctx.lineWidth = 2;
            ctx.stroke();

            // Draw rectangle
            ctx.beginPath();
            ctx.rect(canvas.width * 0.3, canvas.height * 0.7, 80, 50);
            ctx.strokeStyle = "rgba(139, 92, 246, 0.2)";
            ctx.lineWidth = 2;
            ctx.stroke();
        };

        // Draw floating gradient blobs
        const drawGradientBlobs = () => {
            // Blob 1
            const gradient1 = ctx.createRadialGradient(canvas.width * 0.2, canvas.height * 0.2, 0, canvas.width * 0.2, canvas.height * 0.2, 150);
            gradient1.addColorStop(0, "rgba(32, 201, 151, 0.2)");
            gradient1.addColorStop(1, "rgba(32, 201, 151, 0)");

            ctx.beginPath();
            ctx.arc(canvas.width * 0.2, canvas.height * 0.2, 150, 0, Math.PI * 2);
            ctx.fillStyle = gradient1;
            ctx.fill();

            // Blob 2
            const gradient2 = ctx.createRadialGradient(canvas.width * 0.8, canvas.height * 0.8, 0, canvas.width * 0.8, canvas.height * 0.8, 180);
            gradient2.addColorStop(0, "rgba(59, 130, 246, 0.2)");
            gradient2.addColorStop(1, "rgba(59, 130, 246, 0)");

            ctx.beginPath();
            ctx.arc(canvas.width * 0.8, canvas.height * 0.8, 180, 0, Math.PI * 2);
            ctx.fillStyle = gradient2;
            ctx.fill();

            // Blob 3
            const gradient3 = ctx.createRadialGradient(canvas.width * 0.7, canvas.height * 0.3, 0, canvas.width * 0.7, canvas.height * 0.3, 120);
            gradient3.addColorStop(0, "rgba(139, 92, 246, 0.15)");
            gradient3.addColorStop(1, "rgba(139, 92, 246, 0)");

            ctx.beginPath();
            ctx.arc(canvas.width * 0.7, canvas.height * 0.3, 120, 0, Math.PI * 2);
            ctx.fillStyle = gradient3;
            ctx.fill();
        };

        // Connect particles with lines
        const connectParticles = () => {
            for (let i = 0; i < particles.length; i++) {
                for (let j = i; j < particles.length; j++) {
                    const dx = particles[i].x - particles[j].x;
                    const dy = particles[i].y - particles[j].y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < connectionDistance) {
                        // Calculate opacity based on distance
                        const opacity = 1 - distance / connectionDistance;

                        ctx.beginPath();
                        ctx.strokeStyle = `rgba(32, 201, 151, ${opacity * 0.2})`;
                        ctx.lineWidth = 1;
                        ctx.moveTo(particles[i].x, particles[i].y);
                        ctx.lineTo(particles[j].x, particles[j].y);
                        ctx.stroke();

                        // Mark particles as having a connection
                        particles[i].hasConnection = true;
                        particles[j].hasConnection = true;
                    }
                }
            }
        };

        // Draw financial chart patterns
        const drawChartPatterns = () => {
            // Draw a simple line chart pattern
            ctx.beginPath();
            ctx.moveTo(canvas.width * 0.1, canvas.height * 0.6);
            ctx.lineTo(canvas.width * 0.15, canvas.height * 0.55);
            ctx.lineTo(canvas.width * 0.2, canvas.height * 0.65);
            ctx.lineTo(canvas.width * 0.25, canvas.height * 0.58);
            ctx.lineTo(canvas.width * 0.3, canvas.height * 0.62);
            ctx.strokeStyle = "rgba(32, 201, 151, 0.3)";
            ctx.lineWidth = 2;
            ctx.stroke();

            // Draw a candlestick pattern
            const drawCandle = (x: number, y: number, width: number, height: number, color: string) => {
                ctx.beginPath();
                ctx.rect(x, y, width, height);
                ctx.fillStyle = color;
                ctx.fill();
                ctx.beginPath();
                ctx.moveTo(x + width / 2, y - 10);
                ctx.lineTo(x + width / 2, y + height + 10);
                ctx.strokeStyle = color;
                ctx.lineWidth = 1;
                ctx.stroke();
            };

            drawCandle(canvas.width * 0.75, canvas.height * 0.65, 8, 15, "rgba(32, 201, 151, 0.3)");
            drawCandle(canvas.width * 0.78, canvas.height * 0.68, 8, 10, "rgba(236, 72, 153, 0.3)");
            drawCandle(canvas.width * 0.81, canvas.height * 0.63, 8, 12, "rgba(32, 201, 151, 0.3)");
            drawCandle(canvas.width * 0.84, canvas.height * 0.67, 8, 14, "rgba(236, 72, 153, 0.3)");
            drawCandle(canvas.width * 0.87, canvas.height * 0.62, 8, 16, "rgba(32, 201, 151, 0.3)");
        };

        // Animation loop
        const animate = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            drawBackground();
            drawGrid();
            drawGradientBlobs();
            drawShapes();
            drawChartPatterns();

            // Update and draw particles
            particles.forEach((particle) => {
                particle.update();
                particle.draw();
            });

            connectParticles();

            requestAnimationFrame(animate);
        };

        animate();

        // Handle mouse movement
        canvas.addEventListener("mousemove", (e) => {
            const rect = canvas.getBoundingClientRect();
            mouse.x = e.clientX - rect.left;
            mouse.y = e.clientY - rect.top;
        });

        canvas.addEventListener("mouseleave", () => {
            mouse.x = null;
            mouse.y = null;
        });

        return () => {
            window.removeEventListener("resize", resizeCanvas);
            canvas.removeEventListener("mousemove", () => {});
            canvas.removeEventListener("mouseleave", () => {});
        };
    }, []);

    return <canvas ref={canvasRef} className={`${styles.canvas} ${className || ""}`} aria-hidden="true" />;
}

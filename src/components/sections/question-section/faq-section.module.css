.section {
  width: 100%;
  padding: 5rem 0;
  /* background: linear-gradient(to right, #f0f9ff, #e0f7fa); */
  overflow: hidden;
  position: relative;
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.header {
  text-align: left;
  margin-bottom: 3rem;
}

.title {
  font-size: 4.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: rgb(var(--color-sf-black));
}

.subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* Mobile view with tabs */
.mobileView {
  display: block;
}

.tabsContainer {
  margin-bottom: 1.5rem;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.tabs {
  display: flex;
  gap: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.tab {
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  color: var(--text-secondary);
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.2s;
}

.activeTab {
  color: #20c997;
  border-bottom-color: #20c997;
}

.tabContent {
  min-height: 300px;
}

/* Desktop view with columns */
.desktopView {
  /* display: none; */
}

.faqColumns {
  display: grid;
  /* grid-template-columns: 1fr 1fr; */
  gap: 2rem;
}

.faqColumn {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.columnTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.faqAccordion {
  margin-bottom: 2rem;
}

/* Contact CTA */
.contactCta {
  margin-top: 4rem;
  background: linear-gradient(to right, #1d335c, #20c997);
  border-radius: 1rem;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  color: white;
  position: relative;
  overflow: hidden;
}

.ctaContent {
  text-align: center;
  z-index: 1;
}

.ctaTitle {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.ctaText {
  margin-bottom: 1.5rem;
  max-width: 500px;
}

.ctaButton {
  padding: 0.75rem 1.5rem;
  background-color: white;
  color: #1d335c;
  font-weight: 600;
  border: none;
  border-radius: 9999px;
  cursor: pointer;
  transition: all 0.2s;
}

.ctaButton:hover {
  background-color: #f0f9ff;
  transform: translateY(-2px);
}

.ctaImage {
  display: none;
}

.supportImage {
  border-radius: 0.5rem;
  object-fit: cover;
}

/* Responsive adjustments */
@media (min-width: 768px) {
  .mobileView {
    display: none;
  }

  .desktopView {
    display: block;
  }

  .contactCta {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
    padding: 3rem;
  }

  .ctaContent {
    text-align: left;
    max-width: 60%;
  }

  .ctaImage {
    display: block;
  }
}

@media (max-width: 767px) {
  .title {
    font-size: 2rem;
  }

  .faqColumns {
    grid-template-columns: 1fr;
  }
}

"use client";

import Accordion from "@/components/UI/accordion";
import {useTranslation} from "react-i18next";
import styles from "./faq-section.module.css";

const texts = {
    title: "landingpage.faq.title",
    faqItems: [
        {
            id: "faq-1",
            title: "landingpage.faq.faq_items.faq_items_1.title",
            content: "landingpage.faq.faq_items.faq_items_1.content",
        },
        {
            id: "faq-2",
            title: "landingpage.faq.faq_items.faq_items_2.title",
            content: "landingpage.faq.faq_items.faq_items_2.content",
        },
        {
            id: "faq-3",
            title: "landingpage.faq.faq_items.faq_items_3.title",
            content: "landingpage.faq.faq_items.faq_items_3.content",
        },
        {
            id: "faq-4",
            title: "landingpage.faq.faq_items.faq_items_4.title",
            content: "landingpage.faq.faq_items.faq_items_4.content",
        },
        {
            id: "faq-5",
            title: "landingpage.faq.faq_items.faq_items_5.title",
            content: "landingpage.faq.faq_items.faq_items_5.content",
        },
        {
            id: "faq-6",
            title: "landingpage.faq.faq_items.faq_items_6.title",
            content: "landingpage.faq.faq_items.faq_items_6.content",
        },
        {
            id: "faq-7",
            title: "landingpage.faq.faq_items.faq_items_7.title",
            content: "landingpage.faq.faq_items.faq_items_7.content",
        },
        {
            id: "faq-8",
            title: "landingpage.faq.faq_items.faq_items_8.title",
            content: "landingpage.faq.faq_items.faq_items_8.content",
        },
        {
            id: "faq-9",
            title: "landingpage.faq.faq_items.faq_items_9.title",
            content: "landingpage.faq.faq_items.faq_items_9.content",
        },
        {
            id: "faq-10",
            title: "landingpage.faq.faq_items.faq_items_10.title",
            content: "landingpage.faq.faq_items.faq_items_10.content",
        },
        {
            id: "faq-11",
            title: "landingpage.faq.faq_items.faq_items_11.title",
            content: "landingpage.faq.faq_items.faq_items_11.content",
        },
    ],
};

export default function FAQSection() {
    const {t} = useTranslation();

    return (
        <section className={styles.section} id="faq">
            <div className={styles.container}>
                <div className={styles.header}>
                    <h2 className={styles.title}>{t(texts.title)}</h2>
                </div>

                {/* Desktop view - two columns */}
                <div className={styles.desktopView}>
                    <div className={styles.faqColumns}>
                        <div className={styles.faqColumn}>
                            <Accordion items={texts.faqItems} className={styles.faqAccordion} variant="colored" headerColor="#29D0C8" contentColor="#E6ECF5" headerStyle={{padding: "3rem 2rem"}} />
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}

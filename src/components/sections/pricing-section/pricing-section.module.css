.section {
  position: relative;
  width: 100%;
  padding: 5rem 0;
  background: linear-gradient(104deg, #F1FFFE 0%, #90BFF0 100%);
  border-top-left-radius: 5rem;
  border-bottom-right-radius: 5rem;
  font-family: var(--poppins);  
  overflow: hidden;
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: 42px;
  font-weight: 300;
  margin-bottom: 1rem;
  color: #000000;
}

.highlight {
  color: #20c997;
  background: linear-gradient(90deg, #006FF9 0%, #00DCC7 51%, #006FF9 100%) 0/200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 42px;
  font-weight: 600;
}

.subtitle {
  max-width: 600px;
  margin: 0 auto;
  color: var(--text-secondary);
  font-size: 1.825rem;
  line-height: 1.6;
}

/* Billing toggle */
.billingToggle {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 3rem;
  gap: 1rem;
  background: rgb(var(--color-sf-white));
  width: fit-content;
  padding: 1.8rem 2rem;
  border-radius: 3.5rem;
  position: relative;
}

.toggle {
  position: relative;
  width: 60px;
  height: 30px;
  background-color: #1D335D;
  border-radius: 30px;
  padding: 4px;
  transition: background-color 0.3s;
  cursor: pointer;
  border: none;
}

.toggleActive {
  background-color: #1D335D;
}

.toggleHandle {
  position: absolute;
  top: 3px;
  left: 3px;
  width: 24px;
  height: 24px;
  background-color: white;
  border-radius: 50%;
  transition: transform 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggleActive .toggleHandle {
  transform: translateX(30px);
}

.activeLabel {
  font-weight: 600;
  color: #1d335c;
}

.inactiveLabel {
  color: var(--text-secondary);
}

.discount {
  background: linear-gradient(92deg, rgb(29, 51, 93) 0%, rgb(41, 208, 200) 100%);
  color: white;
  font-size: 1rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  position: absolute;
  right: 36px;
  bottom: 2px;
}

/* Pricing cards */
.pricingCards {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-bottom: 3rem;
}

@media (min-width: 768px) {
  .pricingCards {
    flex-direction: row;
    align-items: stretch;
  }
}

.card {
  flex: 1;
  background-color: rgba(var(--color-sf-white));
  padding: 3rem 3.5rem;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  border-top-left-radius: 3rem;
  border-bottom-right-radius: 3rem;
}

.planName {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.price {
  display: flex;
  align-items: baseline;
  margin-bottom: 1rem;
  font-size: 5rem;
  color: #000000;
  font-weight: 200;
}

.currency {
  font-size: 5rem;
  font-weight: 600;
}

.amount {
  font-size: 6rem;
  font-weight: 700;
  line-height: 1;
  margin-right: 0.25rem;
}

.period {
  color: var(--text-secondary);
  font-size: 1rem;
}

.description {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.divider {
  height: 1px;
  background-color: var(--border-color);
  margin: 1.5rem 0;
}

.featuresList {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
  flex-grow: 1;
}

.featureItem {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

.checkIcon {
  color: #10b981;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

.subscribeButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.75rem 1.5rem;
  border-radius: 2rem;
  background: linear-gradient(to right, #1d335c, #20c997);
  color: white;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: opacity 0.2s;
}

.subscribeButton:hover {
  opacity: 0.9;
}

/* Pagination dots */
.pagination {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin: 2rem 0;
}

.paginationDot {
  width: 2.5rem;
  height: 0.5rem;
  border-radius: 9999px;
  background-color: rgba(29, 51, 92, 0.2);
  border: none;
  cursor: pointer;
}

.paginationDotActive {
  background-color: #20c997;
}

/* Comparison table */
.comparisonTable {
  background-color: var(--card-bg);
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: var(--card-shadow);
  margin-bottom: 3rem;
}

.tableHeader {
  display: flex;
  background-color: #1d335c;
  color: white;
  font-weight: 600;
}

.tableRow {
  display: flex;
  border-bottom: 1px solid rgba(var(--color-sf-black), 0.1);
  background-color: rgb(var(--color-sf-white));
  padding: 1.1rem 0;

}

.tableRow:last-child {
  border-bottom: none;
}

.featureColumn {
  flex: 2;
  padding: 1rem;
  display: flex;
  align-items: center;
}

.planColumn {
  flex: 1;
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tableCheckIcon {
  background-color: #10b981;
  border-radius: 50%;
  color: #FFF;
  font-weight: bold;
  padding: 0.3rem;
}

.tableCrossIcon {
  color: #FFF;
  background-color: #ef4444;
  border-radius: 50%;
  padding: 0.3rem;
}

/* Bottom CTA */
.bottomCta {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 2rem;
}

@media (min-width: 768px) {
  .bottomCta {
    flex-direction: row;
    justify-content: right;
    gap: 10rem;
    margin-right: 60px;
  }
}

.taureaButton {
  padding: 0.75rem 1.5rem;
  border-radius: 9999px;
  background: #1d335c;
  color: white;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.taureaButton:hover {
  background-color: #152a4d;
}

.oursButton {
  padding: 0.75rem 1.5rem;
  border-radius: 9999px;
  background: #1d335c;
  color: white;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.oursButton:hover {
  background-color: #1db386;
}

"use client";

import {price, Product} from "@/types/api/stripe";
import {CURRENCY_SYMBOL} from "@/types/utils/constants";
import {ArrowRight, Check, X} from "lucide-react";
import {useRouter} from "next/navigation";
import {useEffect, useState} from "react";
import {useTranslation} from "react-i18next";
import styles from "./pricing-section.module.css";

type BillingCycle = "month" | "year";

const plan_key = (name: string) => {
    if (name === "tiba_touro" || name === "tiba_bull" || name === "tiba_taureau") return "tiba_taureau";
    return "tiba_ours";
};

export default function PricingSection() {
    const {
        t,
        i18n: {language: lang},
    } = useTranslation();
    const [billingCycle, setBillingCycle] = useState<BillingCycle>("month");
    const [product, setProduct] = useState<Product | null>(null);
    const [features, setFeatures] = useState<string[]>([]);
    const [planName, setPlanName] = useState<string[]>([]);
    const router = useRouter();

    const handleToggle = () => {
        setBillingCycle(billingCycle === "month" ? "year" : "month");
    };

    useEffect(() => {
        fetch(`/api/product/${lang}`)
            .then((res) => res.json())
            .then((data) => {
                const obj: any = {};
                const plans: string[] = [];
                const features: string[] = [];

                data?.map((item: Product) => {
                    const keys = Object.keys(item);

                    const [key] = keys;
                    plans.push(key);

                    obj[key] = item[key];
                });

                const dataItems = data[0];
                const dataItemsKey = Object.keys(dataItems)[0] || "";
                const featuresObj = dataItems[dataItemsKey].features;

                featuresObj.forEach((element) => {
                    const [key] = Object.keys(element);
                    features.push(key);
                });

                setPlanName(plans);
                setFeatures(features);
                setProduct(obj);
            })
            .catch((err) => {
                // Optionally handle error
                setProduct(null);
            });
    }, [lang]);

    function selectPlan(name: string): void {
        const selected = product && product[name];

        const {prices} = selected;

        const plan = prices.find((item) => item.interval === billingCycle);

        router.push(`/subscribe/${plan.id_stripe}`);
    }

    return (
        <section className={`${styles.section} mt-14`}>
            <div className={styles.container}>
                <div className={styles.header}>
                    <h2 className={styles.title}>
                        {t("landingpage.prices.title_thin")} <span className={styles.highlight}>{t("landingpage.prices.title_bold")}</span>
                    </h2>
                    <p className={styles.subtitle}>{t("landingpage.prices.description")}</p>
                </div>

                {/* Billing toggle */}
                <div className="w-full flex items-center justify-center">
                    <div className={styles.billingToggle}>
                        <span className={billingCycle === "month" ? styles.activeLabel : styles.inactiveLabel}>{t("landingpage.prices.month_label")}</span>

                        <button
                            className={`${styles.toggle} ${billingCycle === "year" ? styles.toggleActive : ""}`}
                            onClick={handleToggle}
                            aria-label={`Switch to ${billingCycle === "month" ? "year" : "monthly"} billing`}>
                            <span className={styles.toggleHandle}></span>
                        </button>

                        <span className={billingCycle === "year" ? styles.activeLabel : styles.inactiveLabel}>{t("landingpage.prices.year_label")}</span>

                        {billingCycle === "year" && <span className={styles.discount}>{t("landingpage.prices.offer")}</span>}
                    </div>
                </div>

                {/* Pricing cards */}
                <div className={styles.pricingCards}>
                    {product &&
                        Object.keys(product).map((productName, i) => {
                            const current_price_item: price = product[productName].prices.find((price) => price.interval === billingCycle);
                            const {features} = product[productName] || [];

                            const name = plan_key(productName.replace(/\s/g, "_").toLowerCase());

                            return (
                                <div key={i} className={styles.card}>
                                    <h3 className={`${styles.planName} mt-10`}>{productName}</h3>
                                    <div className={styles.price}>
                                        <span className={styles.currency}>{CURRENCY_SYMBOL[current_price_item.currency.toUpperCase()]}</span>
                                        <span className={styles.amount}>{current_price_item.amount.toFixed(2).replace(".", ",")}</span>
                                        <span className={styles.period}>/{billingCycle === "month" ? t("landingpage.prices.month") : t("landingpage.prices.year")}</span>
                                    </div>
                                    <p className={`mt-0 ${styles.description}`}>{t(`landingpage.prices.${name}_description`)}</p>

                                    <div className={styles.divider}></div>

                                    <ul className={styles.featuresList}>
                                        {features.map((feature) =>
                                            Object.keys(feature).map(
                                                (item) =>
                                                    feature[item] === true && (
                                                        <li key={`taureau-${item}`} className={styles.featureItem}>
                                                            <Check className={styles.checkIcon} size={18} />
                                                            <span>{item}</span>
                                                        </li>
                                                    ),
                                            ),
                                        )}
                                    </ul>

                                    <button onClick={() => selectPlan(productName)} className={`mt-10 ${styles.subscribeButton}`}>
                                        {t("landingpage.prices.sign_button")} <ArrowRight size={16} />
                                    </button>
                                </div>
                            );
                        })}
                </div>

                {/* Pagination dots */}
                <div className={styles.pagination}>
                    <button className={`${styles.paginationDot} ${styles.paginationDotActive}`}></button>
                    <button className={styles.paginationDot}></button>
                    <button className={styles.paginationDot}></button>
                </div>

                {/* Comparison table */}
                <div className={styles.comparisonTable}>
                    <div className={styles.tableHeader}>
                        <div className={styles.featureColumn}>Features</div>
                        {planName.map((feature) => (
                            <div key={feature} className={styles.planColumn}>
                                {feature}
                            </div>
                        ))}
                    </div>
                    {product &&
                        features?.map((feature, i) => {
                            const first_plan_features = product[planName[0]].features;
                            const second_plan_features = product[planName[1]].features;

                            const is_first_plan_true = first_plan_features.find((item) => item[feature] === true);
                            const is_second_plan_true = second_plan_features.find((item) => item[feature] === true);

                            return (
                                <div key={i} className={styles.tableRow}>
                                    <div className={styles.featureColumn}>{feature}</div>
                                    <div className={styles.planColumn}>
                                        {is_first_plan_true ? <Check className={styles.tableCheckIcon} size={30} strokeWidth={5} /> : <X className={styles.tableCrossIcon} size={30} strokeWidth={5} />}
                                    </div>
                                    <div className={styles.planColumn}>
                                        {is_second_plan_true ? (
                                            <Check className={styles.tableCheckIcon} size={30} strokeWidth={5} />
                                        ) : (
                                            <X className={styles.tableCrossIcon} size={30} strokeWidth={5} />
                                        )}
                                    </div>
                                </div>
                            );
                        })}
                </div>

                {/* Bottom CTA buttons */}
                <div className={styles.bottomCta}>
                    {planName.map((name) => (
                        <button className={styles.taureaButton} key={name} onClick={() => selectPlan(name)}>
                            {t("landingpage.prices.sign_button")} {name}
                        </button>
                    ))}
                </div>
            </div>
        </section>
    );
}

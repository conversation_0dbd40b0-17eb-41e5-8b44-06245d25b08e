.section {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.topBackground {
  background: linear-gradient(to right, #20c997, #0fbfb0);
  padding: 5rem 0;
  color: white;
}

.bottomBackground {
  /* background-color: white; */
  padding: 5rem 0;
  position: relative;
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1.5rem;
  font-family: var(--dm);
}

.aboutCtaTitle {
    background: linear-gradient(92deg, rgb(29, 51, 93) 0%, rgb(41, 208, 200) 100%);
    padding: 1rem 2rem;
    border-radius: 20px;
    color: var(--colors-white);
    font-weight: bold;
    text-transform: uppercase;
    box-sizing: border-box;
    width: fit-content;
}

.title {
    font-size: 43px;
    line-height: 1.125em;
    font-weight: 700;
}

.title span {
    font-weight: 200;
    font-size: 43px;
}

.description {
    font-weight: 400;
}

@media (min-width: 768px) {
  .container {
    padding: 0 3rem;
  }
}

/* Top section styles */
.topContent {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.mainHeading {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

@media (min-width: 768px) {
  .mainHeading {
    font-size: 3.5rem;
  }
}

.mainDescription {
  font-size: 1.25rem;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
}

/* Bottom section styles */
.bottomContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rem;
}

@media (min-width: 1024px) {
  .bottomContent {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
}

/* Text content styles */
.textContent {
  max-width: 100%;
}

@media (min-width: 1024px) {
  .textContent {
    max-width: 45%;
  }
}

.featureHeading {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #1a1a1a;
}

.featureDescription {
  font-size: 1.125rem;
  color: #666;
  margin-bottom: 2.5rem;
  max-width: 500px;
}

.featuresList {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2.5rem;
}

.featureIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  
  color: var(--colors-white);
  font-size: 50px;

  background: linear-gradient(92deg, rgb(29, 51, 93) 0%, rgb(41, 208, 200) 100%);
  
  width: 60px;
  height: 60px;
  border-radius: 50%;
  flex-shrink: 0;
}

.featureItemTitle {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #1a1a1a;
}

.featureItemDescription {
  font-size: 0.95rem;
  color: #666;
  margin: 0;
}

.ctaButton {
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(to right, #20c997, #0fbfb0);
  color: white;
  font-weight: 500;
  border: none;
  border-radius: 9999px;
  cursor: pointer;
  transition: opacity 0.2s;
}

.ctaButton:hover {
  opacity: 0.9;
}

.ctaIcon {
  margin-left: 0.5rem;
}

/* Phones container styles */
.phonesContainer {
  display: flex;
  justify-content: center;
  left: -10%;
  width: 100%;
  perspective: 1000px;
  position: relative;

}

@media (min-width: 1024px) {
  .phonesContainer {
    width: 45%;
  }
}

.phone1 {
  transform: rotate(0deg);
  box-shadow: 0 20px 30px rgba(0, 0, 0, 0.15);
  border-radius: 36px;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.phone1:hover {
  transform: rotate(-5deg) translateY(-10px);
}

.phone2 {
  transform: rotate(-20deg);
  /* box-shadow: 0 20px 30px rgba(0, 0, 0, 0.15); */
  left: 28%;
  bottom: -40%;
  position: absolute;
  border-radius: 36px;
  overflow: hidden;
  transition: transform 0.3s ease;
  width: 450px;
  height: 700px;
  
}

.phone2 img {
    height: 800px;
  width: auto;
}

.phone2:hover {
  transform: rotate(5deg) translateY(-10px);
}

.phoneImage {
  display: block;
   width: auto;
  height: auto; 
   max-width: 100%; 
  object-fit: contain;
}

@media (max-width: 640px) {
  .phonesContainer {
    flex-direction: column;
    align-items: center;
    left: 0;
  }

  .phone1 {
    max-width: 220px;
  }

  .phone2 {
    display: none;   
  }
}


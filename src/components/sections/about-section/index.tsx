"use client";

import {<PERSON><PERSON><PERSON><PERSON>, Zap} from "lucide-react";
import Image from "next/image";
import {useTranslation} from "react-i18next";
import styles from "./about-section.module.css";

export default function AboutSection() {
    const {t} = useTranslation();

    return (
        <section className={styles.section} id="about">
            {/* White background section with phones */}
            <div className={styles.bottomBackground}>
                <div className={styles.container}>
                    <div className={styles.bottomContent}>
                        {/* Left side text */}
                        <div className="w-100">
                            <div className={styles.aboutCtaTitle}>{t("landingpage.about.call_title")}</div>
                            <h3 className={`${styles.title} mt-10`}>
                                {t("landingpage.about.title")} <span>{t("landingpage.about.subtitle")}</span>
                            </h3>

                            <p className={`${styles.description} mt-10`}>{t("landingpage.about.description")}</p>
                            <div className="flex grid-cols-2 mt-[40px]">
                                <div className="w-[40%] flex flex-col ">
                                    <div className={styles.featureIcon}>
                                        <Zap size={32} strokeWidth={1} />
                                    </div>
                                    <h4 className={`${styles.featureNumber} mt-10`}>15%</h4>
                                    <p>{t("landingpage.about.profitability")}</p>
                                </div>
                                <div className="w-[40%] flex flex-col ">
                                    <div className={styles.featureIcon}>
                                        <ShieldCheck size={32} strokeWidth={1} />
                                    </div>
                                    <h4 className={`${styles.featureNumber} mt-10`}>100%</h4>
                                    <p>{t("landingpage.about.safety")}</p>
                                </div>
                            </div>
                        </div>

                        {/* Right side phones */}
                        <div className={styles.phonesContainer}>
                            <div className={styles.phone1}>
                                <Image src="/imgs/about-section/iphone-1.png" alt="Tiba Invest app showing Apple stock" width={280} height={300} className={styles.phoneImage} />
                            </div>

                            <div className={styles.phone2}>
                                <Image src="/imgs/about-section/iphone-2.png" alt="Tiba Invest app showing stock chart" width={580} height={700} className={styles.phoneImage} />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}

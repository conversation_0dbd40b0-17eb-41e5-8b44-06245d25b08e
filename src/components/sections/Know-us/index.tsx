"use client";
import Accordion from "@/components/UI/accordion";
import Image from "next/image";
import {useTranslation} from "react-i18next";
import styles from "./know-us.module.css";
const image = "/imgs/know-us/know-us.jpg";

const texts = {
    left_side: {
        image_alt: "landingpage.know_us.image_alt",
    },
    right_side: {
        subtitle: "landingpage.know_us.subtitle",
        bold_title: "landingpage.know_us.bold_title",
        thin_title: "landingpage.know_us.thin_title",
        items: [
            {
                id: "analysis",
                title: "landingpage.know_us.items.analysis.title",
                content: "landingpage.know_us.items.analysis.text",
            },
            {
                id: "wallet",
                title: "landingpage.know_us.items.wallet.title",
                content: "landingpage.know_us.items.wallet.text",
            },
            {
                id: "benefits",
                title: "landingpage.know_us.items.benefits.title",
                content: "landingpage.know_us.items.benefits.text",
            },
            {
                id: "comparison",
                title: "landingpage.know_us.items.comparison.title",
                content: "landingpage.know_us.items.comparison.text",
            },
        ],
    },
};

export default function KnowUs() {
    const {t} = useTranslation();

    return (
        <section className={`${styles.container} w-full grid grid-cols-1 sm:grid-cols-2 gap-2 mt-10`}>
            <div className={styles.leftSide}>
                <Image src={"/imgs/know-us/know-us.jpg"} width={500} height={300} alt={t(texts.left_side.image_alt)} />
            </div>
            <div className={styles.rightSide}>
                <div className={`${styles.subtitleContainer} text-center sm:text-left mb-5 w-full`}>
                    <span className={styles.subtitle}>{t(texts.right_side.subtitle)}</span>
                </div>
                <h3 className={styles.title}>
                    {t(texts.right_side.bold_title)} <span>{t(texts.right_side.thin_title)}</span>
                </h3>
                <Accordion items={texts.right_side.items} className="mt-10" />
            </div>
        </section>
    );
}

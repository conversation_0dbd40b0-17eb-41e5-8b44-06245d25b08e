.container {
    background: linear-gradient(104deg, #F1FFFE 0%, #90BFF0 100%);
    border-top-right-radius: 5rem;
    border-bottom-left-radius: 5rem;
    font-family: var(--poppins);
    width: 100%;
}

.leftSide {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 80px 0;
}

.leftSide img {
    border-top-right-radius: 2rem;
    border-bottom-left-radius: 2rem;
    border-top-left-radius: 7rem;
    border-bottom-right-radius: 7rem;
    width: 90%;
    height: auto;
}

.rightSide {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
}

.subtitleContainer {
    
}

.subtitle {
    color: #1D335D;
    font-family: var(--dm);
    font-size: 16px;
    font-weight: 600;
    text-transform: capitalize;
    line-height: 1.25em;
    background-color: transparent;
    background-image: linear-gradient(180deg, #FFFFFF00 50%, #D1DAE9 0%);
}

.title {
    font-size: 42px;
    box-sizing: border-box;
    font-weight: 600;
    line-height: 1.125em;
}

.title span {
    font-weight: 200;
    font-size: 42px;
}

@media screen and (max-width: 640px) {
    .leftSide {
        padding: 30px 20px;
    }
    .rightSide {
        padding: 30px 20px;
    }
}
import {CashFlow} from "../../entities/consolidated_data/CashFlow";
import {Document_type_year_or_quarter} from "../../entities/consolidated_data/DocumentTypeYearorQuarterly";
import {snakeCase, difference} from "lodash";
import {consoleLog} from "@/lib/utils";
import {LogLevel} from "@/utils/types/logs/log";
import {addLogJobExecution} from "@/lib/agenda";

export class CashFlowRepository {
    private saved_yearly: string[];
    private saved_quarterly: string[];
    private cashFlowYearly: CashFlow[];
    private cashFlowQuarterly: CashFlow[];
    private ticker_internal_id: number;
    private files_dates_yearly: string[];
    private files_dates_quarterly: string[];

    constructor(ticker_internal_id: number) {
        this.ticker_internal_id = ticker_internal_id;
    }

    async getSavedYearlyCashFlow(): Promise<string[]> {
        const saved_yearly = await CashFlow.findAll({
            where: {
                ticker_internal_id: this.ticker_internal_id,
                document_type_year_or_quarter: Document_type_year_or_quarter.y,
            },
            attributes: ["document_date"],
        });
        const data: string[] = [];
        for (let i = 0; i < saved_yearly.length; i++) {
            data.push(saved_yearly[i].document_date);
        }
        this.saved_yearly = data;
        return this.saved_yearly;
    }

    async getSavedQuarterlyCashFlow(): Promise<string[]> {
        const saved_quarterly = await CashFlow.findAll({
            where: {
                ticker_internal_id: this.ticker_internal_id,
                document_type_year_or_quarter: Document_type_year_or_quarter.q,
            },
            attributes: ["document_date"],
        });
        const data: string[] = [];
        for (let i = 0; i < saved_quarterly.length; i++) {
            data.push(saved_quarterly[i].document_date);
        }
        this.saved_quarterly = data;
        return this.saved_quarterly;
    }

    async getYearlyCashFlow(cashFlowYearly: any): Promise<CashFlow[]> {
        try {
            const ticker_internal_id = this.ticker_internal_id;
            const data: CashFlow[] = [];
            const keys = Object.keys(cashFlowYearly);
            this.files_dates_yearly = keys;

            for (let i = 0; i < keys.length; i++) {
                const current_key = keys[i];
                const cashFlow = cashFlowYearly[current_key];
                const snakeCasedObject = Object.keys(cashFlow).reduce(
                    (result, key) => ({
                        ...result,
                        ticker_internal_id,
                        document_date: current_key,
                        document_type_year_or_quarter: Document_type_year_or_quarter.y,
                        [snakeCase(key)]: cashFlow[key],
                    }),
                    {
                        ticker_internal_id,
                        document_date: current_key,
                        document_type_year_or_quarter: Document_type_year_or_quarter.y,
                    },
                );

                const cashFlowData = new CashFlow({...snakeCasedObject});
                data.push(cashFlowData);
            }

            this.cashFlowYearly = data;
            return this.cashFlowYearly;
        } catch (error: any) {
            consoleLog(`Error when try to get CashFlow Yearly ${error}`, this.getYearlyCashFlow.name, LogLevel.ERROR);
            addLogJobExecution(
                LogLevel.ERROR,
                this.getYearlyCashFlow.name,
                "Error when try to get CashFlow Yearly",
                {error: error instanceof Error ? error.message : String(error)},
                this.ticker_internal_id,
            );
            throw Error("Error when try to get CashFlow Yearly " + error);
        }
    }

    async getQuarterlyCashFlow(cashFlowQuarterly: any): Promise<CashFlow[]> {
        try {
            const ticker_internal_id = this.ticker_internal_id;
            const data: CashFlow[] = [];
            const keys = Object.keys(cashFlowQuarterly);
            this.files_dates_quarterly = keys;

            for (let i = 0; i < keys.length; i++) {
                const current_key = keys[i];
                const cashFlow = cashFlowQuarterly[current_key];
                const snakeCasedObject = Object.keys(cashFlow).reduce(
                    (result, key) => ({
                        ...result,
                        ticker_internal_id,
                        document_date: current_key,
                        document_type_year_or_quarter: Document_type_year_or_quarter.q,
                        [snakeCase(key)]: cashFlow[key],
                    }),
                    {
                        ticker_internal_id,
                        document_date: current_key,
                        document_type_year_or_quarter: Document_type_year_or_quarter.q,
                    },
                );
                const cashFlowData = new CashFlow({...snakeCasedObject});
                data.push(cashFlowData);
            }

            this.cashFlowQuarterly = data;
            return this.cashFlowQuarterly;
        } catch (error: any) {
            consoleLog(`Error when try to get CashFlow Quarterly ${error}`, this.getQuarterlyCashFlow.name, LogLevel.ERROR);
            addLogJobExecution(
                LogLevel.ERROR,
                this.getQuarterlyCashFlow.name,
                "Error when try to get CashFlow Quarterly",
                {error: error instanceof Error ? error.message : String(error)},
                this.ticker_internal_id,
            );
            throw Error("Error when try to get CashFlow Quarterly " + error);
        }
    }

    async saveYearlyCashFlow() {
        try {
            let toSave: CashFlow[] = [];

            if (this.saved_yearly.length === 0 && this.cashFlowYearly.length > 0) {
                toSave = this.cashFlowYearly;
                toSave[0].document_current = 1;
            } else {
                const delta = difference(this.files_dates_yearly, this.saved_yearly);
                const biggest_date = this.files_dates_yearly[0];

                if (delta.length > 0 && this.cashFlowYearly.length > 0) {
                    for (let i = 0; i < this.cashFlowYearly.length; i++) {
                        if (delta.includes(this.cashFlowYearly[i].document_date)) {
                            if (this.cashFlowYearly[i].document_date === biggest_date) {
                                this.cashFlowYearly[i].document_current = 1;
                            }
                            toSave.push(this.cashFlowYearly[i]);
                        }
                    }
                }

                const cash = await CashFlow.findOne({
                    where: {
                        document_current: 1,
                        ticker_internal_id: this.ticker_internal_id,
                        document_type_year_or_quarter: Document_type_year_or_quarter.y,
                    },
                });

                if (cash && cash?.document_date < biggest_date) {
                    await cash.update({document_current: 0});
                    await cash.save();
                }
            }

            if (toSave.length > 0) {
                await CashFlow.bulkCreate(toSave);
                consoleLog("CashFlow yearly saved", this.saveYearlyCashFlow.name, LogLevel.DEBUG);
            }
        } catch (error: any) {
            consoleLog(`Error when try to save CashFlow yearly ${error}`, this.saveYearlyCashFlow.name, LogLevel.ERROR);
            addLogJobExecution(
                LogLevel.ERROR,
                this.saveYearlyCashFlow.name,
                "Error when try to save CashFlow yearly",
                {error: error instanceof Error ? error.message : String(error)},
                this.ticker_internal_id,
            );
            throw Error("Error when try to save CashFlow yearly " + error);
        }
    }

    async saveQuarterlyCashFlow() {
        try {
            let toSave: CashFlow[] = [];

            if (this.saved_quarterly.length === 0 && this.cashFlowQuarterly.length > 0) {
                toSave = this.cashFlowQuarterly;
                toSave[0].document_current = 1;
            } else {
                const delta = difference(this.files_dates_quarterly, this.saved_quarterly);

                if (delta.length > 0 && this.cashFlowQuarterly.length > 0) {
                    const biggest_date = this.files_dates_quarterly[0];

                    for (let i = 0; i < this.cashFlowQuarterly.length; i++) {
                        if (delta.includes(this.cashFlowQuarterly[i].document_date)) {
                            if (this.cashFlowQuarterly[i].document_date === biggest_date) {
                                this.cashFlowQuarterly[i].document_current = 1;
                            }
                            toSave.push(this.cashFlowQuarterly[i]);
                        }
                    }

                    const cash = await CashFlow.findOne({
                        where: {
                            document_current: 1,
                            ticker_internal_id: this.ticker_internal_id,
                            document_type_year_or_quarter: Document_type_year_or_quarter.q,
                        },
                    });

                    if (cash && cash?.document_date < biggest_date) {
                        await cash.update({document_current: 0});
                        await cash.save();
                    }
                }
            }

            if (toSave.length > 0) {
                await CashFlow.bulkCreate(toSave);
                consoleLog("CashFlow quarterly saved", this.saveQuarterlyCashFlow.name, LogLevel.DEBUG);
            }
        } catch (error: any) {
            consoleLog(`Error when try to save CashFlow quarterly ${error}`, this.saveQuarterlyCashFlow.name, LogLevel.ERROR);
            addLogJobExecution(
                LogLevel.ERROR,
                this.saveQuarterlyCashFlow.name,
                "Error when try to save CashFlow quarterly",
                {error: error instanceof Error ? error.message : String(error)},
                this.ticker_internal_id,
            );
            throw Error("Error when try to save CashFlow quarterly " + error);
        }
    }
}

import {IncomeStatement} from "../../entities/consolidated_data/IncomeStatement";
import {Document_type_year_or_quarter} from "../../entities/consolidated_data/DocumentTypeYearorQuarterly";
import _, {snakeCase} from "lodash";
import {consoleLog} from "@/lib/utils";
import {LogLevel} from "@/utils/types/logs/log";
import {addLogJobExecution} from "@/lib/agenda";

export class IncomeStatementRepository {
    private saved_yearly: string[];
    private saved_quarterly: string[];
    private incomeStatementYearly: IncomeStatement[];
    private incomeStatementQuarterly: IncomeStatement[];
    private ticker_internal_id: number;
    private files_dates_yearly: string[];
    private files_dates_quarterly: string[];

    constructor(ticker_internal_id: number) {
        this.ticker_internal_id = ticker_internal_id;
    }

    async getSavedYearlyIncomeStatement(): Promise<string[]> {
        const saved_yearly = await IncomeStatement.findAll({
            where: {
                ticker_internal_id: this.ticker_internal_id,
                document_type_year_or_quarter: Document_type_year_or_quarter.y,
            },
            attributes: ["document_date"],
        });

        const data: string[] = [];

        for (let i = 0; i < saved_yearly.length; i++) {
            data.push(saved_yearly[i].document_date);
        }

        this.saved_yearly = data;
        return this.saved_yearly;
    }

    async getSavedQuarterlyIncomeStatement(): Promise<string[]> {
        const saved_quarterly = await IncomeStatement.findAll({
            where: {
                ticker_internal_id: this.ticker_internal_id,
                document_type_year_or_quarter: Document_type_year_or_quarter.q,
            },
            attributes: ["document_date"],
        });

        const data: string[] = [];

        for (let i = 0; i < saved_quarterly.length; i++) {
            data.push(saved_quarterly[i].document_date);
        }

        this.saved_quarterly = data;
        return this.saved_quarterly;
    }

    async getYearlyIncomeStatement(incomeStatementYearly: any): Promise<IncomeStatement[]> {
        try {
            const ticker_internal_id = this.ticker_internal_id;
            const data: IncomeStatement[] = [];
            const keys = Object.keys(incomeStatementYearly);
            this.files_dates_yearly = keys;

            for (let i = 0; i < keys.length; i++) {
                const current_key = keys[i];
                const incomeStatement = incomeStatementYearly[current_key];
                const snakeCasedObject = Object.keys(incomeStatement).reduce(
                    (result, key) => ({
                        ...result,
                        ticker_internal_id,
                        document_type_year_or_quarter: Document_type_year_or_quarter.y,
                        document_date: current_key,
                        [snakeCase(key)]: incomeStatement[key],
                    }),
                    {
                        ticker_internal_id,
                        document_type_year_or_quarter: Document_type_year_or_quarter.y,
                        document_date: current_key,
                    },
                );
                const incomeStatementData = new IncomeStatement({...snakeCasedObject});
                data.push(incomeStatementData);
            }

            this.incomeStatementYearly = data;
            return this.incomeStatementYearly;
        } catch (error: any) {
            consoleLog(`Error when try to get IncomeStatement Yearly ${error}`, this.getYearlyIncomeStatement.name, LogLevel.ERROR);
            addLogJobExecution(
                LogLevel.ERROR,
                this.getYearlyIncomeStatement.name,
                "Error when try to get IncomeStatement Yearly",
                {error: error instanceof Error ? error.message : String(error)},
                this.ticker_internal_id,
            );
            throw Error("Error when try to get IncomeStatement Yearly " + error);
        }
    }

    async getQuarterlyIncomeStatement(incomeStatementQuarterly: any): Promise<IncomeStatement[]> {
        try {
            const ticker_internal_id = this.ticker_internal_id;
            const data: IncomeStatement[] = [];
            const keys = Object.keys(incomeStatementQuarterly);
            this.files_dates_quarterly = keys;

            for (let i = 0; i < keys.length; i++) {
                const current_key = keys[i];
                const incomeStatement = incomeStatementQuarterly[current_key];
                const snakeCasedObject = Object.keys(incomeStatement).reduce(
                    (result, key) => ({
                        ...result,
                        ticker_internal_id,
                        document_type_year_or_quarter: Document_type_year_or_quarter.q,
                        document_date: current_key,
                        [snakeCase(key)]: incomeStatement[key],
                    }),
                    {
                        ticker_internal_id,
                        document_type_year_or_quarter: Document_type_year_or_quarter.q,
                        document_date: current_key,
                    },
                );
                const incomeStatementData = new IncomeStatement({...snakeCasedObject});
                data.push(incomeStatementData);
            }

            this.incomeStatementQuarterly = data;
            return this.incomeStatementQuarterly;
        } catch (error: any) {
            consoleLog(`Error when try to get IncomeStatement Quarterly ${error}`, this.getQuarterlyIncomeStatement.name, LogLevel.ERROR);
            addLogJobExecution(
                LogLevel.ERROR,
                this.getQuarterlyIncomeStatement.name,
                "Error when try to get IncomeStatement Quarterly",
                {error: error instanceof Error ? error.message : String(error)},
                this.ticker_internal_id,
            );
            throw Error("Error when try to get Income Statement Quarterly " + error);
        }
    }

    async saveYearlyIncomeStatement() {
        try {
            let toSave: IncomeStatement[] = [];

            if (this.saved_yearly.length === 0 && this.incomeStatementYearly.length > 0) {
                toSave = this.incomeStatementYearly;
                toSave[0].document_current = 1;
            } else {
                const delta = _.difference(this.files_dates_yearly, this.saved_yearly);

                if (delta.length > 0) {
                    const income = await IncomeStatement.findOne({
                        where: {
                            document_current: 1,
                            ticker_internal_id: this.ticker_internal_id,
                            document_type_year_or_quarter: Document_type_year_or_quarter.y,
                        },
                    });

                    let bigger_date_file = this.files_dates_yearly[0];
                    for (let i = 0; i < this.incomeStatementYearly.length; i++) {
                        if (delta?.includes(this.incomeStatementYearly[i].document_date)) {
                            if (this.incomeStatementYearly[i].document_date === bigger_date_file) {
                                this.incomeStatementYearly[i].document_current = 1;
                            }
                            toSave.push(this.incomeStatementYearly[i]);
                        }
                    }

                    if (income && income.document_date < bigger_date_file) {
                        income.document_current = 0;
                        await income.save();
                    }
                }
            }

            if (toSave.length > 0) {
                await IncomeStatement.bulkCreate(toSave); //cast to interface BalanceSheet
                consoleLog("IncomeStatement yearly saved", this.saveYearlyIncomeStatement.name, LogLevel.DEBUG);
            }
        } catch (error: any) {
            consoleLog(`Error when try to save IncomeStatement yearly ${error}`, this.saveYearlyIncomeStatement.name, LogLevel.ERROR);
            addLogJobExecution(
                LogLevel.ERROR,
                this.saveYearlyIncomeStatement.name,
                "Error when try to save IncomeStatement yearly",
                {error: error instanceof Error ? error.message : String(error)},
                this.ticker_internal_id,
            );
            throw Error("Error when try to save IncomeStatement yearly " + error);
        }
    }

    async saveQuarterlyIncomeStatement() {
        try {
            let toSave: IncomeStatement[] = [];

            if (this.saved_quarterly.length === 0 && this.incomeStatementQuarterly.length > 0) {
                toSave = this.incomeStatementQuarterly;
                toSave[0].document_current = 1;
            } else {
                const delta = _.difference(this.files_dates_quarterly, this.saved_quarterly);

                if (delta.length > 0) {
                    const income = await IncomeStatement.findOne({
                        where: {
                            document_current: 1,
                            ticker_internal_id: this.ticker_internal_id,
                            document_type_year_or_quarter: Document_type_year_or_quarter.q,
                        },
                    });

                    let bigger_date_file = this.files_dates_quarterly[0];
                    for (let i = 0; i < this.incomeStatementQuarterly.length; i++) {
                        if (delta?.includes(this.incomeStatementQuarterly[i].document_date)) {
                            if (this.incomeStatementQuarterly[i].document_date === bigger_date_file) {
                                this.incomeStatementQuarterly[i].document_current = 1;
                            }
                            toSave.push(this.incomeStatementQuarterly[i]);
                        }
                    }

                    if (income && income.document_date < bigger_date_file) {
                        income.document_current = 0;
                        await income.save();
                    }
                }
            }

            if (toSave.length > 0) {
                await IncomeStatement.bulkCreate(toSave); //cast to interface BalanceSheet
                consoleLog("IncomeStatement quarterly saved", this.saveQuarterlyIncomeStatement.name, LogLevel.DEBUG);
            }
        } catch (error: any) {
            consoleLog(`Error when try to save IncomeStatement quarterly ${error}`, this.saveQuarterlyIncomeStatement.name, LogLevel.ERROR);
            addLogJobExecution(
                LogLevel.ERROR,
                this.saveQuarterlyIncomeStatement.name,
                "Error when try to save IncomeStatement quarterly",
                {error: error instanceof Error ? error.message : String(error)},
                this.ticker_internal_id,
            );
            throw Error("Error when try to save IncomeStatement quarterly " + error);
        }
    }
}

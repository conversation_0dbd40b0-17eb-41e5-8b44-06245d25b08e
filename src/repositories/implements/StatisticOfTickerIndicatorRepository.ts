import moment from "moment";
import {Op} from "sequelize";
import {BalanceSheet} from "../../entities/consolidated_data/BalanceSheet";
import {CashFlow} from "../../entities/consolidated_data/CashFlow";
import {Document_type_year_or_quarter} from "../../entities/consolidated_data/DocumentTypeYearorQuarterly";
import {HistoricalDividends} from "../../entities/consolidated_data/HistoricalDividends";
import {IncomeStatement} from "../../entities/consolidated_data/IncomeStatement";
import {iStatisticsOfTicker} from "../../entities/consolidated_data/interfaces/iStatisticsOfTicker";
import {consoleLog} from "@/lib/utils";
import {LogLevel} from "@/utils/types/logs/log";

export class StatisticOfTickerIndicatorRepository {
    ticker: iStatisticsOfTicker;
    balance_sheet: BalanceSheet | null;
    all_balance_sheet: BalanceSheet[] | [];
    cash_flow: CashFlow | null;
    income_statement: IncomeStatement | null;
    all_income_statement: IncomeStatement[] | [];
    income_statement_last_year: IncomeStatement[] | [];
    enterprise_value: number;
    dividends: HistoricalDividends[];
    sum_dividends: number;
    total_revenue: number;
    gross_profit: number;
    dividends_count: number;

    constructor(ticker: iStatisticsOfTicker) {
        this.ticker = ticker;
    }

    async get_last_balance_sheet_yearly(balance_sheet: BalanceSheet[]) {
        this.balance_sheet = balance_sheet[0] || {};
        this.all_balance_sheet = balance_sheet;
        return this.balance_sheet;
    }

    async get_last_cash_flow_yearly() {
        this.cash_flow = await CashFlow.findOne({
            where: {
                ticker_internal_id: this.ticker.ticker_internal_id,
                document_type_year_or_quarter: Document_type_year_or_quarter.y,
                document_current: 1,
            },
        });
        return this.cash_flow;
    }

    async get_last_income_statement_yearly(income_statement: IncomeStatement[], income_statement_last_year: IncomeStatement[]) {
        this.income_statement = income_statement[0] || {}; //First item ordered by document_date DESC
        this.all_income_statement = income_statement; //last 4 ordered by document_date
        this.income_statement_last_year = income_statement_last_year;
        return this.income_statement;
    }

    async getDividends() {
        const last_12_months = moment().subtract(11, "months");
        const date_formated = last_12_months.format("YYYY-MM-DD");
        const startedDate = `${date_formated.substring(0, date_formated.length - 2)}01`;
        const endDate = moment().format("YYYY-MM-DD");

        consoleLog(`Getting dividends for ${this.ticker.symbol_code} from ${startedDate} to ${endDate}`, this.getDividends.name, LogLevel.DEBUG);

        const dividends = await HistoricalDividends.findAll({
            where: {
                ticker_internal_id: this.ticker.ticker_internal_id,
                declaration_date: {
                    [Op.gte]: startedDate,
                },
            },
            attributes: ["unadjusted_value", "value"],
        });
        this.dividends = dividends;
        let sum = 0;
        this.dividends_count = dividends.length || 0;

        consoleLog(`Found ${dividends.length} dividends for ${this.ticker.symbol_code}`, this.getDividends.name, LogLevel.DEBUG);

        for (let i = 0; i < dividends.length; i++) {
            const value = dividends[i].value || dividends[i].unadjusted_value;
            if (value) {
                sum = sum + (value || 0);
            }
        }

        consoleLog(`Sum of dividends for ${this.ticker.symbol_code} is ${sum}`, this.getDividends.name, LogLevel.DEBUG);
        this.sum_dividends = sum;
        return dividends;
    }

    async createDividendYield() {
        const current_price = this.ticker.price;
        let dividend_yield = 0;
        if (current_price !== 0 && this.dividends_count > 0 && this.sum_dividends > 0) {
            const dividends_avg = this.sum_dividends / this.dividends_count;
            dividend_yield = (this.sum_dividends / current_price) * 100;
        }
        if (dividend_yield !== 0) {
            this.ticker.dividend_yield = dividend_yield;
        }
        consoleLog(`Dividend yield for ${this.ticker.symbol_code} is ${dividend_yield}`, this.createDividendYield.name, LogLevel.DEBUG);
        consoleLog(`sum_dividends ${this.sum_dividends} and dividends_count ${this.dividends_count} for ${this.ticker.symbol_code}`, this.createDividendYield.name, LogLevel.DEBUG);
    }

    async createEpsLastDate() {
        let sum_eps_last_date = 0;
        for (let i = 0; i < this.income_statement_last_year.length; i++) {
            sum_eps_last_date = sum_eps_last_date + this.income_statement_last_year[i].eps_diluted_current;
        }
        this.ticker.eps_last_year = sum_eps_last_date;
        consoleLog(`EPS last year for ${this.ticker.symbol_code} is ${sum_eps_last_date}`, this.createEpsLastDate.name, LogLevel.DEBUG);
    }

    async createPe() {
        const current_price = this.ticker.price || 0;
        const eps = this.ticker.eps_current || 0;
        if (eps !== 0) {
            this.ticker.pe = current_price / eps;
        }
        consoleLog(`PE for ${this.ticker.symbol_code} is ${this.ticker.pe}`, this.createPe.name, LogLevel.DEBUG);
    }

    async createEpsDilutedCurrent() {
        let sum_eps_current = 0;
        for (let i = 0; i < this.all_income_statement.length; i++) {
            sum_eps_current = sum_eps_current + this.all_income_statement[i].eps_diluted_current;
        }
        this.ticker.eps_current = sum_eps_current;
        consoleLog(`EPS current for ${this.ticker.symbol_code} is ${sum_eps_current}`, this.createEpsDilutedCurrent.name, LogLevel.DEBUG);
    }

    async createPegRatio() {
        const pe = this.ticker.pe || 0;
        const eps_current = this.ticker.eps_current || 0;
        const eps_last_year = this.ticker.eps_last_year || 0;
        consoleLog(`EPS current for ${this.ticker.symbol_code} is ${eps_current}`, this.createPegRatio.name, LogLevel.DEBUG);
        consoleLog(`EPS last year for ${this.ticker.symbol_code} is ${eps_last_year}`, this.createPegRatio.name, LogLevel.DEBUG);
        if (eps_last_year !== 0 && eps_current !== 0 && eps_last_year !== 0) {
            let denominator = (eps_current / eps_last_year - 1) * 100;
            if (denominator != 0) {
                this.ticker.peg_ratio = pe / denominator;
            }
        }
        consoleLog(`PEG ratio for ${this.ticker.symbol_code} is ${this.ticker.peg_ratio}`, this.createPegRatio.name, LogLevel.DEBUG);
    }

    async createBvps() {
        const common_stock_shares_outstanding = this.balance_sheet?.common_stock_shares_outstanding || 0;
        const total_stockholder_equity = this.balance_sheet?.total_stockholder_equity || 0;
        consoleLog(`Common stock shares outstanding for ${this.ticker.symbol_code} is ${common_stock_shares_outstanding}`, this.createBvps.name, LogLevel.DEBUG);
        consoleLog(`Total stockholder equity for ${this.ticker.symbol_code} is ${total_stockholder_equity}`, this.createBvps.name, LogLevel.DEBUG);
        if (common_stock_shares_outstanding !== 0 && total_stockholder_equity !== 0) {
            this.ticker.bvps = total_stockholder_equity / common_stock_shares_outstanding;
        }
        consoleLog(`BVPS for ${this.ticker.symbol_code} is ${this.ticker.bvps}`, this.createBvps.name, LogLevel.DEBUG);
    }

    async createPriceToBook() {
        const current_price = this.ticker.price || 0;
        const bvps = this.ticker.bvps || 0;
        consoleLog(`Current price for ${this.ticker.symbol_code} is ${current_price}`, this.createPriceToBook.name, LogLevel.DEBUG);
        consoleLog(`BVPS for ${this.ticker.symbol_code} is ${bvps}`, this.createPriceToBook.name, LogLevel.DEBUG);
        if (current_price !== 0 && bvps !== 0) {
            this.ticker.price_to_book = current_price / bvps;
        }
        consoleLog(`Price to book for ${this.ticker.symbol_code} is ${this.ticker.price_to_book}`, this.createPriceToBook.name, LogLevel.DEBUG);
    }

    async createEbitda() {
        let sum_ebitda = 0;
        for (let i = 0; i < this.all_income_statement.length; i++) {
            sum_ebitda = sum_ebitda + this.all_income_statement[i].ebitda;
        }
        this.ticker.ebitda = sum_ebitda;
        consoleLog(`EBITDA for ${this.ticker.symbol_code} is ${this.ticker.ebitda}`, this.createEbitda.name, LogLevel.DEBUG);
    }

    async createEbitdaMargin() {
        const ebitda = this.ticker.ebitda || 0;
        const total_revenue = this.total_revenue || 0;
        consoleLog(`EBITDA for ${this.ticker.symbol_code} is ${ebitda}`, this.createEbitdaMargin.name, LogLevel.DEBUG);
        consoleLog(`Total revenue for ${this.ticker.symbol_code} is ${total_revenue}`, this.createEbitdaMargin.name, LogLevel.DEBUG);
        if (ebitda !== 0 && total_revenue != 0) {
            this.ticker.ebitda_margin = (ebitda / total_revenue) * 100;
        }
        consoleLog(`EBITDA margin for ${this.ticker.symbol_code} is ${this.ticker.ebitda_margin}`, this.createEbitdaMargin.name, LogLevel.DEBUG);
    }

    async createEbit() {
        let sum_ebit = 0;
        for (let i = 0; i < this.all_income_statement.length; i++) {
            sum_ebit = sum_ebit + this.all_income_statement[i].ebit;
        }
        this.ticker.ebit = sum_ebit;
        consoleLog(`EBIT for ${this.ticker.symbol_code} is ${this.ticker.ebit}`, this.createEbit.name, LogLevel.DEBUG);
    }

    async createOperationMargin() {
        const ebit = this.ticker.ebit || 0;
        const total_revenue = this?.total_revenue || 0;
        consoleLog(`EBIT for ${this.ticker.symbol_code} is ${ebit}`, this.createOperationMargin.name, LogLevel.DEBUG);
        consoleLog(`Total revenue for ${this.ticker.symbol_code} is ${total_revenue}`, this.createOperationMargin.name, LogLevel.DEBUG);
        if (ebit !== 0 && total_revenue !== 0) {
            this.ticker.operation_margin = (ebit / total_revenue) * 100;
        }
        consoleLog(`Operation margin for ${this.ticker.symbol_code} is ${this.ticker.operation_margin}`, this.createOperationMargin.name, LogLevel.DEBUG);
    }

    async createNetMargin() {
        const net_income = this.income_statement?.net_income || 0;
        const total_revenue = this?.all_income_statement[0]?.total_revenue || 0;
        consoleLog(`Net income for ${this.ticker.symbol_code} is ${net_income}`, this.createNetMargin.name, LogLevel.DEBUG);
        consoleLog(`Total revenue for ${this.ticker.symbol_code} is ${total_revenue}`, this.createNetMargin.name, LogLevel.DEBUG);
        if (net_income !== 0 && total_revenue !== 0) {
            this.ticker.net_margin = (net_income / total_revenue) * 100;
        }
        consoleLog(`Net margin for ${this.ticker.symbol_code} is ${this.ticker.net_margin}`, this.createNetMargin.name, LogLevel.DEBUG);
    }

    async createGrossMargin() {
        const total_revenue = this?.total_revenue || 0;
        const gross_profit = this?.gross_profit || 0;
        consoleLog(`Total revenue for ${this.ticker.symbol_code} is ${total_revenue}`, this.createGrossMargin.name, LogLevel.DEBUG);
        consoleLog(`Gross profit for ${this.ticker.symbol_code} is ${gross_profit}`, this.createGrossMargin.name, LogLevel.DEBUG);
        if (total_revenue !== 0 && gross_profit !== 0) {
            this.ticker.gross_margin = (gross_profit / total_revenue) * 100;
        }
        consoleLog(`Gross margin for ${this.ticker.symbol_code} is ${this.ticker.gross_margin}`, this.createGrossMargin.name, LogLevel.DEBUG);
    }

    async createPriceWorkingCapitalShare() {
        const current_price = this.ticker.price || 0;
        const common_stock_shares_outstanding = this.balance_sheet?.common_stock_shares_outstanding || 0;
        const total_current_assets = this.balance_sheet?.total_current_assets || 0;
        const total_current_liabilities = this.balance_sheet?.total_current_liabilities || 0;
        consoleLog(`Current price for ${this.ticker.symbol_code} is ${current_price}`, this.createPriceWorkingCapitalShare.name, LogLevel.DEBUG);
        consoleLog(`Common stock shares outstanding for ${this.ticker.symbol_code} is ${common_stock_shares_outstanding}`, this.createPriceWorkingCapitalShare.name, LogLevel.DEBUG);
        consoleLog(`Total current assets for ${this.ticker.symbol_code} is ${total_current_assets}`, this.createPriceWorkingCapitalShare.name, LogLevel.DEBUG);
        consoleLog(`Total current liabilities for ${this.ticker.symbol_code} is ${total_current_liabilities}`, this.createPriceWorkingCapitalShare.name, LogLevel.DEBUG);
        if (common_stock_shares_outstanding !== 0 && current_price !== 0 && total_current_assets !== 0 && total_current_liabilities !== 0) {
            const denominator = (total_current_assets - total_current_liabilities) / common_stock_shares_outstanding;
            if (denominator !== 0) {
                this.ticker.price_working_capital_share = current_price / denominator;
            }
        }
        consoleLog(`Price working capital share for ${this.ticker.symbol_code} is ${this.ticker.price_working_capital_share}`, this.createPriceWorkingCapitalShare.name, LogLevel.DEBUG);
    }

    async createEbitRatio() {
        const current_price = this.ticker.price || 0;
        const common_stock_shares_outstanding = this.balance_sheet?.common_stock_shares_outstanding || 0;
        const ebit = this.ticker.ebit || 0;
        consoleLog(`Current price for ${this.ticker.symbol_code} is ${current_price}`, this.createEbitRatio.name, LogLevel.DEBUG);
        consoleLog(`Common stock shares outstanding for ${this.ticker.symbol_code} is ${common_stock_shares_outstanding}`, this.createEbitRatio.name, LogLevel.DEBUG);
        consoleLog(`EBIT for ${this.ticker.symbol_code} is ${ebit}`, this.createEbitRatio.name, LogLevel.DEBUG);
        if (current_price !== 0 && ebit !== 0 && common_stock_shares_outstanding !== 0) {
            this.ticker.ebit_ratio = current_price / (ebit / common_stock_shares_outstanding);
        }
        consoleLog(`EBIT ratio for ${this.ticker.symbol_code} is ${this.ticker.ebit_ratio}`, this.createEbitRatio.name, LogLevel.DEBUG);
    }

    async createNetDebt() {
        if (this.balance_sheet?.net_debt && this.balance_sheet?.net_debt !== 0) {
            this.ticker.net_debt = this.balance_sheet?.net_debt || 0;
        }
        consoleLog(`Net debt for ${this.ticker.symbol_code} is ${this.ticker.net_debt}`, this.createNetDebt.name, LogLevel.DEBUG);
    }

    async createNetDebtEbit() {
        const net_debt = this.ticker.net_debt || 0;
        const ebit = this.ticker.ebit || 0;
        consoleLog(`Net debt for ${this.ticker.symbol_code} is ${net_debt}`, this.createNetDebtEbit.name, LogLevel.DEBUG);
        consoleLog(`EBIT for ${this.ticker.symbol_code} is ${ebit}`, this.createNetDebtEbit.name, LogLevel.DEBUG);
        if (ebit !== 0) {
            this.ticker.net_debt_ebit = net_debt / ebit;
        }
        consoleLog(`Net debt EBIT for ${this.ticker.symbol_code} is ${this.ticker.net_debt_ebit}`, this.createNetDebtEbit.name, LogLevel.DEBUG);
    }

    async createNetDebtEbitda() {
        const net_debt = this.ticker.net_debt || 0;
        const ebitda = this.ticker.ebitda || 0;
        consoleLog(`Net debt for ${this.ticker.symbol_code} is ${net_debt}`, this.createNetDebtEbitda.name, LogLevel.DEBUG);
        consoleLog(`EBITDA for ${this.ticker.symbol_code} is ${ebitda}`, this.createNetDebtEbitda.name, LogLevel.DEBUG);
        if (ebitda !== 0) {
            this.ticker.net_debt_ebitda = net_debt / ebitda;
        }
        consoleLog(`Net debt EBITDA for ${this.ticker.symbol_code} is ${this.ticker.net_debt_ebitda}`, this.createNetDebtEbitda.name, LogLevel.DEBUG);
    }

    async createMarketCapitalization() {
        const current_price = this.ticker.price || 0;
        const common_stock_shares_outstanding = this.balance_sheet?.common_stock_shares_outstanding || 0;
        const market_capitalization = current_price * common_stock_shares_outstanding;
        consoleLog(`Current price for ${this.ticker.symbol_code} is ${current_price}`, this.createMarketCapitalization.name, LogLevel.DEBUG);
        consoleLog(`Common stock shares outstanding for ${this.ticker.symbol_code} is ${common_stock_shares_outstanding}`, this.createMarketCapitalization.name, LogLevel.DEBUG);
        consoleLog(`Market capitalization for ${this.ticker.symbol_code} is ${market_capitalization}`, this.createMarketCapitalization.name, LogLevel.DEBUG);
        this.ticker.market_capitalization = market_capitalization;
        consoleLog(`Market capitalization for ${this.ticker.symbol_code} is ${this.ticker.market_capitalization}`, this.createMarketCapitalization.name, LogLevel.DEBUG);
    }

    async createEV() {
        const market_capitalization = this.ticker.market_capitalization || 0;
        const net_debt = this.ticker.net_debt || 0;
        consoleLog(`Market capitalization for ${this.ticker.symbol_code} is ${market_capitalization}`, this.createEV.name, LogLevel.DEBUG);
        consoleLog(`Net debt for ${this.ticker.symbol_code} is ${net_debt}`, this.createEV.name, LogLevel.DEBUG);
        if (net_debt !== 0 && market_capitalization !== 0) {
            this.enterprise_value = market_capitalization - net_debt;
        }
        consoleLog(`Enterprise value for ${this.ticker.symbol_code} is ${this.enterprise_value}`, this.createEV.name, LogLevel.DEBUG);
    }

    async createEvEbit() {
        const ebit = this.ticker.ebit || 0;
        const enterprise_value = this.enterprise_value || 0;
        consoleLog(`EBIT for ${this.ticker.symbol_code} is ${ebit}`, this.createEvEbit.name, LogLevel.DEBUG);
        consoleLog(`Enterprise value for ${this.ticker.symbol_code} is ${enterprise_value}`, this.createEvEbit.name, LogLevel.DEBUG);
        if (ebit !== 0 && enterprise_value !== 0) {
            this.ticker.ev_ebit = enterprise_value / ebit;
        }
        consoleLog(`EV EBIT for ${this.ticker.symbol_code} is ${this.ticker.ev_ebit}`, this.createEvEbit.name, LogLevel.DEBUG);
    }

    async createNetDebtShareholdersEquity() {
        const net_debt = this.ticker.net_debt || 0;
        const common_stock_shares_outstanding = this.balance_sheet?.common_stock_shares_outstanding || 0;
        consoleLog(`Net debt for ${this.ticker.symbol_code} is ${net_debt}`, this.createNetDebtShareholdersEquity.name, LogLevel.DEBUG);
        consoleLog(`Common stock shares outstanding for ${this.ticker.symbol_code} is ${common_stock_shares_outstanding}`, this.createNetDebtShareholdersEquity.name, LogLevel.DEBUG);
        if (net_debt !== 0 && common_stock_shares_outstanding !== 0) {
            this.ticker.net_debt_shareholders_equity = net_debt / common_stock_shares_outstanding;
        }
        consoleLog(`Net debt shareholders equity for ${this.ticker.symbol_code} is ${this.ticker.net_debt_shareholders_equity}`, this.createNetDebtShareholdersEquity.name, LogLevel.DEBUG);
    }

    async createPriceSalesRatio() {
        const market_capitalization = this.ticker.market_capitalization || 0;
        const total_revenue = this?.total_revenue || 0;
        consoleLog(`Market capitalization for ${this.ticker.symbol_code} is ${market_capitalization}`, this.createPriceSalesRatio.name, LogLevel.DEBUG);
        consoleLog(`Total revenue for ${this.ticker.symbol_code} is ${total_revenue}`, this.createPriceSalesRatio.name, LogLevel.DEBUG);
        if (market_capitalization !== 0 && total_revenue !== 0) {
            this.ticker.price_sales_ratio = market_capitalization / total_revenue;
        }
        consoleLog(`Price sales ratio for ${this.ticker.symbol_code} is ${this.ticker.price_sales_ratio}`, this.createPriceSalesRatio.name, LogLevel.DEBUG);
    }

    async createRoeRoaRoic() {
        let sum_roe = 0;
        let sum_roa = 0;
        let sum_roic = 0;

        for (let i = 0; i < 4; i++) {
            const total_stockholder_equity = this.all_balance_sheet[i]?.total_stockholder_equity || 0;
            const total_current_assets = this.all_balance_sheet[i]?.total_assets || 0;
            const short_term_debt = this.all_balance_sheet[i]?.short_term_debt || 0;
            const long_term_debt = this.all_balance_sheet[i]?.long_term_debt || 0;
            consoleLog(`Total stockholder equity for ${this.ticker.symbol_code} is ${total_stockholder_equity}`, this.createRoeRoaRoic.name, LogLevel.DEBUG);
            consoleLog(`Total current assets for ${this.ticker.symbol_code} is ${total_current_assets}`, this.createRoeRoaRoic.name, LogLevel.DEBUG);
            consoleLog(`Short term debt for ${this.ticker.symbol_code} is ${short_term_debt}`, this.createRoeRoaRoic.name, LogLevel.DEBUG);
            consoleLog(`Long term debt for ${this.ticker.symbol_code} is ${long_term_debt}`, this.createRoeRoaRoic.name, LogLevel.DEBUG);

            const net_income = this.all_income_statement[i]?.net_income;
            const income_tax_expense = this.all_income_statement[i]?.income_tax_expense || this.all_income_statement[i]?.tax_provision || 0;
            const income_before_tax = this.all_income_statement[i]?.income_before_tax || 0;
            const operating_income = this.all_income_statement[i]?.operating_income || 0;
            consoleLog(`Net income for ${this.ticker.symbol_code} is ${net_income}`, this.createRoeRoaRoic.name, LogLevel.DEBUG);
            consoleLog(`Income tax expense for ${this.ticker.symbol_code} is ${income_tax_expense}`, this.createRoeRoaRoic.name, LogLevel.DEBUG);
            consoleLog(`Income before tax for ${this.ticker.symbol_code} is ${income_before_tax}`, this.createRoeRoaRoic.name, LogLevel.DEBUG);
            consoleLog(`Operating income for ${this.ticker.symbol_code} is ${operating_income}`, this.createRoeRoaRoic.name, LogLevel.DEBUG);

            if (total_stockholder_equity !== 0) {
                sum_roe += net_income / total_stockholder_equity;
            }

            if (total_current_assets !== 0) {
                sum_roa += net_income / total_current_assets;
            }

            const denominator = short_term_debt + long_term_debt + total_stockholder_equity;
            consoleLog(`Denominator for ${this.ticker.symbol_code} is ${denominator}`, this.createRoeRoaRoic.name, LogLevel.DEBUG);

            if (denominator !== 0 && income_before_tax !== 0) {
                const tax_rate = income_tax_expense / income_before_tax;
                const nopat = operating_income * (1 - tax_rate);
                sum_roic += nopat / denominator;
            }
        }

        this.ticker.roe = sum_roe * 100;
        this.ticker.roa = sum_roa * 100;
        this.ticker.roic = sum_roic * 100;
        consoleLog(`ROE for ${this.ticker.symbol_code} is ${this.ticker.roe}`, this.createRoeRoaRoic.name, LogLevel.DEBUG);
        consoleLog(`ROA for ${this.ticker.symbol_code} is ${this.ticker.roa}`, this.createRoeRoaRoic.name, LogLevel.DEBUG);
        consoleLog(`ROIC for ${this.ticker.symbol_code} is ${this.ticker.roic}`, this.createRoeRoaRoic.name, LogLevel.DEBUG);
    }

    async createCurrentRatio() {
        const total_current_assets = this.balance_sheet?.total_current_assets || 0;
        const total_current_liabilities = this.balance_sheet?.total_current_liabilities || 0;
        consoleLog(`Total current assets for ${this.ticker.symbol_code} is ${total_current_assets}`, this.createCurrentRatio.name, LogLevel.DEBUG);
        consoleLog(`Total current liabilities for ${this.ticker.symbol_code} is ${total_current_liabilities}`, this.createCurrentRatio.name, LogLevel.DEBUG);
        if (total_current_assets !== 0 && total_current_liabilities !== 0) {
            this.ticker.current_ratio = total_current_assets / total_current_liabilities;
        }
        consoleLog(`Current ratio for ${this.ticker.symbol_code} is ${this.ticker.current_ratio}`, this.createCurrentRatio.name, LogLevel.DEBUG);
    }

    async createShareholderEquityRatio() {
        const total_stockholder_equity = this.balance_sheet?.total_stockholder_equity || 0;
        const total_current_assets = this.balance_sheet?.total_current_assets || 0;
        consoleLog(`Total stockholder equity for ${this.ticker.symbol_code} is ${total_stockholder_equity}`, this.createShareholderEquityRatio.name, LogLevel.DEBUG);
        consoleLog(`Total current assets for ${this.ticker.symbol_code} is ${total_current_assets}`, this.createShareholderEquityRatio.name, LogLevel.DEBUG);
        if (total_current_assets != 0) {
            this.ticker.shareholder_equity_ratio = total_stockholder_equity / total_current_assets;
        }
        consoleLog(`Shareholder equity ratio for ${this.ticker.symbol_code} is ${this.ticker.shareholder_equity_ratio}`, this.createShareholderEquityRatio.name, LogLevel.DEBUG);
    }

    async createTotalDebtToTotalAssetsRatio() {
        const total_current_assets = this.balance_sheet?.total_current_assets || 0;
        const total_liab = this.balance_sheet?.total_liab || 0;
        consoleLog(`Total current assets for ${this.ticker.symbol_code} is ${total_current_assets}`, this.createTotalDebtToTotalAssetsRatio.name, LogLevel.DEBUG);
        consoleLog(`Total liab for ${this.ticker.symbol_code} is ${total_liab}`, this.createTotalDebtToTotalAssetsRatio.name, LogLevel.DEBUG);
        if (total_current_assets !== 0 && total_liab !== 0) {
            this.ticker.total_debt_to_total_assets_ratio = total_current_assets / total_liab;
        }
        consoleLog(`Total debt to total assets ratio for ${this.ticker.symbol_code} is ${this.ticker.total_debt_to_total_assets_ratio}`, this.createTotalDebtToTotalAssetsRatio.name, LogLevel.DEBUG);
    }

    async createAssetTurnover() {
        const total_current_assets = this.balance_sheet?.total_current_assets || 0;
        const total_revenue = this?.total_revenue || 0;
        consoleLog(`Total current assets for ${this.ticker.symbol_code} is ${total_current_assets}`, this.createAssetTurnover.name, LogLevel.DEBUG);
        consoleLog(`Total revenue for ${this.ticker.symbol_code} is ${total_revenue}`, this.createAssetTurnover.name, LogLevel.DEBUG);
        if (total_current_assets !== 0 && total_revenue !== 0) {
            this.ticker.asset_turnover = total_revenue / total_current_assets;
        }
        consoleLog(`Asset turnover for ${this.ticker.symbol_code} is ${this.ticker.asset_turnover}`, this.createAssetTurnover.name, LogLevel.DEBUG);
    }

    async createPayoutRatio() {
        const sum_dividends = this.sum_dividends;
        const eps = this.ticker.eps_current || 0;
        consoleLog(`EPS current for ${this.ticker.symbol_code} is ${eps}`, this.createPayoutRatio.name, LogLevel.DEBUG);
        consoleLog(`Sum dividends for ${this.ticker.symbol_code} is ${sum_dividends}`, this.createPayoutRatio.name, LogLevel.DEBUG);
        if (eps !== 0) {
            this.ticker.payout_ratio = (sum_dividends / eps) * 100; //It is in percentage
        }
        consoleLog(`Payout ratio for ${this.ticker.symbol_code} is ${this.ticker.payout_ratio}`, this.createPayoutRatio.name, LogLevel.DEBUG);
    }

    async createTotalRevenue() {
        let sum_total_revenue = 0;
        for (let i = 0; i < this.all_income_statement.length; i++) {
            sum_total_revenue += this.all_income_statement[i].total_revenue;
        }
        this.total_revenue = sum_total_revenue;
        consoleLog(`Total revenue for ${this.ticker.symbol_code} is ${sum_total_revenue}`, this.createTotalRevenue.name, LogLevel.DEBUG);
    }

    async createGrossProfit() {
        let sum_gross_profit = 0;
        for (let i = 0; i < this.all_income_statement.length; i++) {
            sum_gross_profit += this.all_income_statement[i].gross_profit;
        }
        this.gross_profit = sum_gross_profit;
        consoleLog(`Gross profit for ${this.ticker.symbol_code} is ${sum_gross_profit}`, this.createGrossProfit.name, LogLevel.DEBUG);
    }

    async createPriceToCashFlow() {
        const market_capitalization = this.ticker.market_capitalization || 0;
        const end_period_cash_flow = this.cash_flow ? this.cash_flow?.end_period_cash_flow : 0;
        consoleLog(`Market capitalization for ${this.ticker.symbol_code} is ${market_capitalization}`, this.createPriceToCashFlow.name, LogLevel.DEBUG);
        consoleLog(`End period cash flow for ${this.ticker.symbol_code} is ${end_period_cash_flow}`, this.createPriceToCashFlow.name, LogLevel.DEBUG);
        if (end_period_cash_flow !== 0) {
            this.ticker.price_to_cash_flow = market_capitalization / end_period_cash_flow;
        }
        consoleLog(`Price to cash flow for ${this.ticker.symbol_code} is ${this.ticker.price_to_cash_flow}`, this.createPriceToCashFlow.name, LogLevel.DEBUG);
    }
}

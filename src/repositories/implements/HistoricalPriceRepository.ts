import {HistoricalPrices} from "../../entities/consolidated_data/HistoricalPrices";
import {ListOfTickers} from "../../entities/consolidated_data/ListOfTickers";
import {ApiEOD} from "../../providers/implements/ApiEOD";
import {addLogJobExecution} from "@/lib/agenda";
import {LogLevel} from "@/utils/types/logs/log";
import {consoleLog} from "@/lib/utils";
import _ from "lodash";

interface PriceHistoryData {
    date: string;
    open: number;
    high: number;
    low: number;
    close: number;
    adjusted_close: number;
    volume: number;
}

export class HistoricalPriceRepository {
    private api: ApiEOD;

    constructor() {
        this.api = new ApiEOD();
    }

    /**
     * Gets price history for a ticker and saves new data to database
     * Implements delta logic - only saves dates that don't already exist
     * @param ticker - The ticker to process
     */
    async getPriceHistoryAndSave(ticker: ListOfTickers): Promise<void> {
        try {
            if (!ticker.primary_ticker_eodhd || !ticker.id) {
                throw new Error(`Invalid ticker data: missing primary_ticker_eodhd or id`);
            }

            let fromDate: string | undefined;
            let toDate: string | undefined;
            const latestPriceDate = await this.getLatestPriceDate(ticker.id);
            if (latestPriceDate) {
                // Delta mode: get data from the day after the latest existing date
                const latestDate = new Date(latestPriceDate);
                latestDate.setDate(latestDate.getDate() + 1);
                fromDate = latestDate.toISOString().split("T")[0];
                toDate = new Date().toISOString().split("T")[0];

                consoleLog(`Delta mode for ${ticker.primary_ticker_eodhd}: from ${fromDate} to ${toDate}`, "getPriceHistoryAndSave", LogLevel.DEBUG);
            } else {
                // First time: get all available history (no from/to dates)
                consoleLog(`First time mode for ${ticker.primary_ticker_eodhd}: getting all available history`, "getPriceHistoryAndSave", LogLevel.DEBUG);
            }

            // Fetch price history from API
            const response = await this.api.findPriceHistory(ticker.primary_ticker_eodhd, "d", fromDate, toDate);

            if (!response || !Array.isArray(response)) {
                addLogJobExecution(LogLevel.WARNING, "getPriceHistoryAndSave", "No price history data received", {
                    ticker: ticker.primary_ticker_eodhd,
                    tickerId: ticker.id,
                });
                return;
            }

            const priceHistoryData: PriceHistoryData[] = response;

            if (priceHistoryData.length === 0) {
                addLogJobExecution(LogLevel.INFO, "getPriceHistoryAndSave", "No new price history data to save", {
                    ticker: ticker.primary_ticker_eodhd,
                    tickerId: ticker.id,
                });
                return;
            }

            // Prepare data for bulk insert
            const historicalPricesToSave = priceHistoryData.map((data) => ({
                date: new Date(data.date),
                open: data.open,
                high: data.high,
                low: data.low,
                close: data.close,
                adjusted_close: data.adjusted_close,
                volume: data.volume,
                ticker_internal_id: ticker.id!,
            }));

            // Save to database using bulk create
            await HistoricalPrices.bulkCreate(historicalPricesToSave);

            addLogJobExecution(LogLevel.INFO, "getPriceHistoryAndSave", "Successfully saved price history data", {
                ticker: ticker.primary_ticker_eodhd,
                tickerId: ticker.id,
                savedCount: historicalPricesToSave.length,
                totalReceived: priceHistoryData.length,
                fromDate,
                toDate,
            });

            consoleLog(`Saved ${historicalPricesToSave.length} price history records for ${ticker.primary_ticker_eodhd}`, "getPriceHistoryAndSave", LogLevel.DEBUG);
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            addLogJobExecution(LogLevel.ERROR, this.getPriceHistoryAndSave.name, "Error processing price history", {
                ticker: ticker.primary_ticker_eodhd,
                tickerId: ticker.id,
                error: errorMessage,
            });
            throw error;
        }
    }

    /**
     * Gets the latest date for a ticker's historical prices
     * @param tickerId - The ticker internal ID
     * @returns The latest date or null if no data exists
     */
    async getLatestPriceDate(tickerId: number): Promise<Date | null> {
        try {
            const latestPrice = await HistoricalPrices.findOne({
                where: {
                    ticker_internal_id: tickerId,
                },
                order: [["date", "DESC"]],
                attributes: ["date"],
            });

            return latestPrice ? latestPrice.date : null;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            addLogJobExecution(LogLevel.ERROR, this.getLatestPriceDate.name, "Error getting latest price date", {
                tickerId,
                error: errorMessage,
            });
            throw error;
        }
    }
}

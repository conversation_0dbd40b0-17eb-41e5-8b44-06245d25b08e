//https://blog.logrocket.com/job-scheduling-node-js-agenda-js/
//https://betterstack.com/community/guides/scaling-nodejs/node-scheduled-tasks/
//https://hokify.github.io/agenda/agenda/6.x/index.html
//https://github.com/hokify/agenda
//https://github.com/matthewmueller/date#examples
//https://date.js.org/

import Agenda from "agenda";
import dotenv from "dotenv";
import {ObjectId} from "mongodb";
import {LogLevel} from "@/utils/types/logs/log";

dotenv.config();

// Singleton instance
let agendaInstance: Agenda | null = null;
let isConnected = false;

// Get or create the Agenda instance
export const getAgenda = (): Agenda => {
    if (!agendaInstance) {
        const mongoConnectionString = process.env.MONGODB_URI || "mongodb://localhost:27017/agenda";
        agendaInstance = new Agenda({
            db: {
                address: mongoConnectionString,
                collection: "agendaJobs",
            },
            processEvery: "1 minute",
            maxConcurrency: 1,
            defaultConcurrency: 1,
            defaultLockLimit: 1,
            lockLimit: 1,
            defaultLockLifetime: 120 * 60 * 1000, // 120 minutes in milliseconds
        });

        // Set up error handling
        agendaInstance.on("error", (err) => {
            console.error("Agenda MongoDB connection error:", err);
            isConnected = false;
        });
    }

    return agendaInstance;
};

// Check if Agenda is connected
export const isAgendaConnected = (): boolean => {
    return isConnected;
};

// Connect to MongoDB
export const connectAgendaToMongoDB = async (): Promise<boolean> => {
    if (isConnected) {
        return true;
    }

    try {
        const agenda = getAgenda();

        // Force connection
        await agenda._ready;

        // Verify connection with a simple operation
        const db = agenda._mdb;
        const collection = db.collection("agendaJobs");
        await collection.find({}).limit(1).toArray();

        isConnected = true;
        console.log("MongoDB connection established successfully");
        return true;
    } catch (error) {
        console.error("Failed to connect Agenda to MongoDB:", error);
        isConnected = false;
        return false;
    }
};

// Define a job
export const defineJob = (jobName: string, handler: (job: any) => Promise<void>): void => {
    const agenda = getAgenda();
    agenda.define(jobName, handler);
};

// Check if a job is defined
export const isJobDefined = (jobName: string): boolean => {
    const agenda = getAgenda();
    return !!agenda._definitions && !!agenda._definitions[jobName];
};

// Get all defined job names
export const getDefinedJobNames = (): string[] => {
    const agenda = getAgenda();
    if (!agenda._definitions) {
        return [];
    }
    return Object.keys(agenda._definitions);
};

export const agendaNow = async (jobName: string): Promise<void> => {
    const agenda = getAgenda();

    // Check if job is defined before running
    if (!isJobDefined(jobName)) {
        throw new Error(`Undefined job: "${jobName}" is not defined in the system`);
    }

    agenda.now(jobName, {});
};

// Schedule a job
export const scheduleJob = async (jobName: string, data: any, when?: Date | string): Promise<string> => {
    const agenda = getAgenda();

    // Ensure connection
    if (!isConnected) {
        await connectAgendaToMongoDB();
    }

    // Check if job is defined before scheduling
    if (!isJobDefined(jobName)) {
        throw new Error(`Undefined job: "${jobName}" is not defined in the system`);
    }

    let job;
    if (when) {
        job = await agenda.schedule(when, jobName, data);
    } else {
        job = await agenda.now(jobName, data);
    }

    return job.attrs._id.toString();
};

// Get jobs by name
export const getJobsByName = async (jobName: string): Promise<any[]> => {
    const agenda = getAgenda();

    // Ensure connection
    if (!isConnected) {
        await connectAgendaToMongoDB();
    }

    return agenda.jobs({name: jobName});
};

// Cache for log level to avoid repeated database calls
let cachedLogLevel: LogLevel | null = null;

// Helper function to get current log level
const getCurrentLogLevel = async (): Promise<LogLevel> => {
    if (cachedLogLevel === null) {
        try {
            const logLevel = await getLogLevel();
            cachedLogLevel = logLevel;
        } catch (error) {
            console.error("Error getting log level, using default:", error);
            cachedLogLevel = LogLevel.WARNING; // Default fallback
        }
    }
    return cachedLogLevel;
};

// Log levels hierarchy: ERROR (0) < WARNING (1) < INFO (2) < DEBUG (3)
const LOG_LEVELS = {
    ERROR: 0,
    WARNING: 1,
    INFO: 2,
    DEBUG: 3,
} as const;

type LogLevelPriority = keyof typeof LOG_LEVELS;

export const addLogJobExecution = async (logLevel: LogLevel, functionName: string, message: string, data?: any, ticker_internal_id?: number): Promise<void> => {
    const currentLogLevel = await getCurrentLogLevel();
    const currentLevelValue = LOG_LEVELS[currentLogLevel as LogLevelPriority] || LOG_LEVELS.INFO;
    const messageLevelValue = LOG_LEVELS[logLevel];

    // Only log if the message level is at or above the current log level
    if (messageLevelValue <= currentLevelValue) {
        const agenda = getAgenda();
        const timestamp = new Date();
        const db = agenda._mdb;
        const collection = db.collection("jobLogs");
        await collection.insertOne({
            timestamp,
            logLevel,
            functionName,
            message,
            data,
            ticker_internal_id,
        });
    }
};

// Close connection
export const closeAgendaConnection = async (): Promise<void> => {
    if (agendaInstance) {
        await agendaInstance.stop();
        isConnected = false;
    }
};

// Get the settings collection
const getAgendaSettingsCollection = () => {
    const agenda = getAgenda();
    const db = agenda._mdb;
    return db.collection("agendaSettings");
};

export const getOrCreateAgendaSettings = async () => {
    try {
        const settingsId = new ObjectId("00000001c1c580362ac42646"); //Id is hardcoded, let it like that, does not really matter for this project
        const settingsCollection = getAgendaSettingsCollection();
        // Check if settings document exists
        let existingSettings = await settingsCollection.findOne({_id: settingsId});
        if (existingSettings) {
            return existingSettings;
        }
        // Create new document
        await settingsCollection.insertOne({
            _id: settingsId,
            agendaStartDateTime: new Date(),
            checkAgendaStatusInterval: 30, // Default value in minutes
            priceBatchSize: 30, // Default batch size for price API calls
            priceChunkSize: 1000, // Default chunk size for database operations
            dividendsBatchSize: 10, // Default batch size for dividends API calls
            fundamentalDataBatchSize: 30, // Default batch size for fundamental data API calls
            processS3FilesBatchSize: 10, // Default batch size for S3 files processing
            splitsBatchSize: 10, // Default batch size for splits API calls
            epsBatchSize: 5, // Default batch size for EPS processing
            logLevel: LogLevel.WARNING, // Default log level
            jobLogsRetentionDays: 30, // Default number of days to retain job logs
        });
        existingSettings = await settingsCollection.findOne({_id: settingsId});
        console.log("Created agendaSettings with start time");
        return existingSettings;
    } catch (error) {
        console.error("Error updating agendaSettings collection:", error);
    }
};

export const updateAgendaSettings = async (settings: any) => {
    try {
        const settingsId = new ObjectId("00000001c1c580362ac42646");
        const settingsCollection = getAgendaSettingsCollection();
        // Get existing settings
        const existingSettings = await settingsCollection.findOne({_id: settingsId});
        if (!existingSettings) {
            return await getOrCreateAgendaSettings();
        }
        // Update settings
        const updatedSettings = {...existingSettings, ...settings};
        await settingsCollection.replaceOne({_id: settingsId}, updatedSettings);
        return updatedSettings;
    } catch (error) {
        console.error("Error updating agendaSettings:", error);
        throw error;
    }
};

// Get price batch size from settings
export const getPriceBatchSize = async (): Promise<number> => {
    try {
        const settings = await getOrCreateAgendaSettings();
        return settings?.priceBatchSize || 30; // Default to 30 if not found
    } catch (error) {
        console.error("Error getting price batch size:", error);
        return 30; // Default fallback
    }
};

// Get price chunk size from settings
export const getPriceChunkSize = async (): Promise<number> => {
    try {
        const settings = await getOrCreateAgendaSettings();
        return settings?.priceChunkSize || 1000; // Default to 1000 if not found
    } catch (error) {
        console.error("Error getting price chunk size:", error);
        return 1000; // Default fallback
    }
};

// Get dividends batch size from settings
export const getDividendsBatchSize = async (): Promise<number> => {
    try {
        const settings = await getOrCreateAgendaSettings();
        return settings?.dividendsBatchSize || 10; // Default to 10 if not found
    } catch (error) {
        console.error("Error getting dividends batch size:", error);
        return 10; // Default fallback
    }
};

// Get fundamental data batch size from settings
export const getFundamentalDataBatchSize = async (): Promise<number> => {
    try {
        const settings = await getOrCreateAgendaSettings();
        return settings?.fundamentalDataBatchSize || 30; // Default to 10 if not found
    } catch (error) {
        console.error("Error getting fundamental data batch size:", error);
        return 30; // Default fallback
    }
};

// Get S3 files processing batch size from settings
export const getProcessS3FilesBatchSize = async (): Promise<number> => {
    try {
        const settings = await getOrCreateAgendaSettings();
        return settings?.processS3FilesBatchSize || 10; // Default to 10 if not found
    } catch (error) {
        console.error("Error getting S3 files processing batch size:", error);
        return 10; // Default fallback
    }
};

// Get splits batch size from settings
export const getSplitsBatchSize = async (): Promise<number> => {
    try {
        const settings = await getOrCreateAgendaSettings();
        return settings?.splitsBatchSize || 10; // Default to 10 if not found
    } catch (error) {
        console.error("Error getting splits batch size:", error);
        return 10; // Default fallback
    }
};

// Get EPS batch size from settings
export const getEpsBatchSize = async (): Promise<number> => {
    try {
        const settings = await getOrCreateAgendaSettings();
        return settings?.epsBatchSize || 5; // Default to 5 if not found
    } catch (error) {
        console.error("Error getting EPS batch size:", error);
        return 5; // Default fallback
    }
};

export const getJobLogsRetentionDays = async (): Promise<number> => {
    try {
        const settings = await getOrCreateAgendaSettings();
        return settings?.jobLogsRetentionDays || 30; // Default to 30 days if not set
    } catch (error) {
        console.error("Error getting job logs retention days:", error);
        return 30; // Default fallback
    }
};

export const getLogLevel = async (): Promise<LogLevel> => {
    try {
        const settings = await getOrCreateAgendaSettings();
        return settings?.logLevel || LogLevel.WARNING; // Default to WARNING if not found
    } catch (error) {
        console.error("Error getting log level:", error);
        return LogLevel.WARNING; // Default fallback
    }
};

// Create or update agendaSettings collection with start time
const updateAgendaStartTime = async () => {
    try {
        // Check if settings document exists
        const existingSettings = await getOrCreateAgendaSettings();
        if (existingSettings) {
            // Update existing document
            existingSettings.agendaStartDateTime = new Date();
            await getAgendaSettingsCollection().replaceOne({_id: existingSettings._id}, existingSettings);
            console.log("Updated agendaSettings with new start time");
        }
    } catch (error) {
        console.error("Error updating agendaSettings collection:", error);
    }
};

export const getAgendaStartTime = async (): Promise<Date | null> => {
    try {
        const settings = await getOrCreateAgendaSettings();
        return settings?.agendaStartDateTime || null;
    } catch (error) {
        console.error("Error fetching agenda start time:", error);
        return null;
    }
};

// Start Agenda
export const startAgenda = async () => {
    try {
        // Start processing jobs
        console.log("Starting Agenda...");
        const agenda = getAgenda();

        // Ensure MongoDB connection is established first
        isConnected = await connectAgendaToMongoDB();
        console.log("Agenda MongoDB connection is ready");

        await updateAgendaStartTime();

        // Initialize job definitions from the new jobDefinitions module
        try {
            const {initializeJobs} = await import("./jobDefinitions");
            await initializeJobs();
            console.log("Job definitions initialized successfully");
        } catch (jobError) {
            console.error("Error initializing job definitions:", jobError);
            // Continue with Agenda startup even if job definitions fail
        }

        await agenda.start();
        console.log("Agenda scheduler started successfully");

        return true;
    } catch (error) {
        console.error("Failed to start Agenda:", error);
        return false;
    }
};

// Job definitions have been moved to src/lib/jobDefinitions.ts

// Default export
export default getAgenda();

import NextAuth, { Account, Profile, Session, User } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import google from 'next-auth/providers/google';

const is_prod = process.env.AUTH_TRUST_HOST === 'true'

interface StrapiUser extends User {
  id: string,
  username: string,
  email: string,
  provider: string,
  confirmed: boolean,
  blocked: boolean,
  createdAt: Date,
  updatedAt: Date,
  phone?: string,
  stripeCustomerId?: string,
  language?: string,
  firstname: string,
  lastname: string,
  is_signedup: boolean,
  city?: string,
  country?: string
}

interface TibaJwt {
  name?: string | null
  email?: string | null
  picture?: string | null
  sub?: string
  iat?: number
  exp?: number
  jti?: string,
  strapiToken: string
  strapiUserId: string
  provider: string
  blocked: boolean
  user: StrapiUser
}

interface TibaSession extends Session {
  user: StrapiUser
  access_token: string
  provider: string
  expires: string
}

export const {
  handlers: { GET, POST },
  auth,
  signIn,
  signOut
} = NextAuth(
  {
    providers: [
      google({
        clientId: process.env.AUTH_GOOGLE_ID ?? '',
        clientSecret: process.env.AUTH_GOOGLE_SECRET ?? '',
      }),
      CredentialsProvider({
        name: 'email and password',
        credentials: {
          identifier: {
            label: 'Email *',
            type: 'text',
          },
          password: { label: 'Password *', type: 'password' },
        },
        async authorize(credentials) {
          // make sure the are credentials
          if (!credentials || !credentials.identifier || !credentials.password) {
            return null;
          }
          
            const res = await fetch(
              `${process.env.API_URL}/api/auth/local`,
              {
                method: 'POST',
                headers: {
                  'Content-type': 'application/json',
                },
                body: JSON.stringify({
                  identifier: credentials!.identifier,
                  password: credentials!.password,
                }),
              }
            );

            if (!res.ok) {
              throw new Error( JSON.stringify({ errors: 'Teste', status: false }))
              return null
            }
  
            // success
            const data = await res.json();

            return {
              ...data,
              user: {...data.user},
              strapiToken: data.jwt,
            };
        },
      }),
    ],
    callbacks: {
      async signIn({ user, account, profile }) {
        // console.log('singIn callback', { account, profile, user });
        try {
          if (
            account &&
            account.provider === 'google' &&
            profile &&
            'email_verified' in profile
          ) {
            if (!profile.email_verified) return false;
          }
  
          return true;
          
        } catch(error) {
          throw error
        }
      }, //@ts-ignore
      async jwt(
        { token, trigger, account, user, session }: {
          token: TibaJwt;
          user: any;
          account: Account | null;
          profile?: Profile;
          trigger?: "signIn" | "signUp" | "update";
          isNewUser?: boolean;
          session?: any;
        }) {
        // console.log('jwt callback', {
        //   token,
        //   trigger,
        //   account,
        //   user,
        //   session,
        // });
  
        // change username update
        if (trigger === 'update' ) {
          return { ...token, ...session.user, user: {...session.user} }
        }
  
  
        if (account) {
          if (account.provider === 'google') {
            // we now know we are doing a sign in using GoogleProvider
            try {

              const res = await fetch(`${process.env.API_URL}/api/social-login`, {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({
                    provider: account.provider,
                    access_token: account.access_token,
                  }),
                });
                          

              if (!res.ok) {
    
                const contentType = res.headers.get('content-type');
                
                if (contentType === 'application/json; charset=utf-8') {
        
                    const data = await res.json();
    
                    throw new Error(data.error.message)
        
                } else {
                    throw new Error(res.statusText)
                }
              }

              const strapiLoginResponse = await res.json();
              
              const { jwt, user } = strapiLoginResponse

              const response =  await fetch(`${process.env.API_URL}/api/users/me`, {
                method: 'GET',
                cache: 'no-cache',
                headers: {
                    'Authorization': `Bearer ${jwt}`,
                    'Content-Type': 'application/json'
                }
              })
            
              const data = await response.json()

              // customize token
              // name and email will already be on here
              token.strapiToken = jwt;
              token.strapiUserId = user.id;
              token.provider = account.provider;
              token.blocked = user.blocked;
              token.user = { ...user, ...data }

            } catch (error) {
              throw error;
            }
          }

          if (account.provider === 'credentials') {
            try {
              // for credentials, not google provider
              // name and email are taken care of by next-auth or authorize
              token.strapiToken = user.strapiToken;
              token.strapiUserId = user.strapiUserId;
              token.provider = account.provider;
              token.blocked = user.blocked;
              token.user = user.user
            } catch(e) {
              throw e
            }
          }
        }
        return token;
      },//@ts-ignore
      async session({ token, session }: { token: TibaJwt, session: TibaSession }) {
       
        //@ts-ignore
        session.user = {
          id: token.user.id,
          firstname: token.user.firstname,
          email: token.user.email,
          language: token.user.language
        }

        session.access_token = token.strapiToken
  
        return session;
      },
    },
    session: {
      strategy: 'jwt',
    },
    secret: process.env.NEXTAUTH_SECRET,
    trustHost: is_prod,
    pages: {
      signIn: '/signin',
      // error: '/authError',
    },
  },
)
{"name": "tiba-invest-frontend-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.20", "@mui/x-date-pickers": "^6.18.3", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.2", "@react-pdf/renderer": "^3.4.4", "@stitches/react": "^1.2.8", "@syncfusion/ej2-react-buttons": "^27.1.53", "@syncfusion/ej2-react-calendars": "^27.1.57", "@syncfusion/ej2-react-dropdowns": "^26.2.11", "@syncfusion/ej2-react-grids": "^26.2.11", "@syncfusion/ej2-react-inputs": "^26.2.11", "@syncfusion/ej2-react-lists": "^26.2.11", "@syncfusion/ej2-react-navigations": "^26.2.12", "@syncfusion/ej2-react-popups": "^27.1.56", "@syncfusion/ej2-react-querybuilder": "^27.1.55", "apexcharts": "^3.51.0", "axios": "^1.7.2", "chart.js": "^4.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "export-to-csv": "^1.3.0", "i18next": "^23.7.7", "lodash": "^4.17.21", "lucide-react": "^0.506.0", "material-react-table": "^2.0.5", "moment": "^2.30.1", "next": "^14.2.3", "next-auth": "^5.0.0-beta.18", "next-i18next": "^15.0.0", "ni18n": "^1.1.0", "react": "^18", "react-apexcharts": "^1.4.1", "react-chartjs-2": "^5.2.0", "react-cookie": "^7.2.2", "react-dom": "^18", "react-i18next": "^13.5.0", "react-markdown": "^9.0.1", "react-toastify": "^10.0.5", "react-ts-tradingview-widgets": "^1.2.8", "recharts": "^2.15.3", "remark-gfm": "^4.0.0", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@faker-js/faker": "^8.3.1", "@types/axios": "^0.14.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.16", "eslint": "^8", "eslint-config-next": "14.0.3", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5"}}
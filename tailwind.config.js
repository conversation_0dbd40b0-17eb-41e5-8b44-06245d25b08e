/** @type {import('tailwindcss').Config} */
module.exports = {
    darkMode: ["class"],
    content: ["./src/**/*.{js,ts,jsx,tsx,mdx}"],
  theme: {
  	extend: {
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		colors: {
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			}
  		},
  		keyframes: {
  			'accordion-down': {
  				from: {
  					height: '0'
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: '0'
  				}
  			}
  		},
  		animation: {
  			'accordion-down': 'accordion-down 0.2s ease-out',
  			'accordion-up': 'accordion-up 0.2s ease-out'
  		}
  	},
  	colors: {
  		primary: '#13DCD2',
  		primaryShadow: '#25A9AD',
  		lightPrimary: '#d3f5f3',
  		primaryShadowAlpha: '#25A9AD50',
  		text: '#718EBF',
  		purpleText: '#5D586B',
  		gray: '#908D99',
  		lightBg: '#F8F8F8',
  		white: '#ffffff',
  		secondary: '#e86c30',
  		error: '#FF82AC',
  		errorLight: '#FFE0EB',
  		warning: '#ffbb38',
  		warningLight: '#FFF5D9',
  		info: '#1F82DE',
  		success: '#66bb6a',
  		inactiveStage: '#0E062275',
  		enabled: '#28D0C8',
  		greenTransparent: '#4BBC3328',
  		green: '#4BBC33',
  		lightGray: '#C2C2C2',
  		cardBorder: 'rgba(201, 208, 208, 0.04)',
  		planBorder: 'rgba(82, 160, 193, 0.2)',
  		labelColor: 'rgba(14, 6, 34, 0.46)',
  		indicator: 'rgba(14, 6, 34, 0.67)',
  		lightwhite: '#EEEEEE',
  		lightshadow: 'rgba(133, 153, 171, 0.08)',
  		tableborder: '#E4E8EC',
  		blueborder: '#52A0C1',
  		contrastText: '#000',
  		light: '#CCCCCC',
  		lightShadow: '#8599AB',
  		whiteText: '#06346b',
  		facebook: '#2157A2',
  		google: '#D34A48',
  		yellow: '#F8CD1A',
  		black: '#000000',
  		dark: '#192438',
  		secondaryGray: '#4C4C4C',
  		lightgrey: '#EAEAEA',
  		shadow: 'rgba(0,0,0,.6)',
  		colorsLightshadow: 'rgba(112,112,112,.06)',
  		sfPurple: '#718EBF',
  		sfDarkPurple: '#1A0C41'
  	}
  },
  plugins: [require("tailwindcss-animate")],
};

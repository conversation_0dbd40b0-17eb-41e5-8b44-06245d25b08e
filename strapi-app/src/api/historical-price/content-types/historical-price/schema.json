{"kind": "collectionType", "collectionName": "historical_prices", "info": {"singularName": "historical-price", "pluralName": "historical-prices", "displayName": "Historical Prices"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"ticker_internal": {"type": "relation", "relation": "oneToOne", "target": "api::list-of-ticker.list-of-ticker", "useJoinTable": false, "column": {"notNullable": true}}, "date": {"type": "date", "required": true}, "open": {"type": "float"}, "high": {"type": "float"}, "low": {"type": "float"}, "close": {"type": "float"}, "adjusted_close": {"type": "float"}, "volume": {"type": "float"}}}
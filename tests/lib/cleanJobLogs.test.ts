import {_cleanJobLogs} from "../../src/lib/jobDefinitions";
import {getJobLogsRetentionDays} from "../../src/lib/agenda";
import {beforeEach, describe, expect, it, jest} from "@jest/globals";

// Mock the dependencies
jest.mock("../../src/lib/agenda", () => ({
    getJobLogsRetentionDays: jest.fn(),
    getAgenda: jest.fn(),
}));

// Type the mocked functions
const mockedGetJobLogsRetentionDays = getJobLogsRetentionDays as jest.MockedFunction<typeof getJobLogsRetentionDays>;

describe("_cleanJobLogs - Unit Tests", () => {
    let mockCollection: any;
    let mockDb: any;
    let mockAgenda: any;

    beforeEach(() => {
        jest.clearAllMocks();

        // Mock collection methods
        mockCollection = {
            deleteMany: jest.fn(),
        };

        // Mock database
        mockDb = {
            collection: jest.fn().mockReturnValue(mockCollection),
        };
        0;

        // Mock agenda
        mockAgenda = {
            _mdb: mockDb,
        };

        // Mock getAgenda to return our mock agenda
        const {getAgenda} = require("../../src/lib/agenda");
        (getAgenda as jest.Mock).mockReturnValue(mockAgenda);
    });

    it("should delete job logs older than retention period", async () => {
        // Arrange
        const retentionDays = 30;
        const deletedCount = 5;

        mockedGetJobLogsRetentionDays.mockResolvedValue(retentionDays);
        mockCollection.deleteMany.mockResolvedValue({deletedCount});

        // Act
        await _cleanJobLogs();

        // Assert
        expect(mockedGetJobLogsRetentionDays).toHaveBeenCalledTimes(1);
        expect(mockDb.collection).toHaveBeenCalledWith("jobLogs");
        expect(mockCollection.deleteMany).toHaveBeenCalledTimes(1);

        // Check that deleteMany was called with correct date filter
        const deleteCall = mockCollection.deleteMany.mock.calls[0][0];
        expect(deleteCall).toHaveProperty("timestamp");
        expect(deleteCall.timestamp).toHaveProperty("$lt");

        // Verify the cutoff date is approximately 30 days ago
        const cutoffDate = deleteCall.timestamp.$lt;
        const expectedCutoff = new Date();
        expectedCutoff.setDate(expectedCutoff.getDate() - retentionDays);

        // Allow for small time differences (within 1 minute)
        const timeDiff = Math.abs(cutoffDate.getTime() - expectedCutoff.getTime());
        expect(timeDiff).toBeLessThan(60000); // Less than 1 minute
    });

    it("should handle errors gracefully", async () => {
        // Arrange
        const errorMessage = "Database connection failed";
        mockedGetJobLogsRetentionDays.mockRejectedValue(new Error(errorMessage));

        // Act & Assert
        await expect(_cleanJobLogs()).rejects.toThrow(errorMessage);
    });

    it("should use default retention days when getJobLogsRetentionDays fails", async () => {
        // Arrange
        mockedGetJobLogsRetentionDays.mockRejectedValue(new Error("Settings not found"));
        mockCollection.deleteMany.mockResolvedValue({deletedCount: 0});

        // Act & Assert
        await expect(_cleanJobLogs()).rejects.toThrow("Settings not found");
    });

    it("should log the number of deleted records", async () => {
        // Arrange
        const retentionDays = 15;
        const deletedCount = 10;
        const consoleSpy = jest.spyOn(console, "log").mockImplementation(() => {});

        mockedGetJobLogsRetentionDays.mockResolvedValue(retentionDays);
        mockCollection.deleteMany.mockResolvedValue({deletedCount});

        // Act
        await _cleanJobLogs();

        // Assert
        expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining(`Cleaned up ${deletedCount} job logs older than ${retentionDays} days`));

        consoleSpy.mockRestore();
    });
});

/*import {getFundamentalDataController} from "../../src/controller/fundamentalDataController";
import {getFundamentalDataAPI_EOD} from "../../src/controller/tickersController";
import {ListOfTickers} from "../../src/entities/consolidated_data/ListOfTickers";
import {beforeEach, describe, expect, it, jest} from "@jest/globals";

// Mock the dependencies
jest.mock("../../src/controller/tickersController", () => ({
    getFundamentalDataAPI_EOD: jest.fn(),
}));
jest.mock("../../src/lib/agenda", () => ({
    addLogJobExecution: jest.fn().mockResolvedValue("mock-log-id"),
    getFundamentalDataBatchSize: jest.fn().mockResolvedValue(10),
    LogLevel: {
        INFO: "INFO",
        ERROR: "ERROR",
        WARNING: "WARNING",
    },
}));
jest.mock("../../src/lib/jobDefinitions", () => ({
    initializeJobProgress: jest.fn().mockResolvedValue(undefined),
    updateJobProgress: jest.fn().mockResolvedValue(undefined),
}));
jest.mock("../../src/repositories/implements/FundamentaDataRepository", () => ({
    FundamentalDataRepository: jest.fn().mockImplementation(() => ({
        create: jest.fn().mockResolvedValue("mock-filename.json"),
    })),
}));
jest.mock("../../src/controller/logsController", () => ({
    LogsController: jest.fn().mockImplementation(() => ({
        tickerUpdatedEODHD: jest.fn().mockResolvedValue(undefined),
    })),
}));
jest.mock("../../src/entities/consolidated_data/StatisticsOfTickers", () => ({
    StatisticsOfTicker: {
        destroy: jest.fn().mockResolvedValue(undefined),
    },
}));

const mockGetFundamentalDataAPI_EOD = getFundamentalDataAPI_EOD as jest.MockedFunction<typeof getFundamentalDataAPI_EOD>;

describe("getFundamentalDataController - Unit Tests", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    const createMockTicker = (id: number, primary_ticker_eodhd: string): Partial<ListOfTickers> => ({
        id,
        primary_ticker_eodhd,
        is_enable: 1,
        save: jest.fn().mockResolvedValue(undefined),
        update: jest.fn().mockResolvedValue(undefined),
    });

    it("should process empty ticker list without errors", async () => {
        const tickers: ListOfTickers[] = [];

        await expect(getFundamentalDataController(tickers)).resolves.not.toThrow();
    });

    it("should process single ticker successfully", async () => {
        const mockTicker = createMockTicker(1, "AAPL.US") as ListOfTickers;
        const tickers = [mockTicker];

        mockGetFundamentalDataAPI_EOD.mockResolvedValue({
            ticker_internal_id: 1,
            General: {Name: "Apple Inc."},
        });

        await expect(getFundamentalDataController(tickers)).resolves.not.toThrow();
        expect(mockGetFundamentalDataAPI_EOD).toHaveBeenCalledWith("AAPL.US");
    });

    it("should process multiple tickers in batches", async () => {
        const tickers = Array.from({length: 25}, (_, i) => createMockTicker(i + 1, `TICKER${i + 1}.US`) as ListOfTickers);

        mockGetFundamentalDataAPI_EOD.mockResolvedValue({
            ticker_internal_id: 1,
            General: {Name: "Test Company"},
        });

        await expect(getFundamentalDataController(tickers)).resolves.not.toThrow();
        expect(mockGetFundamentalDataAPI_EOD).toHaveBeenCalledTimes(25);
    });

    it("should handle API errors gracefully", async () => {
        const mockTicker = createMockTicker(1, "INVALID.US") as ListOfTickers;
        const tickers = [mockTicker];

        mockGetFundamentalDataAPI_EOD.mockRejectedValue(new Error("Symbol not found"));

        await expect(getFundamentalDataController(tickers)).resolves.not.toThrow();
        expect(mockTicker.save).toHaveBeenCalled();
    });

    it("should skip tickers with undefined id", async () => {
        const validTicker = createMockTicker(1, "AAPL.US") as ListOfTickers;
        const invalidTicker = {
            id: undefined,
            primary_ticker_eodhd: "INVALID.US",
        } as ListOfTickers;
        const tickers = [validTicker, invalidTicker];

        mockGetFundamentalDataAPI_EOD.mockResolvedValue({
            ticker_internal_id: 1,
            General: {Name: "Apple Inc."},
        });

        await expect(getFundamentalDataController(tickers)).resolves.not.toThrow();
        expect(mockGetFundamentalDataAPI_EOD).toHaveBeenCalledTimes(1);
        expect(mockGetFundamentalDataAPI_EOD).toHaveBeenCalledWith("AAPL.US");
    });

    it("should handle concurrent batch processing", async () => {
        // Create enough tickers to trigger concurrent batch processing
        const tickers = Array.from({length: 35}, (_, i) => createMockTicker(i + 1, `TICKER${i + 1}.US`) as ListOfTickers);

        mockGetFundamentalDataAPI_EOD.mockResolvedValue({
            ticker_internal_id: 1,
            General: {Name: "Test Company"},
        });

        const startTime = Date.now();
        await getFundamentalDataController(tickers);
        const duration = Date.now() - startTime;

        // Should complete in reasonable time due to concurrent processing
        expect(duration).toBeLessThan(10000); // 10 seconds max
        expect(mockGetFundamentalDataAPI_EOD).toHaveBeenCalledTimes(35);
    });

    it("should update ticker timestamps on successful processing", async () => {
        const mockTicker = createMockTicker(1, "AAPL.US") as ListOfTickers;
        const tickers = [mockTicker];

        mockGetFundamentalDataAPI_EOD.mockResolvedValue({
            ticker_internal_id: 1,
            General: {Name: "Apple Inc."},
        });

        await getFundamentalDataController(tickers);

        expect(mockTicker.save).toHaveBeenCalled();
        // Verify that timestamps were set (they should be Date objects)
        expect(mockTicker.fundamental_data_last_updated).toBeInstanceOf(Date);
        expect(mockTicker.updated_at).toBeInstanceOf(Date);
    });

    it("should handle progress tracking when historyId is provided", async () => {
        const mockTicker = createMockTicker(1, "AAPL.US") as ListOfTickers;
        const tickers = [mockTicker];
        const historyId = "507f1f77bcf86cd799439011";

        mockGetFundamentalDataAPI_EOD.mockResolvedValue({
            ticker_internal_id: 1,
            General: {Name: "Apple Inc."},
        });

        await expect(getFundamentalDataController(tickers, historyId)).resolves.not.toThrow();
    });
});
*/

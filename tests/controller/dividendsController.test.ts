/*import {getDividendsController, insertDividends} from "../../src/controller/dividendsController";
import {ApiEOD} from "../../src/providers/implements/ApiEOD";
import {ListOfTickers} from "../../src/entities/consolidated_data/ListOfTickers";
import {beforeEach, describe, expect, it, jest} from "@jest/globals";

// Mock the dependencies
jest.mock("../../src/providers/implements/ApiEOD");
jest.mock("../../src/lib/agenda", () => ({
    addLogJobExecution: jest.fn().mockResolvedValue("mock-log-id"),
    getDividendsBatchSize: jest.fn().mockResolvedValue(10),
}));
jest.mock("../../src/lib/jobDefinitions", () => ({
    initializeJobProgress: jest.fn().mockResolvedValue(undefined),
    updateJobProgress: jest.fn().mockResolvedValue(undefined),
}));
// Mock the HistoryDividendsEODRepository
jest.mock("../../src/repositories/implements/HistoryDividendsEODRepository", () => {
    return {
        HistoryDividendsEODRepository: jest.fn().mockImplementation(() => ({
            getSavedDividends: jest.fn().mockResolvedValue(undefined),
            getDividends: jest.fn().mockResolvedValue([]),
            saveDividends: jest.fn().mockResolvedValue(undefined),
        })),
    };
});

describe("getDividendsController - Batch Processing Tests", () => {
    let mockApiEOD: jest.Mocked<ApiEOD>;

    beforeEach(() => {
        jest.clearAllMocks();

        // Setup API mock
        mockApiEOD = new ApiEOD() as jest.Mocked<ApiEOD>;
        mockApiEOD.findDividends = jest.fn();
        (ApiEOD as jest.MockedClass<typeof ApiEOD>).mockImplementation(() => mockApiEOD);
    });

    it("should process empty ticker list without errors", async () => {
        const emptyTickers: ListOfTickers[] = [];

        await expect(getDividendsController(emptyTickers)).resolves.not.toThrow();

        expect(mockApiEOD.findDividends).not.toHaveBeenCalled();
    });

    it("should process single ticker correctly", async () => {
        const mockTickers: ListOfTickers[] = [
            {
                id: 1,
                primary_ticker_eodhd: "AAPL.US",
                symbol_code: "AAPL",
                is_enable: 1,
            } as ListOfTickers,
        ];

        const mockDividendsResponse = [
            {
                date: "2023-11-10",
                dividend: 0.24,
                adjusted_dividend: 0.24,
                currency: "USD",
                declaration_date: "2023-11-02",
                record_date: "2023-11-13",
                payment_date: "2023-11-16",
            },
        ];

        mockApiEOD.findDividends.mockResolvedValue(mockDividendsResponse);

        await getDividendsController(mockTickers);

        expect(mockApiEOD.findDividends).toHaveBeenCalledTimes(1);
        expect(mockApiEOD.findDividends).toHaveBeenCalledWith("AAPL.US");
    });

    it("should process multiple tickers in batches", async () => {
        // Create 25 mock tickers to test batch processing (default batch size is 10)
        const mockTickers: ListOfTickers[] = Array.from({length: 25}, (_, i) => ({
            id: i + 1,
            primary_ticker_eodhd: `TICKER${i + 1}.US`,
            symbol_code: `TICKER${i + 1}`,
            is_enable: 1,
        })) as ListOfTickers[];

        const mockDividendsResponse = [
            {
                date: "2023-11-10",
                dividend: 0.24,
                adjusted_dividend: 0.24,
                currency: "USD",
                declaration_date: "2023-11-02",
                record_date: "2023-11-13",
                payment_date: "2023-11-16",
            },
        ];

        mockApiEOD.findDividends.mockResolvedValue(mockDividendsResponse);

        await getDividendsController(mockTickers);

        // Should call findDividends for each ticker
        expect(mockApiEOD.findDividends).toHaveBeenCalledTimes(25);

        // Verify some specific calls
        expect(mockApiEOD.findDividends).toHaveBeenCalledWith("TICKER1.US");
        expect(mockApiEOD.findDividends).toHaveBeenCalledWith("TICKER25.US");
    });

    it("should handle API errors gracefully and continue processing", async () => {
        const mockTickers: ListOfTickers[] = [
            {
                id: 1,
                primary_ticker_eodhd: "GOOD.US",
                symbol_code: "GOOD",
                is_enable: 1,
            } as ListOfTickers,
            {
                id: 2,
                primary_ticker_eodhd: "BAD.US",
                symbol_code: "BAD",
                is_enable: 1,
            } as ListOfTickers,
            {
                id: 3,
                primary_ticker_eodhd: "GOOD2.US",
                symbol_code: "GOOD2",
                is_enable: 1,
            } as ListOfTickers,
        ];

        const mockDividendsResponse = [
            {
                date: "2023-11-10",
                dividend: 0.24,
                adjusted_dividend: 0.24,
                currency: "USD",
                declaration_date: "2023-11-02",
                record_date: "2023-11-13",
                payment_date: "2023-11-16",
            },
        ];

        // Mock API to fail for the second ticker
        mockApiEOD.findDividends
            .mockResolvedValueOnce(mockDividendsResponse) // GOOD.US succeeds
            .mockRejectedValueOnce(new Error("API Error")) // BAD.US fails
            .mockResolvedValueOnce(mockDividendsResponse); // GOOD2.US succeeds

        await getDividendsController(mockTickers);

        // Should still call findDividends for all tickers
        expect(mockApiEOD.findDividends).toHaveBeenCalledTimes(3);
    });

    it("should handle tickers with no dividends", async () => {
        const mockTickers: ListOfTickers[] = [
            {
                id: 1,
                primary_ticker_eodhd: "NODIV.US",
                symbol_code: "NODIV",
                is_enable: 1,
            } as ListOfTickers,
        ];

        // Mock API to return empty array (no dividends)
        mockApiEOD.findDividends.mockResolvedValue([]);

        await getDividendsController(mockTickers);

        expect(mockApiEOD.findDividends).toHaveBeenCalledTimes(1);
        expect(mockApiEOD.findDividends).toHaveBeenCalledWith("NODIV.US");
    });
});
*/
